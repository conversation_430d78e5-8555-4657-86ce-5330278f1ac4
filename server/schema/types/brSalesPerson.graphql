"""
This type is not relevant to our investment app
"""
type BrSalesPerson {
  id: Int!
  cnpj: String
  hideCommissionSimulatorFlg: Boolean!
  name: String
  legalName: String
  thirdPartyFlg: Boolean!
  salesExperience: String
  referral: String
  brSalesPartnerStatusId: Int
  agreementAwsObjectKey: String
  pendingAgreementAwsObjectKey: String
  signatureRequestedDt: Date
  signatureDt: Date

  # associations
  adminBrContact: BrContact
  brContacts: [BrContact!]!
  brConsumerUnits: [BrConsumerUnit!]!
  brCommissionPayments: [BrCommissionPayment!]!
  brSalesPartnerStatus: BrSalesPartnerStatus

  # calculated
  brConsumerUnitCount: Int!
  pendingAgreementDownloadUrl: String
  agreementDownloadUrl: String

  # system
  createdAt: Date!
  updatedAt: Date!
}

type BrSalesPersonFeed {
  rows: [BrSalesPerson!]!
  count: Int!
}

input UpdateBrSalesPersonInput {
  id: Int!
  adminBrContactId: Int
  brPowerPlanIds: [Int!]
  cnpj: String
  hideCommissionSimulatorFlg: Boolean
  name: String
  legalName: String
  thirdPartyFlg: Boolean
  salesExperience: String
  referral: String
  brSalesPartnerStatusId: Int
  agreementAwsObjectKey: String
  pendingAgreementAwsObjectKey: String
  signatureRequestedDt: Date
  signatureDt: Date

  sendUnsignedPartnershipAgreementEmail: Boolean
  sendStageChangeEmail: Boolean
}

input GetPartnershipAgreementOfflineInput {
  id: Int!
  secretKey: String!
}

input CreateBrSalesPersonInput {
  adminBrContactId: Int
  brPowerPlanIds: [Int!]
  cnpj: String
  hideCommissionSimulatorFlg: Boolean
  name: String
  legalName: String
  thirdPartyFlg: Boolean!
  salesExperience: String
  referral: String
  brSalesPartnerStatusId: Int
  agreementAwsObjectKey: String
  pendingAgreementAwsObjectKey: String
  signatureRequestedDt: Date
  signatureDt: Date
}

type BrSellerObject {
  id: String!
  name: String!
  soldConsumptionKWh: Float!
}

type MonthlyBrSaleVolumeByPartner {
  id: String!
  month: String!
  sellers: [BrSellerObject!]!
}

type BrStateObject {
  id: String!
  name: String!
  soldConsumptionKWh: Float!
}

type DailyBrSaleVolumeByState {
  id: String!
  date: String!
  states: [BrStateObject!]!
}

input SelfRegisterSalesPartnerInput {
  cnpj: String
  businessName: String
  businessLegalName: String
  cpf: String!
  contactFirstName: String!
  contactLastName: String!
  phone: String!
  email: String!
  password: String!
  jobTitle: String
  salesExperience: String
  referral: String
  operatingRegions: [String!]!
  photoIdCloudinaryPublicId: String
  photoIdFileName: String
  address1: String!
  address2: String
  district: String!
  city: String!
  state: String!
  postalCode: String!

  commissionBankName: String!
  commissionBankAccountNumber: String!
  commissionBankAgency: String!
  commissionBankAccountTaxNumber: String!
  commissionPix: String
}

input BrSalesPersonFilter {
  salesPartnerName: String
  salesPersonName: String
  brSalesPartnerStatus: Entity
}

input CreatePartnershipAgreementFormInput {
  brSalesPersonId: Int!
  signature: String
  ipAddress: String
  """
  Optional parameter used to allow customers to sign the agreement on energea.com.br without signing in
  """
  secretKey: String
}

type Query {
  getBrSalesPerson(id: Int!): BrSalesPerson
  brSalesPersonFeed(
    pagination: Pagination
    sort: Sort
    filter: BrSalesPersonFilter
  ): BrSalesPersonFeed
  allBrSalesPersons: [BrSalesPerson!]!
  monthlyBrSaleVolumeByPartner: [MonthlyBrSaleVolumeByPartner!]!
  dailyBrSaleVolumeByState: [DailyBrSaleVolumeByState!]!
  getPartnershipAgreementOffline(
    input: GetPartnershipAgreementOfflineInput!
  ): BrSalesPerson
}

type Mutation {
  updateBrSalesPerson(input: UpdateBrSalesPersonInput!): BrSalesPerson
  createBrSalesPerson(input: CreateBrSalesPersonInput!): BrSalesPerson
  createPartnershipAgreementForm(
    input: CreatePartnershipAgreementFormInput!
  ): BrSalesPerson
  selfRegisterSalesPartner(input: SelfRegisterSalesPartnerInput!): BrSalesPerson
  deleteBrSalesPerson(id: Int!): DeletedEntity
}
