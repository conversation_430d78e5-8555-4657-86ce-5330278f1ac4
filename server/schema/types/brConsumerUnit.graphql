"""
This type is not relevant to our investment app
"""
type BrConsumerUnit {
  id: Int!
  address1: String
  address2: String
  aprConsumption: Float
  augConsumption: Float
  brCustomerId: Int
  brTariffClassId: Int
  brSelfConsumptionOfftakerId: Int
  city: String
  countryCode: String
  decConsumption: Float
  district: String
  febConsumption: Float
  hubSpotDealId: String
  installationCode: String
  invoiceOfflineFlg: Boolean
  janConsumption: Float
  julConsumption: Float
  junConsumption: Float
  marConsumption: Float
  mayConsumption: Float
  name: String
  novConsumption: Float
  octConsumption: Float
  postalCode: String
  preferredPaymentMethod: String
  previousConsortiumContractTerminationDt: DateOnly
  salesPartnerNotes: String
  sepConsumption: Float
  state: String
  utilityCompanyId: Int
  utilityCustomerCode: String
  salesforceProjectId: Int

  # associations
  salesforceProject: SalesforceProject
  brCreditCompensations: [BrCreditCompensation!]!
  brConsumerUnitStage: BrConsumerUnitStage
  brCustomer: BrCustomer!
  brNotes: [BrNote!]!
  brTickets: [BrTicket!]!
  brSalesPerson: BrSalesPerson
  salesPersonBrContact: BrContact
  brSelfConsumptionOfftaker: BrSelfConsumptionOfftaker
  brTariffClass: BrTariffClass
  currentBrTermsOfAdhesion: BrTermsOfAdhesion
  brTermsOfAdhesions: [BrTermsOfAdhesion!]!
  brUtilityBills: [BrUtilityBill!]!
  brVoltagePhase: BrVoltagePhase
  utilityCompany: UtilityCompany
  recentBrCreditCompensations: [BrCreditCompensation!]!
  latestBrCreditCompensation: BrCreditCompensation
  mostRecentUnpaidBrInvoice: BrInvoice

  # calculated
  address: String
  amountDue: Float!
  avgMonthlyConsumption: Float
  adjustedAvgMonthlyConsumption: Float
  currentDiscountRate: Float
  onboardingStatus: BrConsumerUnitOnboardingStatus!
  pastDue: Float!
  percentageOfConsumptionDataInput: Float
  consumptionSeasonalityAsPercentageOffMax: Float
  startDt: Date
  hasInvoiceSentToCollections: Boolean!

  # system
  createdAt: Date!
  updatedAt: Date!
}

type BrConsumerUnitOnboardingStatus {
  status: String!
  requirementsEnglish: String
  requirementsPortuguese: String
}

type BrConsumerUnitFeed {
  rows: [BrConsumerUnit!]!
  count: Int!
}

input UpdateBrConsumerUnitInput {
  id: Int!
  address1: String
  address2: String
  aprConsumption: Float
  augConsumption: Float
  brCustomerId: Int
  brConsumerUnitStageId: Int
  brSalesPersonId: Int
  salesPersonBrContactId: Int
  brSelfConsumptionOfftakerId: Int
  brTariffClassId: Int
  brVoltagePhaseId: Int
  city: String
  countryCode: String
  decConsumption: Float
  district: String
  febConsumption: Float
  hubSpotDealId: String
  installationCode: String
  invoiceOfflineFlg: Boolean
  janConsumption: Float
  julConsumption: Float
  junConsumption: Float
  marConsumption: Float
  mayConsumption: Float
  novConsumption: Float
  octConsumption: Float
  postalCode: String
  preferredPaymentMethod: String
  previousConsortiumContractTerminationDt: DateOnly
  salesPartnerNotes: String
  sepConsumption: Float
  state: String
  utilityCompanyId: Int
  utilityCustomerCode: String
  salesforceProjectId: Int

  sendStageChangeEmail: Boolean
}

input CreateBrConsumerUnitInput {
  address1: String
  address2: String
  aprConsumption: Float
  augConsumption: Float
  brCustomerId: Int!
  brConsumerUnitStageId: Int
  brSalesPersonId: Int
  salesPersonBrContactId: Int
  brSelfConsumptionOfftakerId: Int
  brTariffClassId: Int
  brVoltagePhaseId: Int
  city: String
  countryCode: String
  decConsumption: Float
  district: String
  febConsumption: Float
  hubSpotDealId: String
  installationCode: String!
  invoiceOfflineFlg: Boolean
  janConsumption: Float
  julConsumption: Float
  junConsumption: Float
  marConsumption: Float
  mayConsumption: Float
  novConsumption: Float
  octConsumption: Float
  postalCode: String
  preferredPaymentMethod: String
  previousConsortiumContractTerminationDt: DateOnly
  salesPartnerNotes: String
  sepConsumption: Float
  state: String
  utilityCompanyId: Int
  utilityCustomerCode: String
  salesforceProjectId: Int

  brPowerPlanId: Int

  draftTermsOfAdhesionOnSuccess: Boolean
}

input BrConsumerUnitFilter {
  q: String
  brCustomer: Entity
  brSalesPerson: Entity
  salesPersonBrContact: Entity
  brConsumerUnitStage: Entity
  salesforceProject: Entity
  cpfCnpj: String
  hasSignedToa: Boolean
  installationCode: String
  brCustomerName: String
}

type Query {
  getBrConsumerUnit(id: Int!): BrConsumerUnit
  brConsumerUnitFeed(
    pagination: Pagination
    sort: Sort
    filter: BrConsumerUnitFilter
    exportConfig: ListExportInput
  ): BrConsumerUnitFeed
  allBrConsumerUnits: [BrConsumerUnit!]!
  getBrConsumerUnitExcelDownloadUrl(filter: BrConsumerUnitFilter, sort: Sort): String!
}

type Mutation {
  updateBrConsumerUnit(input: UpdateBrConsumerUnitInput!): BrConsumerUnit
  createBrConsumerUnit(input: CreateBrConsumerUnitInput!): BrConsumerUnit
  deleteBrConsumerUnit(id: Int!): DeletedEntity
}
