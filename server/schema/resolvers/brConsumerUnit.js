import { ApolloError, ForbiddenError } from 'apollo-server-express';
import moment from 'moment';
import { Sequelize } from 'sequelize';
import Queue from 'bull';
import { getAuthorized } from '../../../utils/authMatrix';
import {
  createTermsOfAdhesionForm,
  getCurrentDiscountRate,
  getCurrentTermsOfAdhesion,
  getConsumerUnitStartDt,
  getBrConsumerUnitFeedQueryConfig,
} from '../../services/creditManagement';
import SlackService from '../../services/slack';
import {
  sendBrConsumerUnitApprovedEmail,
  sendBrConsumerUnitActivatingEmail,
  sendBrConsumerUnitActivatedEmail,
  sendBrConsumerUnitCancelledEmail,
} from '../../services/mail';
import { lintAwsObjectKey, stringifyObject } from '../../../utils/global';
import { bullOptions } from '../../services/redis';
import ExporterService from '../../services/exporter';
import { getSignedUrl, uploadObjectToS3 } from '../../services/aws';

const { Op, fn, col } = Sequelize;

export default {
  BrConsumerUnit: {
    brSelfConsumptionOfftaker: (brConsumerUnit) =>
      brConsumerUnit.getBrSelfConsumptionOfftaker(),
    brSalesPerson: (brConsumerUnit) => brConsumerUnit.getBrSalesPerson(),
    salesPersonBrContact: (brConsumerUnit) =>
      brConsumerUnit.getSalesPersonBrContact(),
    brConsumerUnitStage: (brConsumerUnit) =>
      brConsumerUnit.getBrConsumerUnitStage(),
    brNotes: (brConsumerUnit) =>
      brConsumerUnit.getBrNotes({ order: [['createdAt', 'DESC']] }),
    brTickets: (brConsumerUnit) =>
      brConsumerUnit.getBrTickets({ order: [['openedDt', 'DESC']] }),
    brVoltagePhase: (brConsumerUnit) => brConsumerUnit.getBrVoltagePhase(),
    brUtilityBills: (brConsumerUnit) => brConsumerUnit.getBrUtilityBills(),
    percentageOfConsumptionDataInput: (brConsumerUnit) => {
      const monthlyValues = [
        brConsumerUnit.janConsumption,
        brConsumerUnit.febConsumption,
        brConsumerUnit.marConsumption,
        brConsumerUnit.aprConsumption,
        brConsumerUnit.mayConsumption,
        brConsumerUnit.junConsumption,
        brConsumerUnit.julConsumption,
        brConsumerUnit.augConsumption,
        brConsumerUnit.sepConsumption,
        brConsumerUnit.octConsumption,
        brConsumerUnit.novConsumption,
        brConsumerUnit.decConsumption,
      ];

      const nonNullValuesCount = monthlyValues.reduce(
        (count, value) => count + (value !== null ? 1 : 0),
        0
      );
      return (nonNullValuesCount / 12) * 100;
    },
    consumptionSeasonalityAsPercentageOffMax: (brConsumerUnit) => {
      const monthlyValues = [
        brConsumerUnit.janConsumption,
        brConsumerUnit.febConsumption,
        brConsumerUnit.marConsumption,
        brConsumerUnit.aprConsumption,
        brConsumerUnit.mayConsumption,
        brConsumerUnit.junConsumption,
        brConsumerUnit.julConsumption,
        brConsumerUnit.augConsumption,
        brConsumerUnit.sepConsumption,
        brConsumerUnit.octConsumption,
        brConsumerUnit.novConsumption,
        brConsumerUnit.decConsumption,
      ];

      let minValue = Infinity;
      let maxValue = -Infinity;
      let foundValidValues = false;

      for (let value of monthlyValues) {
        if (value !== null) {
          foundValidValues = true;
          if (value < minValue) minValue = value;
          if (value > maxValue) maxValue = value;
        }
      }

      if (!foundValidValues) {
        return null; // or 0 if preferred
      }

      return ((maxValue - minValue) / maxValue) * 100;
    },
    hasInvoiceSentToCollections: (brConsumerUnit, args, { models }) =>
      models.brInvoice
        .count({
          where: {
            sentToCollectionsDt: {
              [Op.not]: null,
            },
          },
          include: [
            {
              model: models.brCreditCompensation,
              where: { brConsumerUnitId: brConsumerUnit.id },
              required: true,
              attributes: [],
            },
          ],
        })
        .then((res) => res > 0),
    startDt: (brConsumerUnit) => getConsumerUnitStartDt(brConsumerUnit.id),
    mostRecentUnpaidBrInvoice: (brConsumerUnit, args, { models }) =>
      models.brInvoice.findOne({
        where: {
          cancelledDt: null,
          amountDue: {
            [Op.gt]: 0,
          },
          amountPaid: {
            [Op.lt]: col('amountDue'),
          },
        },
        include: [
          {
            model: models.brCreditCompensation,
            where: { brConsumerUnitId: brConsumerUnit.id },
            required: true,
            attributes: [],
          },
        ],
        order: [['dueDt', 'DESC']],
      }),
    salesforceProject: (brConsumerUnit) =>
      brConsumerUnit.getSalesforceProject(),
    brCustomer: (brConsumerUnit) => brConsumerUnit.getBrCustomer(),
    brTariffClass: (brConsumerUnit) => brConsumerUnit.getBrTariffClass(),
    brTermsOfAdhesions: (brConsumerUnit) =>
      brConsumerUnit.getBrTermsOfAdhesions({
        order: [['signatureDt', 'DESC NULLS LAST']],
      }),
    currentBrTermsOfAdhesion: (brConsumerUnit) =>
      getCurrentTermsOfAdhesion(brConsumerUnit.id),
    currentDiscountRate: (brConsumerUnit) =>
      getCurrentDiscountRate(brConsumerUnit.id),
    onboardingStatus: async (brConsumerUnit, args, { models }) => {
      const [customer, termsOfAdhesionWPowerPlan] = await Promise.all([
        brConsumerUnit.getBrCustomer(),
        brConsumerUnit
          .getBrTermsOfAdhesions({
            where: {
              [Op.or]: [
                { endDt: null },
                {
                  endDt: {
                    [Op.gte]: new Date(),
                  },
                },
              ],
            },
            include: [
              {
                required: true,
                model: models.brPowerPlan,
                attributes: ['id'],
              },
            ],
            limit: 1,
            order: [['signatureDt', 'DESC NULLS LAST']],
          })
          .then((res) => res?.[0] || null),
      ]);

      const completedContact = await customer
        .getBrContacts({
          where: {
            firstName: {
              [Op.not]: null,
            },
            lastName: {
              [Op.not]: null,
            },
            email: {
              [Op.not]: null,
            },
            phone: {
              [Op.not]: null,
            },
          },
          limit: 1,
        })
        .then((res) => res?.[0] || null);

      if (
        !customer.type ||
        !(customer.cpf || customer.cnpj) ||
        !completedContact
      ) {
        // customer type, cpf/cnpj, and contact info (name, email, and phone) are required
        return {
          status: 'Customer Onboarding',
          requirementsEnglish:
            'Customer type, CPF/CNPJ, and contact info (name, email, and phone)',
        };
      }
      if (
        !termsOfAdhesionWPowerPlan ||
        !brConsumerUnit.installationCode ||
        !brConsumerUnit.utilityCompanyId ||
        (!brConsumerUnit.janConsumption &&
          brConsumerUnit.janConsumption !== 0) ||
        (!brConsumerUnit.febConsumption &&
          brConsumerUnit.febConsumption !== 0) ||
        (!brConsumerUnit.marConsumption &&
          brConsumerUnit.marConsumption !== 0) ||
        (!brConsumerUnit.aprConsumption &&
          brConsumerUnit.aprConsumption !== 0) ||
        (!brConsumerUnit.mayConsumption &&
          brConsumerUnit.mayConsumption !== 0) ||
        (!brConsumerUnit.junConsumption &&
          brConsumerUnit.junConsumption !== 0) ||
        (!brConsumerUnit.julConsumption &&
          brConsumerUnit.julConsumption !== 0) ||
        (!brConsumerUnit.augConsumption &&
          brConsumerUnit.augConsumption !== 0) ||
        (!brConsumerUnit.sepConsumption &&
          brConsumerUnit.sepConsumption !== 0) ||
        (!brConsumerUnit.octConsumption &&
          brConsumerUnit.octConsumption !== 0) ||
        (!brConsumerUnit.novConsumption &&
          brConsumerUnit.novConsumption !== 0) ||
        (!brConsumerUnit.decConsumption && brConsumerUnit.decConsumption !== 0)
      ) {
        // monthly consumption numbers, power plan assigned to a terms of adhesion (can be unsigned or pending), installation number, and utility customer code are required
        return {
          status: 'Consumer Unit Onboarding',
          requirementsEnglish:
            'All monthly consumption numbers, utility company, installation number, and power plan assigned to a terms of adhesion (can be unsigned or pending)',
        };
      }
      if (
        !termsOfAdhesionWPowerPlan ||
        !termsOfAdhesionWPowerPlan.signatureDt ||
        !termsOfAdhesionWPowerPlan.awsObjectKey
      ) {
        return {
          status: 'Consumer Unit Closeout',
          requirementsEnglish:
            'Terms of adhesion document has been signed, uploaded, and signature date has been set.',
        };
      }

      return {
        status: 'Complete',
      };
    },
    utilityCompany: (brConsumerUnit) => brConsumerUnit.getUtilityCompany(),
    amountDue: (brConsumerUnit, args, { models }) =>
      models.brInvoice
        .findAll({
          attributes: [
            [fn('sum', col('amountDue')), 'amountDue'],
            [fn('sum', col('amountPaid')), 'amountPaid'],
          ],
          where: {
            cancelledDt: null,
            amountPaid: {
              [Op.lt]: col('amountDue'),
            },
          },
          raw: true,
          include: [
            {
              model: models.brCreditCompensation,
              where: { brConsumerUnitId: brConsumerUnit.id },
              required: true,
              attributes: [],
            },
          ],
        })
        .then((res) =>
          Math.max(
            parseFloat(res[0].amountDue || 0) -
            parseFloat(res[0].amountPaid || 0),
            0
          )
        ),
    pastDue: (brConsumerUnit, args, { models }) =>
      models.brInvoice
        .findAll({
          attributes: [
            [fn('sum', col('amountDue')), 'amountDue'],
            [fn('sum', col('amountPaid')), 'amountPaid'],
          ],
          raw: true,
          where: {
            dueDt: {
              [Op.lt]: moment().format('YYYY-MM-DD'),
            },
            cancelledDt: null,
            amountPaid: {
              [Op.lt]: col('amountDue'),
            },
          },
          include: [
            {
              model: models.brCreditCompensation,
              where: { brConsumerUnitId: brConsumerUnit.id },
              required: true,
              attributes: [],
            },
          ],
        })
        .then((res) =>
          Math.max(
            parseFloat(res[0].amountDue || 0) -
            parseFloat(res[0].amountPaid || 0),
            0
          )
        ),
    brCreditCompensations: (brConsumerUnit, args, { models }) =>
      brConsumerUnit.getBrCreditCompensations({
        include: [
          { attributes: ['id', 'billingMonth'], model: models.brBillingCycle },
        ],
        order: [[models.brBillingCycle, 'billingMonth', 'ASC']],
      }),
    recentBrCreditCompensations: (brConsumerUnit, args, { models }) =>
      brConsumerUnit.getBrCreditCompensations({
        include: [
          { attributes: ['id', 'billingMonth'], model: models.brBillingCycle },
        ],
        order: [[models.brBillingCycle, 'billingMonth', 'DESC']],
        limit: 4,
      }),
    latestBrCreditCompensation: (brConsumerUnit, args, { models }) =>
      brConsumerUnit
        .getBrCreditCompensations({
          include: [
            {
              attributes: ['id', 'billingMonth'],
              model: models.brBillingCycle,
            },
            {
              attributes: [],
              model: models.brInvoice,
              required: true,
              where: { amountDue: { [Op.gt]: 0 } },
            },
          ],
          order: [[models.brBillingCycle, 'billingMonth', 'DESC']],
          limit: 1,
        })
        .then((res) => res?.[0] || null),
  },
  Query: {
    getBrConsumerUnit: (parent, { id }, { models }) =>
      models.brConsumerUnit.findByPk(id),
    allBrConsumerUnits: (parent, args, { models }) =>
      models.brConsumerUnit.findAll(),
    getBrConsumerUnitExcelDownloadUrl: async (parent, { filter, sort }, { models }) => {
      const { includes, filterClauses } = getBrConsumerUnitFeedQueryConfig(
        filter,
        sort
      );
      return models.brConsumerUnit.findAndCountAll({
        where: filterClauses,
        include: includes,
        order: [[sort.field, sort.order]],
      }).then(res =>
        ExporterService.getWorkbook({ type: 'partnerBrConsumerUnits', records: res.rows }).then(wb => {
          const awsObjectKey = lintAwsObjectKey(
            `temporary/partnerBrConsumerUnits_${moment().format(
              'YYYYMMDDHHmmssSSS'
            )}.xlsx`
          );
          return wb.writeToBuffer().then(
            (buffer) =>
              uploadObjectToS3(awsObjectKey, buffer, process.env.S3_BUCKET_CREDIT_MGMT
              ).then(
                () =>
                  getSignedUrl(
                    awsObjectKey,
                    `unidades_consumidoras.xlsx`,
                    process.env.S3_BUCKET_CREDIT_MGMT
                  ),
                (err1) => {
                  console.error('Error partner CU list excel to bucket', err1);
                  throw new ApolloError(
                    'Erro: Tente novamente mais tarde',
                    'AWS',
                    {
                      err: err1,
                    }
                  );
                }
              ),
            (err2) => {
              console.error('Error writing partner CU list excel to buffer', err2);
              throw new ApolloError(
                'Erro: Tente novamente mais tarde',
                'APPLICATION',
                {
                  err: err2,
                }
              );
            }
          );
        })
      )
    },
    brConsumerUnitFeed: async (
      parent,
      { filter, sort, pagination, exportConfig },
      { models }
    ) => {
      const { includes, filterClauses } = getBrConsumerUnitFeedQueryConfig(
        filter,
        sort
      );

      if (!exportConfig || !exportConfig.emailResults) {
        return models.brConsumerUnit.findAndCountAll({
          where: filterClauses,
          include: includes,
          order: [[sort.field, sort.order]],
          offset: (pagination.page - 1) * pagination.perPage,
          limit: pagination.perPage,
        });
      }

      const queue = new Queue('handleListEmailExport', bullOptions);
      await queue.add({
        sort,
        filter,
        exportConfig,
        list: 'brConsumerUnit',
      });
      queue.close();
      return {
        rows: [],
        count: 0,
      };
    },
  },
  Mutation: {
    createBrConsumerUnit: async (
      parent,
      args,
      { models, user: contextUser, brContact, roles }
    ) => {
      // Check for authorized user
      const isAuthorized = getAuthorized('allUsers', roles);
      if (!isAuthorized) {
        const brContactBelongsToCustomer = await models.brContactsBrCustomer
          .findAll({
            attributes: ['id'],
            where: {
              brContactId: brContact.id,
              brCustomerId: args.input.brCustomerId,
            },
          })
          .then((res) => res.length > 0);
        if (!brContactBelongsToCustomer) {
          return new ForbiddenError('You do not have access to this endpoint', {
            contextUser,
            roles,
          });
        }
      }

      // Set name based on installation code and customer name
      const lintedInput = args.input;
      if (!lintedInput.name) {
        if (lintedInput.brCustomerId) {
          await models.brCustomer
            .findByPk(lintedInput.brCustomerId, { attributes: ['name'] })
            .then((brCustomer) => {
              lintedInput.name = `${lintedInput.installationCode || ''} - ${brCustomer?.name?.toUpperCase() || ''
                }`;
            });
        } else {
          lintedInput.name = lintedInput.installationCode || '';
        }
      }

      // default stage to em analise (in analysis)
      if (!lintedInput.brConsumerUnitStageId) {
        lintedInput.brConsumerUnitStageId = 1;
      }

      // If power plan is provided (as is done when partner sends CU a link to register), get the power plan and set some fields on the CU if needed
      if (!lintedInput.brTariffClassId && lintedInput.brPowerPlanId) {
        const powerPlan = await models.brPowerPlan.findByPk(
          lintedInput.brPowerPlanId,
          {
            attributes: ['brTariffClassId', 'overrideSalesforceProjectId'],
          }
        );

        if (!lintedInput.brTariffClassId) {
          lintedInput.brTariffClassId = powerPlan?.brTariffClassId;
        }

        if (!lintedInput.salesforceProjectId) {
          if (powerPlan?.overrideSalesforceProjectId) {
            lintedInput.salesforceProjectId =
              powerPlan.overrideSalesforceProjectId;
          }
        }
      }

      return models.brConsumerUnit
        .create(lintedInput, {
          authId: contextUser?.authId || contextUser?.oktaId,
        })
        .then(
          async (res) => {
            // Handle setting default project and consortium based on utility company if not already set
            if (!res.salesforceProjectId && res.utilityCompanyId) {
              const [customer, utilityCompany] = await Promise.all([
                res.getBrCustomer({
                  attributes: ['brConsortiumId'],
                }),
                res.getUtilityCompany({
                  attributes: ['defaultSalesforceProjectId'],
                }),
              ]);

              if (utilityCompany.defaultSalesforceProjectId) {
                const defaultConsortium = await models.brConsortium.findOne({
                  include: [
                    {
                      required: true,
                      attributes: [],
                      model: models.salesforceProject,
                      where: {
                        id: utilityCompany.defaultSalesforceProjectId,
                      },
                    },
                  ],
                });

                // Only update the CU's project if it doesn't have a consortium or if the consortium matches the utility company's default project
                if (
                  !customer.brConsortiumId ||
                  (defaultConsortium &&
                    customer.brConsortiumId === defaultConsortium.id)
                ) {
                  const updatedCU = res;
                  updatedCU.salesforceProjectId =
                    utilityCompany.defaultSalesforceProjectId;
                  await updatedCU.save();
                }

                // Only update the customer's consortium if there is a default consortium and doesn't have one
                if (defaultConsortium) {
                  if (!customer.brConsortiumId) {
                    await models.brCustomer
                      .update(
                        {
                          brConsortiumId: defaultConsortium.id,
                        },
                        {
                          where: {
                            id: res.brCustomerId,
                          },
                        }
                      )
                      .catch((error) => {
                        console.error(
                          'Error setting consortium on customer',
                          'APPLICATION',
                          { error }
                        );
                      });
                  }
                }
              }
            }
            SlackService.logToSlack({
              title: 'New Consumer Unit Created',
              type: 'credit-management-customers',
              url: `${process.env.CMS_HOST}/BrConsumerUnit/${res.id}`,
              data: [
                {
                  label: 'Name',
                  value: res.name,
                },
                {
                  label: 'Created By',
                  value: contextUser ? contextUser.email : brContact?.email,
                },
              ],
            });

            if (lintedInput.draftTermsOfAdhesionOnSuccess) {
              if (!res.utilityCompanyId) {
                SlackService.logToSlack({
                  title:
                    'Failed to automatically create terms of adhesion form for new CU',
                  type: 'credit-management-customers',
                  url: `${process.env.CMS_HOST}/BrConsumerUnit/${res.id}`,
                  data: [
                    {
                      label: 'Opportunity',
                      value: res.name,
                    },
                    {
                      label: 'Error',
                      value:
                        'Consumer unit missing utility company. Credit team to complete missing data and use CMS to send TOA for signature.',
                    },
                  ],
                });
                return res;
              }
              if (!res.brTariffClassId) {
                SlackService.logToSlack({
                  title:
                    'Failed to automatically create terms of adhesion form for new CU',
                  type: 'credit-management-customers',
                  url: `${process.env.CMS_HOST}/BrConsumerUnit/${res.id}`,
                  data: [
                    {
                      label: 'Opportunity',
                      value: res.name,
                    },
                    {
                      label: 'Error',
                      value:
                        'Consumer unit missing tariff class. Credit team to complete missing data and use CMS to send TOA for signature.',
                    },
                  ],
                });
                return res;
              }

              const brPowerPlanFilter = lintedInput.brPowerPlanId
                ? { id: lintedInput.brPowerPlanId }
                : {
                  brTariffClassId: res.brTariffClassId,
                  utilityCompanyId: res.utilityCompanyId,
                  defaultFlg: true,
                };
              if (res.brTariffClassId === 1 || res.brTariffClassId === 3) {
                brPowerPlanFilter.brTariffClassId = {
                  [Op.or]: [res.brTariffClassId, 4],
                };
              }
              return models.brPowerPlan
                .findOne({
                  where: brPowerPlanFilter,
                  order: [['id', 'ASC']],
                  limit: 1,
                })
                .then((brPowerPlan) => {
                  if (!brPowerPlan) {
                    SlackService.logToSlack({
                      title:
                        'Failed to automatically create terms of adhesion form for new CU',
                      type: 'credit-management-customers',
                      url: `${process.env.CMS_HOST}/BrConsumerUnit/${res.id}`,
                      data: [
                        {
                          label: 'Opportunity',
                          value: res.name,
                        },
                        {
                          label: 'Error',
                          value:
                            'No default power plan found. Credit team to complete missing data and use CMS to send TOA for signature.',
                        },
                      ],
                    });
                    return res;
                  }
                  return models.brTermsOfAdhesion
                    .create({
                      brConsumerUnitId: res.id,
                      brPowerPlanId: brPowerPlan.id,
                    })
                    .then((brTermsOfAdhesion) =>
                      createTermsOfAdhesionForm({
                        brTermsOfAdhesionId: brTermsOfAdhesion.id,
                      }).then(
                        () => res,
                        (err) => {
                          SlackService.logToSlack({
                            title:
                              'Failed to automatically create terms of adhesion form for new CU',
                            type: 'credit-management-customers',
                            url: `${process.env.CMS_HOST}/BrConsumerUnit/${res.id}`,
                            data: [
                              {
                                label: 'Opportunity',
                                value: res.name,
                              },
                              {
                                label: 'Message',
                                value:
                                  'Error creating TOA document. Credit team to complete missing data and use CMS to send TOA for signature.',
                              },
                              { label: 'Error', value: stringifyObject(err) },
                            ],
                          });
                          return res;
                        }
                      )
                    );
                });
            }
            return res;
          },
          (error) => {
            console.error(error);
            throw new ApolloError(
              'Error creating Consumer Unit',
              'APPLICATION',
              {
                error,
              }
            );
          }
        );
    },
    updateBrConsumerUnit: async (
      parent,
      args,
      { models, user: contextUser, roles, brContact }
    ) => {
      const { input } = args;
      if (!input.id) {
        throw new Error('You must provide an id');
      }
      // Check for authorized user
      const isAuthorized = getAuthorized('allUsers', roles);
      if (!isAuthorized) {
        const brContactBelongsToCustomer =
          args.input.brCustomerId &&
          (await models.brContactsBrCustomer
            .findAll({
              attributes: ['id'],
              where: {
                brContactId: brContact.id,
                brCustomerId: args.input.brCustomerId,
              },
            })
            .then((res) => res.length > 0));
        const brContactIsCUSalesPartner = await models.brContact.findOne({
          attributes: ['id'],
          where: {
            id: brContact.id,
          },
          include: [
            {
              attributes: [],
              required: true,
              model: models.brSalesPerson,
              include: [
                {
                  attributes: [],
                  required: true,
                  model: models.brConsumerUnit,
                  where: { id: input.id },
                },
              ],
            },
          ],
        });
        if (!brContactBelongsToCustomer && !brContactIsCUSalesPartner) {
          return new ForbiddenError('You do not have access to this endpoint', {
            contextUser,
            roles,
          });
        }
      }

      const closeTermsOfAdhesion = async (brConsumerUnitId) => {
        try {
          // Batch update to close open terms of adhesion
          const now = new moment().subtract(1, 'days').toDate();
          await models.brTermsOfAdhesion.update(
            { endDt: now },
            {
              where: {
                brConsumerUnitId,
                endDt: null,
              },
            }
          );
        } catch (error) {
          console.error(
            `Error closing terms of adhesion for brConsumerUnitId ${brConsumerUnitId}:`,
            error
          );
          throw error; // Rethrow the error if needed
        }
      };

      return models.brConsumerUnit
        .findByPk(input.id)
        .then((brConsumerUnit) => {
          const initialAvgMonthlyConsumption =
            brConsumerUnit.avgMonthlyConsumption;
          const saveObj = brConsumerUnit;
          Object.keys(input).forEach(async (key) => {
            if (['id'].indexOf(key) < 0) {
              const value = input[String(key)];
              saveObj[String(key)] =
                value instanceof Date
                  ? moment.utc(value).format('MM-DD-YYYY')
                  : value;
              if (
                key === 'brConsumerUnitStageId' &&
                [4, 5].includes(saveObj.brConsumerUnitStageId)
              ) {
                await closeTermsOfAdhesion(saveObj.id);
              }
            }
          });
          // NOTE: The below is no longer need since NUV was fired
          // const newAvgMonthlyConsumption = saveObj.avgMonthlyConsumption;
          // if (
          //   initialAvgMonthlyConsumption !== newAvgMonthlyConsumption &&
          //   brConsumerUnit.salesforceProjectId &&
          //   brConsumerUnit.id === 4513 // NUV's CU. We still want to see historical consumption data for this CU on the sales dashboard without having to manage all CUs in the CMS
          // ) {
          //   models.brProjectSalesHistoryUpdate.create({
          //     salesforceProjectId: brConsumerUnit.salesforceProjectId,
          //     effectiveDt: new Date(),
          //     grossAverageMonthlyConsumption: newAvgMonthlyConsumption,
          //   });
          // }

          if (input.sendStageChangeEmail) {
            /*
              1 em analise
              2 em activacao
              3 ativo
              4 em cancelamento
              5 cancelado
              7 em espera
              8 reprovado - Baixo consumo
              9 aprovado
              10 reprovado - Dados incompletos
            */
            const cuApproved =
              saveObj.brConsumerUnitStageId === 9 &&
              [1, 7].indexOf(
                saveObj._previousDataValues.brConsumerUnitStageId
              ) > -1;
            const cuActivating =
              saveObj.brConsumerUnitStageId === 2 &&
              [9].indexOf(saveObj._previousDataValues.brConsumerUnitStageId) >
              -1;
            const cuActivated =
              saveObj.brConsumerUnitStageId === 3 &&
              [2].indexOf(saveObj._previousDataValues.brConsumerUnitStageId) >
              -1;
            const cuCancelling =
              saveObj.brConsumerUnitStageId === 4 &&
              [3].indexOf(saveObj._previousDataValues.brConsumerUnitStageId) >
              -1;
            if (cuApproved || cuActivating || cuActivated || cuCancelling) {
              Promise.all([
                brConsumerUnit.getBrCustomer({
                  required: true,
                  attributes: ['id'],
                  include: [
                    {
                      required: true,
                      attributes: ['id'],
                      model: models.brContactsBrCustomer,
                      where: {
                        primaryContactFlg: true,
                      },
                      include: [
                        {
                          required: true,
                          attributes: ['email', 'firstName', 'lastName'],
                          model: models.brContact,
                        },
                      ],
                    },
                  ],
                }),
                brConsumerUnit.getUtilityCompany({
                  attributes: ['name'],
                }),
                brConsumerUnit.getSalesforceProject({ attributes: ['id'] }),
              ]).then(([brCustomer, utilityCompany, salesforceProject]) => {
                const contact =
                  brCustomer?.brContactsBrCustomers?.[0]?.brContact;
                if (!contact || !contact.email) {
                  SlackService.logToSlack({
                    title: `No contact found for consumer unit ${brConsumerUnit.id} to send stage change email`,
                    type: 'credit-management-customers',
                    data: [
                      {
                        label: 'Consumer Unit',
                        value: brConsumerUnit.name,
                      },
                      {
                        label: 'Message',
                        value:
                          "Didn't send stage change email due to missing primary contact or email address.",
                      },
                    ],
                  });
                } else {
                  // eslint-disable-next-line no-lonely-if
                  if (cuApproved) {
                    if (!utilityCompany) {
                      SlackService.logToSlack({
                        title: `No utility company found for consumer unit ${brConsumerUnit.id} to send stage change email`,
                        type: 'credit-management-customers',
                        data: [
                          {
                            label: 'Consumer Unit',
                            value: brConsumerUnit.name,
                          },
                          {
                            label: 'Message',
                            value:
                              "Didn't send stage change email due to missing utility company.",
                          },
                        ],
                      });
                    } else {
                      const vassourasSalesforceProjectId = 96;
                      sendBrConsumerUnitApprovedEmail({
                        brContact: contact,
                        consumerUnitName: brConsumerUnit.name,
                        utilityCompanyName: utilityCompany.name,
                        isVassouras:
                          salesforceProject?.id ===
                          vassourasSalesforceProjectId,
                      });
                    }
                  } else if (cuActivating) {
                    sendBrConsumerUnitActivatingEmail({
                      brContact: contact,
                      consumerUnitName: brConsumerUnit.name,
                    });
                  } else if (cuActivated) {
                    sendBrConsumerUnitActivatedEmail({
                      brContact: contact,
                      consumerUnitName: brConsumerUnit.name,
                    });
                  } else if (cuCancelling) {
                    sendBrConsumerUnitCancelledEmail({
                      brContact: contact,
                    });
                  }
                }
              });
            }
          }
          return saveObj.save({
            authId: contextUser?.authId || contextUser?.oktaId,
          });
        })
        .catch((error) => {
          throw new ApolloError(
            'Error updating this brConsumerUnit',
            'APPLICATION',
            {
              error,
            }
          );
        });
    },
    deleteBrConsumerUnit: async (
      parent,
      { id },
      { models, user: contextUser, roles }
    ) => {
      // Check for authorized user
      const isAuthorized = getAuthorized('allUsers', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          contextUser,
          roles,
        });
      }
      const cuToaCount = await models.brTermsOfAdhesion.count({
        where: {
          brConsumerUnitId: id,
        },
      });
      if (cuToaCount > 0) {
        throw new ApolloError(
          'Cannot delete a consumer unit that has any terms of adhesions. Cancel the CU instead or delete the terms of adhesion first.',
          'VALIDATION'
        );
      }
      return models.brConsumerUnit
        .destroy({
          where: {
            id,
          },
          authId: contextUser?.authId || contextUser?.oktaId,
        })
        .then(
          () => ({
            id,
          }),
          (error) =>
            new ApolloError('Error deleting brConsumerUnit', 'APPLICATION', {
              error,
            })
        );
    },
  },
};
