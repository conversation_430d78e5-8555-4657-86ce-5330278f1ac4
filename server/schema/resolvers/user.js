import { Client } from 'dwolla-v2';
import cloudinary from 'cloudinary';
import { GraphQLError } from 'graphql';
import { Cloudinary } from 'cloudinary-core';
import validator from 'validator';

import { Sequelize } from 'sequelize';
import Queue from 'bull';
import moment from 'moment';
import numeral from 'numeral';
import passwordGenerator from 'generate-password';

import countries from '../../../utils/countries';
import states from '../../../utils/states';
import {
  constants,
  interpolateColors,
  portfolioCanAcceptInvestments,
  round,
  stringifyObject,
} from '../../../utils/global';
import { getAuthorized } from '../../../utils/authMatrix';
import updateUser from '../../services/updateUser';
import {
  enrollInMFA,
  confirmEmail,
  signup,
  disableMFA,
  sendPasswordResetEmail,
} from '../../services/auth';
import {
  checkUserAccountAccess,
  getUserAccessibleAccounts,
} from '../../services/accountAccessAuth';
import { oktaClient, oktaGroupDict } from '../../services/okta';
import { getSignedUrl } from '../../services/aws';
import { bullOptions } from '../../services/redis';
import { getMTCEstimatedInvestableBalance } from '../../services/subAccount';

import {
  sendBrazilContactUsEmail,
  sendContactUsEmail,
  sendCustomerPermanentlySuspendedEmail,
  sendFeedbackEmail,
  sendEmailAddressChangeEmail,
  sendForgotPasswordEmail,
  sendNotifyMTCSuspendedUserEmail,
  sendRegisterEmail,
  sendTransferDisputeEmail,
  sendCustomerTransfersSelfCancelledEmail,
  sendIssueEmail,
  sendNewEmployeeEmail,
  sendAccountDeletionConfirmationEmail,
} from '../../services/mail';
import ValidatorService from '../../services/ipQualityScore';

import DwollaService from '../../services/dwolla';
import EversignService from '../../services/eversign';
import HubSpotService from '../../services/hubSpot';
import PortfolioService from '../../services/portfolio';
import UserService from '../../services/user';
import {
  getAuthSessionStatus,
  getMillenniumTrustInvestmentTransferFromDbTransfer,
} from '../../services/millenniumTrust';
import SlackService from '../../services/slack';
import GoogleMapsService from '../../services/googleMaps';

import database from '../../models/index';
import {
  createAuth0User,
  getAuth0User,
  deleteAuth0User,
  updateAuth0User,
  updateAuth0UserWithMerge,
  importOktaUsers,
  getEnrollments,
  // getCurrentIP,
  getAuth0Tokens,
  createTOTPEnrollment,
  confirmTOTPEnrollment,
  createSMSMFAEnrollment,
  sendSMSVerificationCodeById,
  confirmSMSMFAEnrollment,
  deleteSMSMFAEnrollment,
  getSMSMFAEnrollments,
} from '../../services/auth0';
import { runAuth0Migration } from '../../services/auth0Migration';

const User = database.models.user;
const ModalAlertEvent = database.models.modalAlertEvent;

const { Op, fn, col } = Sequelize;

const dwolla = new Client({
  key: process.env.DWOLLA_APP_KEY,
  secret: process.env.DWOLLA_APP_SECRET,
  environment: process.env.DWOLLA_ENV,
});

const stagingDwolla = new Client({
  key: process.env.STAGING_DWOLLA_APP_KEY,
  secret: process.env.STAGING_DWOLLA_APP_SECRET,
  environment: 'sandbox',
});

const cl = new Cloudinary({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  secure: true,
});

export default {
  User: {
    auth0User: (user) => {
      if (!user.authId) return null;
      return getAuth0User(user.authId).then((auth0User) => auth0User.data);
    },
    show2025IRAPromo: async (user) => {
      const mExpirationDt = moment(constants.iraPromo2025.expirationDt);
      const now = moment();
      if (now.isAfter(mExpirationDt)) {
        return false;
      }
      if (user.type !== 'personal') {
        return false;
      }
      const claimedPromo = await user.getPromoCodes({
        attributes: ['id'],
        where: {
          promoType: constants.iraPromo2025.typeName,
        },
        raw: true,
      });
      if (claimedPromo.length > 0) {
        return false;
      }
      return true;
    },
    personalInfoComplete: (user) =>
      UserService.getMissingPersonalFieldsNo(user) === 0,
    investorInfoComplete: (user) =>
      UserService.getMissingInvestorFieldsNo(user) === 0,
    businessInfoComplete: (user) =>
      UserService.getMissingBusinessFieldsNo(user) === 0,
    iraComplete: UserService.hasSubAccountWithBuyDirection,
    securityComplete: (user) => {
      // TODO: Implement
      return true;
    },
    lastPasswordResetText: (user) => UserService.getLastPasswordResetText(user),
    beneficiaryComplete: async (user) =>
      UserService.getBeneficiaryComplete(user),
    corporateBeneficiaryComplete: async (user) => {
      const beneficiaries = await user.getCorporateBeneficiaries({
        where: {
          primaryFlg: true,
        },
      });
      return beneficiaries && beneficiaries.length > 0;
    },
    percentageComplete: async (user) => {
      const [
        totalRequiredFields,
        isVerified,
        hasBankAccount,
        // beneficiaryComplete,
        missingRequiredFieldsNo,
        // missingSecurityFieldsNo,
        beneficialOwnershipIncomplete,
      ] = await Promise.all([
        user.totalRequiredFields,
        UserService.isVerified(user),
        user.hasBankAccount,
        // UserService.getBeneficiaryComplete(user), // NOTE: This is no longer required
        user.missingRequiredFieldsNo,
        // UserService.getMissingSecurityFieldsNo(user),
        UserService.getBeneficialOwnershipIncomplete(user),
      ]).catch((err) => {
        console.error('Error in user.percentageComplete resolver', err);
        return [0, false, false, false, 0, 0, false];
      });
      const requiresBeneficialOwnership =
        UserService.getRequiresBeneficialOwnership(user);

      return Math.round(
        ((totalRequiredFields +
          (requiresBeneficialOwnership && !beneficialOwnershipIncomplete
            ? 1
            : 0) +
          (isVerified ? 1 : 0) +
          (hasBankAccount ? 1 : 0) -
          missingRequiredFieldsNo -
          0) /
          (totalRequiredFields + 2 + (requiresBeneficialOwnership ? 1 : 0))) *
          100
      );
    },
    canAutoDeleteAccount: (user) => UserService.canAutoDeleteAccount(user),
    accountDeleteProcess: (user) => UserService.accountDeleteProcess(user),
    label: (user) => `#${user.id} ${user.firstName} ${user.lastName}`,
    employee: (user) =>
      user.getEmployee({ attributes: ['id', 'firstName', 'lastName'] }),
    userLogins: (user) =>
      user.getUserLogins({
        order: [['loginDt', 'desc']],
      }),
    showPreferredReceivingAccountFeature: async (user) => {
      const walletIsPreferredReceivingAcct = !user.preferredReceivingAccount;
      if (!walletIsPreferredReceivingAcct) return true;
      const [firstInvestmentDt, investmentSum] = await Promise.all([
        UserService.firstInvestmentDt(user),
        user
          .getInvestments({
            attributes: [[fn('sum', col('value')), 'investmentSum']],
            where: {
              cancelledDt: null,
            },
          })
          .then((investment) => investment?.[0]?.dataValues?.investmentSum),
      ]);
      const firstInvestment60DaysAgo =
        firstInvestmentDt &&
        moment(firstInvestmentDt).isBefore(
          moment().subtract(
            constants.daysFromFirstInvestmentToCashAllowedToLeavePlatform,
            'days'
          )
        );
      const hasInvestedEnough =
        investmentSum >=
        constants.preferredReceivingAccountFeatureTotalInvestedMin;
      return (
        !walletIsPreferredReceivingAcct ||
        !!(firstInvestment60DaysAgo && hasInvestedEnough)
      );
    },
    nextHubSpotMeetingDt: (user) => HubSpotService.getNextMeetingDt(user),
    accreditedQualification: (user) => user.getAccreditedQualification(),
    annualizedCOCYield: async (user, args, { models }) => {
      const aPromises = [];
      // Get dividend total
      aPromises.push(
        user
          .getDividends({
            attributes: [[fn('sum', col('value')), 'dividendTotal']],
            raw: true,
            include: [
              {
                attributes: [],
                required: true,
                model: models.monthlyPortfolioFinancialActual,
              },
            ],
          })
          .then((res) => parseFloat(res[0].dividendTotal))
      );
      // Get investments
      aPromises.push(
        user.getInvestments({
          attributes: ['value', 'shares', 'startDt', 'portfolioId'],
          where: {
            [Op.and]: [
              {
                cancelledDt: null,
              },
            ],
          },
        })
      );
      // Get Share Transfers
      aPromises.push(
        user.getSellOrders({
          attributes: ['id'],
          where: { cancelledDt: null },
          include: [
            {
              model: models.shareTransfer,
              attributes: ['value', 'sellDt'],
              include: [
                {
                  attributes: [],
                  required: true,
                  where: {
                    cancelledDt: null,
                  },
                  model: models.investment,
                },
              ],
            },
          ],
        })
      );

      const [totalDividends, investments, sellOrders] = await Promise.all(
        aPromises
      );
      if (!investments || investments.length === 0) {
        return 0;
      }
      const totalShareTransfers = sellOrders.reduce(
        (acc, sellOrder) =>
          acc +
          sellOrder.shareTransfers.reduce(
            (acc2, shareTransfer) => acc2 + parseFloat(shareTransfer.value),
            0
          ),
        0
      );

      const totalInvestmentDollarDays = investments.reduce(
        (acc, investment) => {
          const { value, startDt } = investment;
          const daysInvested = moment().diff(moment(startDt), 'days', true);
          return acc + daysInvested * parseFloat(value);
        },
        0
      );

      // NOTE: Uncomment to remove days that you didn't hold shares
      // const totalShareTransferDays = sellOrders.reduce((acc, sellOrder) => acc + sellOrder.shareTransfers.reduce((acc2, shareTransfer) => {
      //   const daysSold = moment().diff(moment(shareTransfer.sellDt), 'days', true)
      //   return acc2 + (daysSold * parseFloat(shareTransfer.value))
      // }, 0), 0);
      // const totalDollarDays = Math.max(totalInvestmentDollarDays - totalShareTransferDays,0)
      const totalDollarDays = totalInvestmentDollarDays;

      const returnObj =
        ((totalDividends + totalShareTransfers) / (totalDollarDays / 365)) *
        100;
      return Number.isNaN(returnObj) || !Number.isFinite(returnObj)
        ? null
        : round(returnObj, 2);
    },
    activeAutoInvestSubscriptions: (user) =>
      user.getAutoInvestSubscriptions({
        order: [['id', 'DESC']],
        where: {
          inactive: {
            [Op.not]: true,
          },
          archivedDt: null,
        },
      }),
    autoInvestSubscriptions: (user) =>
      user.getAutoInvestSubscriptions({
        order: [['id', 'DESC']],
        where: {
          archivedDt: null,
        },
      }),
    autoReinvestIndicators: (user, args) => {
      const subAccountFilter =
        args.accountFilter && args.accountFilter.allAccounts === false
          ? {
              subAccountId: args.accountFilter.subAccountId,
            }
          : null;
      return user.getAutoReinvestIndicators({
        where: {
          ...subAccountFilter,
        },
        order: [['id', 'DESC']],
      });
    },
    bestUnusedMatchPromoCode: async (user, args, { models }) => {
      try {
        const matchPromos = await user
          .getPromoCodes({
            where: {
              completedDt: null,
              expirationDt: {
                [Op.gt]: moment().toDate(),
              },
              promoType: [
                constants.foundersCardMatchPromo.typeName,
                constants.platinumLevelMatchPromo.typeName,
                constants.goldLevelMatchPromo.typeName,
                constants.greenLevelMatchPromo.typeName,
                constants.holiday2024Promo.typeName,
              ],
            },
            include: [
              {
                attributes: ['id'],
                model: models.investment,
                as: 'promoTriggeringInvestment',
                required: false,
                where: {
                  cancelledDt: null,
                },
              },
            ],
          })
          .catch((err) => {
            console.error('Error querying in bestUnusedMatchPromoCode', err);
            return [];
          });
        const unusedMatchPromos =
          matchPromos?.filter(
            (promoCode) =>
              !promoCode.promoTriggeringInvestment ||
              promoCode.promoTriggeringInvestment.length === 0
          ) || [];
        if (unusedMatchPromos.length === 0) {
          return null;
        }
        const foundersCardPromos = unusedMatchPromos.filter(
          (pc) => pc.promoType === constants.foundersCardMatchPromo.typeName
        );
        const platinumMatchPromos = unusedMatchPromos.filter(
          (pc) => pc.promoType === constants.platinumLevelMatchPromo.typeName
        );
        const goldMatchPromos = unusedMatchPromos.filter(
          (pc) => pc.promoType === constants.goldLevelMatchPromo.typeName
        );
        const greenMatchPromos = unusedMatchPromos.filter(
          (pc) => pc.promoType === constants.greenLevelMatchPromo.typeName
        );

        const holiday2024Promos = unusedMatchPromos.filter(
          (pc) => pc.promoType === constants.holiday2024Promo.typeName
        );

        if (foundersCardPromos.length > 0) {
          return foundersCardPromos[0];
        }
        if (platinumMatchPromos.length > 0) {
          return platinumMatchPromos[0];
        }
        if (goldMatchPromos.length > 0) {
          return goldMatchPromos[0];
        }
        if (greenMatchPromos.length > 0) {
          return greenMatchPromos[0];
        }
        if (holiday2024Promos.length > 0) {
          return holiday2024Promos[0];
        }
        return null;
      } catch (err) {
        console.error('Error in bestUnusedMatchPromoCode', err);
        return null;
      }
    },
    COCYield: async (user, args, { models }) => {
      const aPromises = [];
      // Get dividend total
      aPromises.push(
        user
          .getDividends({
            attributes: [[fn('sum', col('value')), 'dividendTotal']],
            raw: true,
            include: [
              {
                attributes: [],
                required: true,
                model: models.monthlyPortfolioFinancialActual,
              },
            ],
          })
          .then((res) => parseFloat(res[0].dividendTotal))
      );
      // Get investments
      // TODO: this can be reduced to agg function
      aPromises.push(
        user.getInvestments({
          attributes: ['value', 'shares', 'startDt', 'portfolioId'],
          where: {
            [Op.and]: [
              {
                // userId: {
                //   [Op.not]: 76
                // },
                cancelledDt: null,
              },
            ],
          },
        })
      );
      // Get Share Transfers
      // TODO: this can be reduced to agg function
      aPromises.push(
        user.getSellOrders({
          attributes: ['id'],
          where: { cancelledDt: null },
          include: [
            {
              model: models.shareTransfer,
              attributes: ['value'],
              include: [
                {
                  attributes: [],
                  required: true,
                  where: {
                    cancelledDt: null,
                  },
                  model: models.investment,
                },
              ],
            },
          ],
        })
      );

      const [totalDividends, investments, sellOrders] = await Promise.all(
        aPromises
      );
      if (!investments || investments.length === 0) {
        return 0;
      }
      const totalShareTransfers = sellOrders.reduce(
        (acc, sellOrder) =>
          acc +
          sellOrder.shareTransfers.reduce(
            (acc2, shareTransfer) => acc2 + parseFloat(shareTransfer.value),
            0
          ),
        0
      );
      const totalInvested = investments.reduce(
        (acc, investment) => acc + parseFloat(investment.value),
        0
      );
      const returnObj =
        ((totalDividends + totalShareTransfers) / totalInvested) * 100;
      return Number.isNaN(returnObj) ? null : round(returnObj, 4);
    },
    consolidatedAddress: (user) => {
      let sReturn = (user.address1 && user.address1.trim()) || '';
      const lintedAddress2 = user.address2 && user.address2.trim();
      if (lintedAddress2) {
        sReturn += `, ${lintedAddress2}`;
      }
      return sReturn;
    },
    consolidatedTaxName: (user) =>
      user.type === 'business' && user.businessName && user.businessName !== ''
        ? user.businessName
        : `${user.firstName} ${user.lastName}`,
    costBasis: (user, params) =>
      UserService.costBasis({
        user,
        allAccounts: params.accountFilter
          ? params.accountFilter.allAccounts
          : null,
        subAccountId: params.accountFilter
          ? params.accountFilter.subAccountId
          : null,
      }),
    dailyAccountValueChartData: async (user, params, { models }) => {
      const subAccountFilter =
        params.accountFilter && params.accountFilter.allAccounts === false
          ? { subAccountId: params.accountFilter.subAccountId }
          : null;
      const portfolioFilter = params.portfolioId
        ? { portfolioId: params.portfolioId }
        : null;

      // Run queries concurrently
      const promises = [
        user.getInvestments({
          attributes: [
            'id',
            'startDt',
            'portfolioId',
            'shares',
            'value',
            'dividendId',
          ],
          include: [
            {
              model: models.dividend,
            },
          ],
          where: {
            cancelledDt: null,
            ...subAccountFilter,
            ...portfolioFilter,
          },
          order: [['startDt', 'ASC']],
        }),
        user.getDividends({
          where: {
            ...subAccountFilter,
          },
          include: [
            {
              model: models.investment,
              required: false,
              where: {
                cancelledDt: null,
              },
            },
            portfolioFilter
              ? {
                  attributes: ['id'],
                  model: models.monthlyPortfolioFinancialActual,
                  where: {
                    portfolioId: params.portfolioId,
                  },
                  required: true,
                }
              : null,
          ].filter((el) => el),
          order: [['date', 'ASC']],
        }),
        user
          .getSellOrders({
            where: {
              ...subAccountFilter,
              cancelledDt: null,
              ...portfolioFilter,
            },
            attributes: ['id', 'portfolioId', 'giftFlg'],
            include: [
              {
                attributes: [
                  'id',
                  'investmentId',
                  'sellDt',
                  'soldShares',
                  'sellOrderId',
                  'value',
                ],
                model: models.shareTransfer,
              },
            ],
          })
          .then((sellOrders) => {
            const executedShareTransfers = [];
            if (sellOrders) {
              sellOrders.forEach((sellOrder) => {
                if (sellOrder.shareTransfers) {
                  sellOrder.shareTransfers.forEach((shareTransfer) => {
                    // Attach sell order info to each share transfer
                    shareTransfer.portfolioId = sellOrder.portfolioId;
                    shareTransfer.giftFlg = sellOrder.giftFlg;
                    executedShareTransfers.push(shareTransfer);
                  });
                }
              });
            }
            // If you add an order clause on sellDt in the getSellOrders call,
            // you may be able to avoid the sort below.
            return executedShareTransfers.sort((a, b) =>
              a.sellDt < b.sellDt ? -1 : 1
            );
          }),
      ];

      const [investments, dividends, shareTransfers] = await Promise.all(
        promises
      );

      if (!investments || investments.length === 0) {
        return [];
      }

      // Precompute start date and today once
      const today = moment();
      const startDt = moment(investments[0].startDt).startOf('day');

      // Precompute formatted date strings for each record to avoid repeated moment calls
      investments.forEach((inv) => {
        inv._dateStr = moment(inv.startDt).format('MM-DD-YYYY');
      });
      shareTransfers.forEach((st) => {
        st._dateStr = moment(st.sellDt).format('MM-DD-YYYY');
      });
      dividends.forEach((div) => {
        div._dateStr = moment(div.date).format('MM-DD-YYYY');
      });

      // Build portfolio share prices – use a Set to avoid duplicate requests
      const portfolioSharePrices = {};
      const portfolioIDs = new Set();
      const sharePricePromises = [];
      investments.forEach((investment) => {
        if (!portfolioIDs.has(investment.portfolioId)) {
          portfolioIDs.add(investment.portfolioId);
          const realizedSharePrice = investment.value / investment.shares;
          sharePricePromises.push(
            PortfolioService.getLintedSharePrices(
              investment.portfolioId,
              investment.startDt,
              today,
              realizedSharePrice
            ).then((sharePrices) => {
              portfolioSharePrices[String(investment.portfolioId)] =
                sharePrices;
            })
          );
        }
      });
      await Promise.all(sharePricePromises);

      // Pre-calculate the total number of days
      const dayCount = today.diff(startDt, 'days') + 1;
      const goal = {};
      const portfolioTallies = {}; // Stores sharesOwned per portfolio
      let investmentIndex = 0;
      let shareTransferIndex = 0;
      let dividendIndex = 0;
      let dividendsNotReinvestedTotal = 0.0;

      // Iterate once per day using a for-loop
      for (let i = 0; i < dayCount; i++) {
        const currDt = moment(startDt).add(i, 'days');
        const currDtStr = currDt.format('MM-DD-YYYY');

        const investmentsToday = [];
        while (
          investmentIndex < investments.length &&
          investments[investmentIndex]._dateStr === currDtStr
        ) {
          const investment = investments[investmentIndex];
          investmentsToday.push(investment);
          const pid = String(investment.portfolioId);
          if (!portfolioTallies[pid]) {
            portfolioTallies[pid] = { sharesOwned: 0.0 };
          }
          portfolioTallies[pid].sharesOwned += parseFloat(investment.shares);
          // For reinvested dividends, subtract from total dividends
          if (investment.dividendId) {
            dividendsNotReinvestedTotal -= parseFloat(investment.value);
          }
          investmentIndex++;
        }

        const shareTransfersToday = [];
        while (
          shareTransferIndex < shareTransfers.length &&
          shareTransfers[shareTransferIndex]._dateStr === currDtStr
        ) {
          const shareTransfer = shareTransfers[shareTransferIndex];
          const pid = String(shareTransfer.portfolioId);
          // Deduct sold shares from portfolio tally
          if (portfolioTallies[pid]) {
            portfolioTallies[pid].sharesOwned -= parseFloat(
              shareTransfer.soldShares
            );
          }
          shareTransfersToday.push(shareTransfer);
          shareTransferIndex++;
        }

        const dividendsToday = [];
        while (
          dividendIndex < dividends.length &&
          dividends[dividendIndex]._dateStr === currDtStr
        ) {
          const dividend = dividends[dividendIndex];
          dividendsNotReinvestedTotal += parseFloat(dividend.value);
          dividendsToday.push(dividend);
          dividendIndex++;
        }

        // Calculate net asset value for this day
        let nav = 0.0;
        Object.keys(portfolioTallies).forEach((pid) => {
          const currSharePrice =
            (portfolioSharePrices[pid] &&
              portfolioSharePrices[pid][currDtStr]) ||
            0;
          nav += portfolioTallies[pid].sharesOwned * currSharePrice;
        });

        goal[currDtStr] = {
          nav,
          investments: investmentsToday,
          shareTransfers: shareTransfersToday,
          dividends: dividendsToday,
          dividendsPaidOut: Math.max(dividendsNotReinvestedTotal, 0),
        };
      }
      return Object.keys(goal).map((dateStr) => ({
        date: moment(dateStr, 'MM-DD-YYYY').toDate(),
        investments: goal[dateStr].investments,
        shareTransfers: goal[dateStr].shareTransfers,
        dividends: goal[dateStr].dividends,
        nav: goal[dateStr].nav,
        dividendsPaidOut: goal[dateStr].dividendsPaidOut,
      }));
    },
    hasActiveAutoInvestSubscription: (user, args) => {
      if (args.accountFilter && args.accountFilter.subAccountId) return false;
      // NOTE: autoInvestSubscriptions don't yet have a subAccountId
      // const subAccountFilter =
      //   args.accountFilter && args.accountFilter.allAccounts === false
      //     ? {
      //         subAccountId: args.accountFilter.subAccountId,
      //       }
      //     : null;
      return user
        .getAutoInvestSubscriptions({
          attributes: ['id'],
          where: {
            inactive: {
              [Op.not]: true,
            },
            // ...subAccountFilter,
          },
        })
        .then((res) => res && res.length > 0);
    },
    hasActiveAutoReinvestIndicator: (user, args) => {
      const subAccountFilter =
        args.accountFilter && args.accountFilter.allAccounts === false
          ? {
              subAccountId: args.accountFilter.subAccountId,
            }
          : null;
      return user
        .getAutoReinvestIndicators({
          attributes: ['id'],
          where: {
            inactive: {
              [Op.not]: true,
            },
            percentage: {
              [Op.gt]: 0,
            },
            ...subAccountFilter,
          },
        })
        .then((res) => res && res.length > 0);
    },
    hasInvestedWithoutReferrals: (user) =>
      UserService.hasInvestedWithoutReferrals(user),
    isQualifiedForReferralRewards: (user) =>
      UserService.hasInvestedWithoutReferrals(user),
    hasInvested: (user, args) => {
      const subAccountFilter =
        args.accountFilter && args.accountFilter.allAccounts === false
          ? {
              subAccountId: args.accountFilter.subAccountId,
            }
          : null;
      return user
        .getInvestments({
          attributes: ['id'],
          where: {
            cancelledDt: null,
            ...subAccountFilter,
          },
          limit: 1,
        })
        .then((investments) => investments && investments.length > 0);
    },
    hasEnergeaIRA: (user) =>
      user
        .getSubAccounts({
          where: {
            subAccountTypeId: constants.millenniumTrustSubAccountTypeID,
          },
        })
        .then((res) => res && res.length > 0),
    hasPotentiallyIncompleteIRA: (user, args, { models }) => {
      const aPromises = [];
      aPromises.push(
        user
          .getMillenniumTrustAuthSessions({
            where: {
              [Op.and]: [
                {
                  verificationStatus: {
                    [Op.not]: 'Verified',
                  },
                },
                {
                  verificationStatus: {
                    [Op.not]: null,
                  },
                },
              ],
            },
          })
          .then((openAuthSessions) => openAuthSessions.length > 0)
      );
      aPromises.push(
        user
          .getSubAccounts({
            attributes: ['id'],
            // NOTE: 'limit: n' in an include cause Sequelize to compile this
            // query into a query for each subAccount this user has all of which
            // are union'ed together. This is fine here because a user only has
            // a short list of subAccounts but if there were more, this gets pretty
            // inefficient.
            // logging: console.log,
            where: {
              closedDt: null,
              // subAccountTypeId: constants.millenniumTrustSubAccountTypeID,
            },
            include: [
              {
                required: false,
                attributes: ['id'],
                model: models.investment,
                where: {
                  cancelledDt: null,
                },
                limit: 1,
              },
            ],
          })
          .then((userWSubAccountsWInvestments) => {
            if (!userWSubAccountsWInvestments) return false;
            if (userWSubAccountsWInvestments.length === 0) return false;
            for (
              let index = 0;
              index < userWSubAccountsWInvestments.length;
              index += 1
            ) {
              const subAcct = userWSubAccountsWInvestments[parseInt(index, 10)];
              if (subAcct.investments.length === 0) return true;
            }
            return false;
          })
      );
      return Promise.all(aPromises).then(
        ([hasOpenAuthSession, hasSubAccountWithoutInvestments]) =>
          hasOpenAuthSession || hasSubAccountWithoutInvestments
      );
    },
    iraNextStep: async (user, args, { models }) => {
      // Step 1) Is contact verification complete
      const openAuthSessions = await user.getMillenniumTrustAuthSessions({
        where: {
          [Op.and]: [
            {
              verificationStatus: {
                [Op.not]: 'Verified',
              },
            },
            {
              verificationStatus: {
                [Op.not]: null,
              },
            },
          ],
        },
      });
      // NOTE: There should only be one of these
      try {
        for (let index = 0; index < openAuthSessions.length; index += 1) {
          const authSession = openAuthSessions[parseInt(index, 10)];
          // eslint-disable-next-line no-await-in-loop
          const verificationStatus = await getAuthSessionStatus(authSession);
          if (verificationStatus.status === 'OTP Sent') {
            return {
              code: 'otpVerificationFailed',
              codeSince: authSession.verificationStatusUpdatedDt,
            };
          }
          const verificationDocumentsAwaitingUpload =
            verificationStatus?.requiredDocuments?.filter(
              (d) => d.status === 'Awaiting Documentation'
            );
          if (verificationDocumentsAwaitingUpload.length > 0) {
            return {
              code: 'verificationDocumentsRequired',
              codeSince: authSession.verificationStatusUpdatedDt,
            };
          }
        }
      } catch (error) {
        return null;
      }

      // Step 2) Is account funded
      // Step 3) Is funded account invested
      const subAccounts = await user.getSubAccounts({
        attributes: ['id', 'createdAt'],
        where: {
          subAccountTypeId: constants.millenniumTrustSubAccountTypeID,
          closedDt: null,
        },
        include: [
          {
            required: false,
            attributes: ['id'],
            model: models.investment,
            where: {
              cancelledDt: null,
            },
          },
          {
            required: false,
            attributes: ['id', 'amount'],
            model: models.millenniumTrustFundingSession,
            where: {
              toMillenniumTransferCancelledDt: null,
            },
          },
        ],
      });
      for (let index = 0; index < subAccounts.length; index += 1) {
        const subAccount = subAccounts[parseInt(index, 10)];
        if (subAccount.investments.length > 0) continue;
        if (subAccount.accountStatus === 'Pending') continue;
        if (subAccount.millenniumTrustFundingSessions.length === 0) {
          return {
            code: 'fundAccount',
            codeSince: subAccount.createdAt,
            subAccountId: subAccount.id,
          };
        }

        // NOTE: Use the following logic when we handle this case on the front end
        // const fundingTransfersValue = subAccount.millenniumTrustFundingSessions.reduce((acc, cur) => acc + parseFloat(cur.amount), 0)
        // if (fundingTransfersValue > 0) {
        //   return {
        //     code: 'invest'
        // codeSince: TODO:
        //   }
        // }
      }
      return null;
    },
    hubSpotLeadSource: (user) => user.getHubSpotLeadSource(),
    modalAlerts: (user, args, context) =>
      UserService.getModalAlerts(user, args, context),
    initialInvestmentValue: (user) =>
      user
        .getInvestments({
          attributes: ['value', 'blendedProductInvestmentId'],
          where: {
            cancelledDt: null,
          },
          order: [['createdAt', 'ASC']],
          limit: 3,
        })
        .then((investments) => {
          if (!investments || investments.length === 0) {
            return null;
          }
          if (investments[0].blendedProductInvestmentId) {
            // This is a blended product investment. Sum other investments with same blended product investment id
            const blendedProductInvestmentTotal = investments
              .filter(
                (i) =>
                  i.blendedProductInvestmentId ===
                  investments[0].blendedProductInvestmentId
              )
              .reduce((acc, cur) => acc + parseFloat(cur.value), 0);
            return blendedProductInvestmentTotal;
          }
          return parseFloat(investments[0].value);
        }),
    projectedMonthlyAccountValueChartData: async (user, params) => {
      if (params.accountFilter && params.accountFilter.allAccounts === false) {
        return UserService.accountProjectedMonthlyAccountValueChartData(
          user,
          params.accountFilter.subAccountId
        );
      }
      return user.getSubAccounts().then((subAccts) => {
        const consolidatedData = {};
        const subAccountIds = subAccts.map((acct) => acct.id);
        subAccountIds.push(null);
        const subAcctPromises = subAccountIds.map((subAccountId) =>
          UserService.accountProjectedMonthlyAccountValueChartData(
            user,
            subAccountId
          ).then((res) => {
            res.forEach((dataPoint) => {
              if (!consolidatedData[String(dataPoint.date)]) {
                consolidatedData[String(dataPoint.date)] = dataPoint;
              } else {
                consolidatedData[String(dataPoint.date)].nav += parseFloat(
                  dataPoint.nav
                );
                consolidatedData[String(dataPoint.date)].principalInvested +=
                  parseFloat(dataPoint.principalInvested);
                consolidatedData[String(dataPoint.date)].dividendsPaidOut +=
                  parseFloat(dataPoint.dividendsPaidOut);
                consolidatedData[String(dataPoint.date)].equitySoldOff +=
                  parseFloat(dataPoint.equitySoldOff);
              }
            });
          })
        );
        return Promise.all(subAcctPromises).then(() =>
          Object.values(consolidatedData)
        );
      });
    },
    ssnEin: (user) =>
      user.type === 'business' &&
      !(!user.ein && user.businessType === 'soleProprietorship')
        ? user.ein
        : user.ssn,
    tinType: (user) => {
      if (user.id === 76) return '1';
      return user.type === 'business' &&
        !(!user.ein && user.businessType === 'soleProprietorship')
        ? '1'
        : '2';
    },
    millenniumTrustAuthSessions: (user) =>
      user.getMillenniumTrustAuthSessions(),
    dividendData: (user, params, { models }) =>
      user
        .getDividends({
          include: [
            {
              model: models.monthlyPortfolioFinancialActual,
              required: true,
              include: [
                {
                  model: models.portfolio,
                  attributes: ['id', 'subtitle'],
                  required: true,
                },
              ],
            },
          ],
        })
        .then((dividends) => {
          const dateFormat = 'M-YYYY';
          // populate the data object with month string keys and corresponding summed dividends for that month
          const portfolioIds = [];
          const portfolios = [];
          let minDate;
          let maxDate;
          let feesAndCarrySum = 0;
          dividends.forEach((dividend) => {
            if (
              portfolioIds.indexOf(
                dividend.monthlyPortfolioFinancialActual.portfolioId
              ) === -1
            ) {
              portfolioIds.push(
                dividend.monthlyPortfolioFinancialActual.portfolioId
              );
              portfolios[
                String(dividend.monthlyPortfolioFinancialActual.portfolioId)
              ] = {
                dividends: {},
                values: [],
                name: dividend.monthlyPortfolioFinancialActual.portfolio
                  .subtitle,
                id: dividend.monthlyPortfolioFinancialActual.portfolio.id,
              };
            }
            const { date, value } = dividend;
            if (!minDate) {
              minDate = date;
            } else if (date < minDate) {
              minDate = date;
            }
            if (!maxDate) {
              maxDate = date;
            } else if (date > maxDate) {
              maxDate = date;
            }
            const sDateKey = date ? moment(date).format(dateFormat) : 'Unknown';
            if (
              portfolios[
                String(dividend.monthlyPortfolioFinancialActual.portfolio.id)
              ].dividends[String(sDateKey)]
            ) {
              portfolios[
                String(dividend.monthlyPortfolioFinancialActual.portfolio.id)
              ].dividends[String(sDateKey)] +=
                Math.round(parseFloat(value) * 100) / 100;
            } else {
              portfolios[
                String(dividend.monthlyPortfolioFinancialActual.portfolio.id)
              ].dividends[String(sDateKey)] =
                Math.round(parseFloat(value) * 100) / 100;
            }
            const equity =
              value / dividend.monthlyPortfolioFinancialActual.grossCafd;
            feesAndCarrySum +=
              (parseFloat(dividend.monthlyPortfolioFinancialActual.fees) || 0) *
              equity;
            feesAndCarrySum +=
              (parseFloat(dividend.monthlyPortfolioFinancialActual.carry) ||
                0) * equity;
          });
          const range = moment.range(moment(minDate), moment().endOf('month'));
          const months = Array.from(range.by('month')).map((m) =>
            m.format(dateFormat)
          );
          const colors = interpolateColors(portfolios.length - 1);
          if (months.length > 0) {
            months.unshift(
              moment(months[0], dateFormat)
                .subtract(1, 'month')
                .format(dateFormat)
            );
          }
          return {
            dividendTypeId: 'actuals',
            months,
            feesAndCarrySum,
            monthlyDividendPayoutsByPortfolio: portfolios
              .sort((a, b) => a.id - b.id)
              .map((portfolio, index) => {
                let sum = 0;
                const returnObj = portfolio;
                returnObj.color = colors[parseInt(index, 10)];
                months.forEach((sMonth) => {
                  sum +=
                    Math.round(
                      parseFloat(portfolio.dividends[String(sMonth)] || 0) * 100
                    ) / 100;
                  returnObj.values.push(sum);
                });
                return returnObj;
              }),
          };
        }),
    ssnAlreadyExists: (user, args, { models }) => {
      if (!user.ssn || user.dwollaId) return false;
      return models.user
        .count({
          where: {
            dwollaId: { [Op.not]: null },
            type: 'personal',
            ssn: user?.ssn,
          },
        })
        .then((resp) => resp > 0);
    },
    userPortfolioTaxDocuments: (user, args, { models }) =>
      user.getUserPortfolioTaxDocuments({
        include: [
          {
            model: models.portfolio,
            attributes: ['subtitle'],
            required: true,
          },
        ],
      }),
    referralUrl: (user) =>
      `${process.env.HOST}/get-started/referral?r=${
        user.authId || user.oktaId
      }`,
    dividends: (user, args) => {
      const subAccountFilter =
        args.accountFilter && args.accountFilter.allAccounts === false
          ? {
              subAccountId: args.accountFilter.subAccountId,
            }
          : null;
      const { pagination } = args;
      return user.getDividends({
        where: {
          ...subAccountFilter,
        },
        order: [['date', 'DESC']],
        offset: pagination ? pagination.page * pagination.perPage : 0,
        limit: pagination ? pagination.perPage : 1_000_000,
      });
    },
    dividendCount: (user, args, { models }) => {
      const subAccountFilter =
        args.accountFilter && args.accountFilter.allAccounts === false
          ? {
              subAccountId: args.accountFilter.subAccountId,
            }
          : null;
      return models.dividend.count({
        where: {
          userId: user.id,
          ...subAccountFilter,
        },
      });
    },
    documents: async (user, args, { models }) => {
      const returnObj = [];
      const aPromises = [];

      aPromises.push(
        user.getInvestments({
          attributes: [
            'startDt',
            'createdAt',
            'value',
            'eversignDocumentId',
            'documentAwsObjectKey',
          ],
          include: [
            {
              model: models.portfolio,
              attributes: ['subtitle'],
              required: true,
            },
            {
              model: models.blendedProductInvestment,
              attributes: ['id'],
              required: false,
              include: [
                {
                  model: models.blendedProduct,
                  attributes: ['name'],
                  required: true,
                },
                {
                  model: models.investment,
                  attributes: ['value'],
                  required: true,
                },
              ],
            },
          ],
          where: {
            cancelledDt: null,
            [Op.or]: [
              {
                eversignDocumentId: {
                  [Op.not]: null,
                },
              },
              {
                documentAwsObjectKey: {
                  [Op.not]: null,
                },
              },
            ],
          },
        })
      );

      aPromises.push(
        user.getAutoReinvestIndicators({
          include: [
            {
              model: models.portfolio,
              attributes: ['subtitle'],
              required: true,
            },
          ],
          attributes: [
            'createdAt',
            'eversignDocumentId',
            'documentAwsObjectKey',
          ],
        })
      );

      aPromises.push(
        user.getAutoInvestSubscriptions({
          where: {
            archivedDt: null,
          },
          include: [
            {
              model: models.portfolio,
              attributes: ['subtitle'],
              required: true,
            },
            {
              model: models.blendedProduct,
              attributes: ['name'],
              required: false,
            },
          ],
          attributes: [
            'createdAt',
            'eversignDocumentId',
            'documentAwsObjectKey',
          ],
        })
      );

      aPromises.push(
        user.getSubAccounts({
          include: [
            {
              model: models.buyDirection,
              required: false,
              attributes: ['amount', 'eversignDocumentId', 'createdAt'],
              where: {
                cancelledAt: null,
              },
            },
            {
              model: models.subAccountType,
              attributes: ['id', 'name'],
            },
            {
              model: models.millenniumTrustFundingSession,
              required: false,
              attributes: [
                // NOTE: user.subAccounts.millenniumTrustFundingSession.offlineTransferDocumentEversignId is
                // too long of a nesting name for Sequelize. Renaming offlineTransferDocumentEversignId as eversignId
                // does the trick here. See https://github.com/sequelize/sequelize/issues/2084
                ['offlineTransferDocumentEversignId', 'eversignId'],
                'createdAt',
              ],
              where: {
                offlineTransferDocumentEversignId: {
                  [Op.not]: null,
                },
                toMillenniumTransferCancelledDt: null,
              },
            },
          ],
        })
      );

      aPromises.push(
        user.getUserPortfolioTaxDocuments({
          include: [
            {
              model: models.portfolio,
              attributes: ['subtitle'],
              required: true,
            },
          ],
          attributes: ['createdAt', 'year', 'awsObjectKey', 'documentType'],
        })
      );

      aPromises.push(
        user.getQuarterlyUserFinancialStatements({
          attributes: ['createdAt', 'quarter', 'awsObjectKey'],
          include: [
            { model: models.subAccount, required: false, attributes: ['id'] },
          ],
        })
      );
      aPromises.push(
        models.potentialCorporateInvestor.findOne({
          where: [
            { email: user.email },
            { investmentAgreementAwsObjectKey: { [Op.not]: null } },
            { investmentAgreementSignedDt: { [Op.not]: null } },
          ],
        })
      );

      const [
        investments,
        autoReinvestIndicators,
        autoInvestSubscriptions,
        subAccounts,
        taxDocuments,
        quarterlyStatements,
        potentialCorporateInvestor,
      ] = await Promise.all(aPromises);

      const subAccountNameMap = {};
      await Promise.all(
        subAccounts.map((subAcct) =>
          subAcct.name.then((subAcctName) => {
            subAccountNameMap[String(subAcct.id)] = subAcctName;
          })
        )
      );

      const blendedProductDocumentIds = [];

      if (potentialCorporateInvestor) {
        const {
          investmentAgreementSignedDt,
          commitmentAmount,
          // investmentAgreementSentDt,
          investmentAgreementAwsObjectKey,
        } = potentialCorporateInvestor;
        returnObj.push({
          type: 'Corporate Raise Investment Agreement',
          createdAt: investmentAgreementSignedDt,
          title: `${moment(investmentAgreementSignedDt).format(
            'M/D/Y'
          )} - Corporate Raise Investment Agreement`,
          subtitle: `Investment of ${numeral(commitmentAmount).format(
            '$0,0.00'
          )} in Energea Global LLC`,
          downloadUrl: getSignedUrl(
            investmentAgreementAwsObjectKey,
            `Energea Corporate Raise - Investment Agreement.pdf`
          ).catch(() => null),
        });
      }

      investments.forEach((investment) => {
        const createdAt = investment.startDt;
        const { documentAwsObjectKey, eversignDocumentId } = investment;
        const type = 'Investment Agreement';
        const title = `${moment(investment.createdAt).format(
          'M/D/Y'
        )} - Investment Agreement`;
        let portfolioLabel = investment.portfolio.subtitle;
        let investmentValue = investment.value;
        if (investment.blendedProductInvestment) {
          const docKey = documentAwsObjectKey || eversignDocumentId;
          if (blendedProductDocumentIds.indexOf(docKey) > -1) {
            return null;
          }
          blendedProductDocumentIds.push(docKey);
          portfolioLabel =
            investment.blendedProductInvestment.blendedProduct.name;
          investmentValue =
            investment.blendedProductInvestment.investments.reduce(
              (acc, curVal) => acc + parseFloat(curVal.value),
              0
            );
        }
        const subtitle = `Investment of ${numeral(investmentValue).format(
          '$0,0.00'
        )} in '${portfolioLabel}'`;
        returnObj.push({
          type,
          createdAt,
          title,
          subtitle,
          // TODO: add error handling for null autoInvestSubscription.eversignId
          downloadUrl: documentAwsObjectKey
            ? getSignedUrl(
                documentAwsObjectKey,
                `${portfolioLabel} - Investment Agreement.pdf`
              ).catch(() => null)
            : EversignService.getDocumentDownloadUrl(eversignDocumentId),
        });
      });

      autoReinvestIndicators.forEach((autoReinvestIndicator) => {
        const type = 'Dividend-Reinvestment Agreement';
        const title = `${moment(autoReinvestIndicator.createdAt).format(
          'M/D/Y'
        )} - Dividend-Reinvestment Agreement`;
        const subtitle = `Dividend-Reinvestment Agreement for '${autoReinvestIndicator.portfolio.subtitle}'`;
        returnObj.push({
          type,
          createdAt: autoReinvestIndicator.createdAt,
          title,
          subtitle,
          // TODO: add error handling for null autoInvestSubscription.eversignId
          downloadUrl: autoReinvestIndicator.documentAwsObjectKey
            ? getSignedUrl(
                autoReinvestIndicator.documentAwsObjectKey,
                `Dividend-Reinvestment Agreement.pdf`
              ).catch(() => null)
            : EversignService.getDocumentDownloadUrl(
                autoReinvestIndicator.eversignDocumentId
              ),
        });
      });

      autoInvestSubscriptions.forEach((autoInvestSubscription) => {
        const type = 'Auto-Invest Subscription Agreement';
        const title = `${moment(autoInvestSubscription.createdAt).format(
          'M/D/Y'
        )} - Auto-Invest Subscription Agreement`;
        let portfolioLabel = autoInvestSubscription.portfolio.subtitle;
        if (autoInvestSubscription.blendedProduct) {
          const docKey =
            autoInvestSubscription.documentAwsObjectKey ||
            autoInvestSubscription.eversignDocumentId;
          if (blendedProductDocumentIds.indexOf(docKey) > -1) {
            return null;
          }
          blendedProductDocumentIds.push(docKey);
          portfolioLabel = autoInvestSubscription.blendedProduct.name;
        }
        const subtitle = `Auto-Invest Subscription Agreement for '${portfolioLabel}'`;
        returnObj.push({
          type,
          createdAt: autoInvestSubscription.createdAt,
          title,
          subtitle,
          // TODO: add error handling for null autoInvestSubscription.eversignId
          downloadUrl: autoInvestSubscription.documentAwsObjectKey
            ? getSignedUrl(
                autoInvestSubscription.documentAwsObjectKey,
                `Auto-Invest Subscription Agreement.pdf`
              ).catch(() => null)
            : EversignService.getDocumentDownloadUrl(
                autoInvestSubscription.eversignDocumentId
              ),
        });
      });

      subAccounts.forEach((subAccount) => {
        const {
          millenniumTrustFundingSessions,
          buyDirections,
          subAccountType,
        } = subAccount;
        let accountId = '';
        if (subAccount.subAccountType) {
          accountId = subAccount.accountId;
        }
        buyDirections.forEach((buyDirection) => {
          const type = 'Buy Direction';
          const title = `${moment(buyDirection.createdAt).format(
            'M/D/Y'
          )} - Buy Direction`;
          const subtitle = `Transfer of ${numeral(buyDirection.amount).format(
            '$0,0.00'
          )} to '${subAccountType.name} - ${accountId}'`;
          returnObj.push({
            type,
            createdAt: buyDirection.createdAt,
            title,
            subtitle,
            // TODO: add error handling for null autoInvestSubscription.eversignId
            downloadUrl: EversignService.getDocumentDownloadUrl(
              buyDirection.eversignDocumentId
            ),
          });
        });

        const subAccountName = subAccountNameMap[String(subAccount.id)];
        millenniumTrustFundingSessions.forEach((fundingSession) => {
          const type = 'IRA Transfer Form';
          const title = `${moment(fundingSession.createdAt).format(
            'M/D/Y'
          )} - IRA Transfer`;
          const subtitle = `IRA Transfer/Rollover to '${subAccountName}'`;
          returnObj.push({
            type,
            createdAt: fundingSession.createdAt,
            title,
            subtitle,
            // TODO: add error handling for null autoInvestSubscription.eversignId
            downloadUrl: EversignService.getDocumentDownloadUrl(
              fundingSession.dataValues.eversignId
            ),
          });
        });
      });

      taxDocuments.forEach((taxDocument) => {
        const type = 'Tax Document';
        const title = `${moment(taxDocument.createdAt).format('M/D/Y')} - ${
          taxDocument.documentType
        } Tax Document`;
        const subtitle = `${taxDocument.year} Tax Document for '${taxDocument.portfolio.subtitle}'`;
        returnObj.push({
          type,
          createdAt: taxDocument.createdAt,
          title,
          subtitle,
          downloadUrl: getSignedUrl(
            process.env.NODE_ENV === 'production'
              ? taxDocument.awsObjectKey
              : 'TaxDocuments/1099s/2024/1-100_Richard_Reinhard_1099-DIV_2024.pdf',
            `${taxDocument.year} ${taxDocument.portfolio.subtitle} ${taxDocument.documentType}`
          ).catch(() => null),
        });
      });

      quarterlyStatements.forEach((quarterlyStatement) => {
        const type = 'Quarterly statement';
        const title = `${moment(quarterlyStatement.createdAt).format(
          'M/D/Y'
        )} - ${type}`;
        let subtitle = `${quarterlyStatement.quarter} Statement`;
        if (quarterlyStatement.subAccount) {
          const subAccountName =
            subAccountNameMap[String(quarterlyStatement.subAccount.id)];
          if (subAccountName) {
            subtitle += ` - ${subAccountName}`;
          }
        }
        returnObj.push({
          type,
          createdAt: quarterlyStatement.createdAt,
          title,
          subtitle,
          downloadUrl: getSignedUrl(quarterlyStatement.awsObjectKey).catch(
            () => null
          ),
        });
      });
      returnObj.sort((a, b) => (a.createdAt < b.createdAt ? 1 : -1));
      return returnObj.map((el, index) => ({
        ...el,
        id: index,
      }));
    },
    curTaxDocuments: async (user, args, { models }) => {
      const returnObj = [];
      const taxDocuments = await user.getUserPortfolioTaxDocuments({
        where: {
          year: constants.taxYear,
        },
        include: [
          {
            model: models.portfolio,
            attributes: ['subtitle'],
            required: true,
          },
        ],
        attributes: ['createdAt', 'year', 'awsObjectKey', 'documentType'],
      });

      taxDocuments.forEach((taxDocument) => {
        const type = 'Tax document';
        const title = `${taxDocument.documentType} Tax Document`;
        const subtitle = `${taxDocument.year} ${taxDocument.portfolio.subtitle} Tax Document`;
        returnObj.push({
          id: taxDocument.id,
          type,
          createdAt: taxDocument.createdAt,
          title,
          subtitle,
          downloadUrl: getSignedUrl(
            process.env.NODE_ENV === 'production'
              ? taxDocument.awsObjectKey
              : 'TaxDocuments/1099s/2024/1-100_Richard_Reinhard_1099-DIV_2024.pdf',
            `${taxDocument.year} ${taxDocument.portfolio.subtitle} ${taxDocument.documentType}`
          ),
        });
      });

      return returnObj;
    },
    requiresBeneficialOwnership: (user) =>
      UserService.getRequiresBeneficialOwnership(user),

    beneficialOwnershipIncomplete: (user) =>
      UserService.getBeneficialOwnershipIncomplete(user),
    taxDocuments: async (user, args, { models }) => {
      const returnObj = [];
      const taxDocuments = await user.getUserPortfolioTaxDocuments({
        include: [
          {
            model: models.portfolio,
            attributes: ['subtitle'],
            required: true,
          },
        ],
        attributes: ['createdAt', 'year', 'awsObjectKey', 'documentType'],
      });

      taxDocuments.forEach((taxDocument) => {
        const type = 'Tax Document';
        const title = `${moment(taxDocument.createdAt).format('M/D/Y')} - ${
          taxDocument.documentType
        } Tax Document`;
        const subtitle = `${taxDocument.year} ${taxDocument.portfolio.subtitle} Tax Document`;
        returnObj.push({
          type,
          createdAt: taxDocument.createdAt,
          title,
          subtitle,
          downloadUrl: getSignedUrl(
            taxDocument.awsObjectKey,
            `${taxDocument.year} ${taxDocument.portfolio.subtitle} ${taxDocument.documentType}`
          ),
        });
      });

      return returnObj;
    },
    dwollaBalance: DwollaService.dwollaBalance,
    dwollaBalanceFundingSource: DwollaService.dwollaBalanceFundingSource,
    dwollaBankFundingSources: DwollaService.dwollaBankFundingSources,
    dwollaBeneficialOwners: (user) => {
      if (!user.dwollaId || user.type !== 'business') return null;
      return dwolla
        .get(
          `customers/${user.dwollaId}/beneficial-owners`,
          {
            // id: user.dwollaId
          },
          {
            // id: user.dwollaId
          }
        )
        .then(
          (res) => {
            const beneficialOwners = res.body;
            if (!beneficialOwners) return null;
            return beneficialOwners._embedded['beneficial-owners'];
          },
          (e) =>
            console.error(
              `Error retrieving beneficial owners for dwollaId: ${user.dwollaId}`,
              e
            )
        );
    },
    dwollaCustomer: (user, args, { isReviewUser }) => {
      if (!user.dwollaId) return null;
      const dwollaClient = isReviewUser ? stagingDwolla : dwolla;
      return dwollaClient
        .get(
          `customers/${user.dwollaId}`,
          {
            // id: user.dwollaId
          },
          {
            // id: user.dwollaId
          }
        )
        .then(
          (res) => {
            const attrs = [
              'id',
              'firstName',
              'lastName',
              'email',
              'type',
              'status',
              'created',
              'businessName',
              'ipAddress',
              'correlationId',
              'address1',
              'address2',
              'city',
              'state',
              'postalCode',
              'phone',
              'controller',
            ];
            const customer = res.body;
            if (!customer) return null;
            const data = {};
            attrs.forEach((attr) => {
              data[String(attr)] = customer[String(attr)];
              if (attr === 'created') {
                data[String(attr)] = new Date(customer[String(attr)]);
              }
              if (attr === 'controller' && data[String(attr)]) {
                data[
                  String(attr)
                ].fullName = `${customer.firstName} ${customer.lastName}`;
              }
            });
            data.customerUrl = `https://dashboard${
              process.env.NODE_ENV !== 'production' ? '-sandbox' : ''
            }.dwolla.com/customers/${user.dwollaId}`;
            // determine the document status requirements
            data.businessAndController = null;
            if (customer._links['verify-business-with-document']) {
              data.documentVerificationType = 'business';
            } else if (customer._links['verify-with-document']) {
              data.documentVerificationType = 'controller';
            } else if (
              customer._links['verify-controller-and-business-with-document']
            ) {
              data.documentVerificationType = 'businessAndController';
            }

            // Sync verifiedDt if user is not marked as verified but Dwolla customer is verified
            if (!user.verifiedDt && customer.status === 'verified') {
              // Update only the verifiedDt field asynchronously to avoid performance impact and race conditions
              setImmediate(async () => {
                try {
                  await user.update({ verifiedDt: new Date() });
                } catch (error) {
                  console.error('Error syncing user verifiedDt:', error);
                }
              });
            }

            return data;
          },
          () => null
        );
    },
    todDocuments: (user) => user.getTodDocuments(),
    millenniumTrustVerificationDocuments: (user) =>
      user.getAuthenticationDocuments({
        where: {
          millenniumTrustAttachment: {
            [Op.not]: null,
          },
        },
      }),
    beneficialOwnerDwollaDocuments:
      DwollaService.beneficialOwnerDwollaDocuments,
    dwollaDocuments: DwollaService.dwollaDocuments,
    dwollaFundingSources: DwollaService.dwollaFundingSources,
    frontendFundingSources: async (user, args, context) => {
      const returnObj = [];

      // Fetch funding sources and subAccounts concurrently
      const [dwollaFundingSources, subAccounts] = await Promise.all([
        DwollaService.dwollaFundingSources(user, args, context),
        user.getSubAccounts({ where: { closedDt: null } }),
      ]);

      // Helper function to format funding source data
      const formatFundingSource = (source) => ({
        id: source.id,
        fromAccountId: source.id,
        label:
          source.type === 'balance'
            ? 'Energea Wallet'
            : `${source.name} - ${source.bankName}`,
        iconClass:
          source.type === 'balance'
            ? constants.iconClasses.energeaLogo
            : constants.iconClasses.bank,
        status:
          source.id === '84e5fe3a-4033-405d-a10b-81b0682bd667'
            ? 'unverified'
            : source.status,
        balance:
          source.type === 'balance'
            ? parseFloat(source?.balance?.value || 0)
            : null,
        type: source.type,
        bankName: source.bankName,
        accountName: source.name,
        dwollaFundingSource: source,
      });

      // Map and process dwollaFundingSources concurrently NOTE: do this before subAccounts so that the awaits happen
      const dwollaSources = await Promise.all(
        dwollaFundingSources.map(async (sourcePromise) => {
          const source = await sourcePromise;
          return formatFundingSource(source);
        })
      ).catch((e) => {
        console.error('Error formatting dwolla funding sources', e);
        return [];
      });

      // Process subAccounts
      const subAccountSources = await Promise.all(
        subAccounts.map(async (subAccount) => {
          const isEnergeaIRA =
            subAccount.subAccountTypeId ===
            constants.millenniumTrustSubAccountTypeID;
          return {
            id: `${subAccount.id}-subAccount`, // NOTE: must be non-null and different from "dwollaIds"
            fromAccountId: isEnergeaIRA
              ? null
              : dwollaSources.filter(
                  (dwollaSource) => dwollaSource.type === 'balance'
                )?.[0]?.id,
            subAccountId: subAccount.id,
            type: isEnergeaIRA ? 'inspiraSubAccount' : 'subAccount',
            label: subAccount.name,
            balance: isEnergeaIRA
              ? await getMTCEstimatedInvestableBalance(subAccount.id)
              : await DwollaService.getDwollaLabel(
                  subAccount.dwollaLabelId,
                  context.isReviewUser
                ).then((subAccountLabel) =>
                  parseFloat(subAccountLabel?.amount?.value || 0)
                ),
            iconClass: constants.iconClasses.ira,
            status: 'verified',
          };
        })
      );

      returnObj.push(...subAccountSources);

      const sortedDwollaSources = dwollaSources.sort((a, b) => {
        if (a.type === 'balance' && a.balance > 0) return -1; // First: balance with positive balance
        if (b.type === 'balance' && b.balance > 0) return 1;
        if (a.type === 'balance' && a.balance === 0) return 1; // Last: balance with zero balance
        if (b.type === 'balance' && b.balance === 0) return -1;

        return 0; // Middle: everything else
      });
      returnObj.push(...sortedDwollaSources);

      return returnObj.map((fundingSrcWithoutReceivingAccount) => {
        const fundingSrc = fundingSrcWithoutReceivingAccount;
        fundingSrc.isPreferredReceivingAccount =
          user.preferredReceivingAccount &&
          fundingSrc.fromAccountId === user.preferredReceivingAccount;
        return fundingSrc;
      });
    },
    allSellData: (user) => UserService.allSellData(user),
    sellData: (user, { portfolioId }) =>
      UserService.sellData({
        user,
        portfolioId,
        subAccountId: null,
      }),
    estimatedIRR: UserService.estimatedIRR,
    productionEnabled: UserService.cleanEnergyProduced,
    projectedProductionEnabled: UserService.lifetimeCleanEnergyEnabled,
    impactEquivalents: async (user, args, context) => {
      const production = await UserService.cleanEnergyProduced(
        user,
        args,
        context
      );
      return {
        production: production || 0,
        homesPoweredForOneYear: !production ? 0 : production * 0.0893,
        smartPhonesCharged: !production ? 0 : production * 86206,
        treesPlanted: !production ? 0 : production * 11.72,
        tonsOfCarbonEmissionsReduced: !production ? 0 : production * 0.709,
      };
    },
    projectedImpactEquivalents: async (user, args, context) => {
      const production = await UserService.lifetimeCleanEnergyEnabled(
        user,
        args,
        context
      );
      return {
        production: production || 0,
        homesPoweredForOneYear: !production ? 0 : production * 0.0893,
        smartPhonesCharged: !production ? 0 : production * 86206,
        treesPlanted: !production ? 0 : production * 11.72,
        tonsOfCarbonEmissionsReduced: !production ? 0 : production * 0.709,
      };
    },
    eventNotifications: (user, args) =>
      user.getEventNotifications({
        where: {
          deletedFlg: {
            [Op.not]: true,
          },
        },
        order: [['createdAt', 'DESC']],
        limit: args.limit || 20,
      }),
    unreadNotificationCount: (user, args, { models }) =>
      models.eventNotification.count({
        attributes: [],
        where: {
          deletedFlg: {
            [Op.not]: true,
          },
          readDt: null,
          userId: user.id,
        },
      }),
    firstInvestmentDt: UserService.firstInvestmentDt,
    firstInvestmentId: UserService.firstInvestmentId,
    hubSpotContactUrl: (user) =>
      user.hubSpotContactId
        ? `https://app.hubspot.com/contacts/********/contact/${user.hubSpotContactId}/`
        : null,
    nav: UserService.getNAV,
    roi: UserService.getROI,
    nav30DaysAgo: async (user, args) => {
      const endDt = moment().add(-30, 'days');
      const lintedArgs = args || {};
      lintedArgs.endDt = endDt;
      return UserService.getNAV(user, lintedArgs);
    },
    navBasedIRRExcludingEarlyExitPenalties: (user, args) =>
      UserService.navBasedIRR(
        { user, grossOfPenaltiesFlg: true },
        args?.accountFilter
          ? {
              accountFilter: args?.accountFilter,
            }
          : {}
      ),
    navBasedIRR: (user, args) =>
      UserService.navBasedIRR(
        { user },
        args?.accountFilter
          ? {
              accountFilter: args?.accountFilter,
            }
          : {}
      ),
    navBasedIRR30DaysAgo: (user, args) =>
      UserService.navBasedIRR(
        { user },
        args?.accountFilter
          ? {
              accountFilter: args?.accountFilter,
              endDt: new Date(moment().add(-30, 'days')),
            }
          : {
              dateFilter: moment().add(-30, 'days').format('YYYY-MM-DD'),
              endDt: new Date(moment().add(-30, 'days')),
            }
      ),
    notifications: (user) =>
      user.getNotifications({
        where: {
          hideFlg: {
            [Op.not]: true,
          },
        },
      }),
    // oktaUserRecentLocations: async (user) => {
    //   if (!user || !user.oktaId) return null;
    //   const locations = [];
    //   const ips = [];
    //   await oktaClient
    //     .getLogs({
    //       since: moment().add(-1, 'month').format(),
    //       // until: default is right now
    //       filter: `actor.id eq "${user.oktaId}"`,
    //       // limit: default is 100
    //     })
    //     .each((logEvent) => {
    //       // NOTE: ipChain is a list but I haven't found a logEvent with a list length != 1
    //       if (logEvent.request.ipChain.length === 0) {
    //         locations.push({
    //           ip: 'Unknown',
    //         });
    //       } else if (logEvent.request.ipChain?.[0]?.geographicalContext) {
    //         locations.push({
    //           ip: 'Unknown',
    //         });
    //       } else {
    //         // NOTE: added this 'else' clause because sometimes the ipChain list length is 0 so it throws an error when destructuring below
    //         const { ip, geographicalContext } = logEvent.request.ipChain[0];
    //         if (ips.indexOf(ip) === -1) {
    //           ips.push(ip);
    //           locations.push({
    //             ip,
    //             city: geographicalContext?.city,
    //             state: geographicalContext?.state,
    //             country: geographicalContext?.country,
    //             postalCode: geographicalContext?.postalCode,
    //             latitude:
    //               (geographicalContext?.geolocation?.lat &&
    //                 String(geographicalContext?.geolocation?.lat)) ||
    //               null,
    //             longitude:
    //               (geographicalContext?.geolocation?.lon &&
    //                 String(geographicalContext.geolocation.lon)) ||
    //               null,
    //           });
    //         }
    //       }
    //     });
    //   return locations;
    // },
    oktaUser: async (user) => {
      if (!user || !user.oktaId) return null;
      return oktaClient.getUser(user.oktaId).then(
        async (oktaUser) => {
          const roles = [];
          await oktaUser.listGroups().each((group) => {
            const oGroup = {
              id: group.id,
              name: group.profile.name,
              description: group.profile.description,
            };
            roles.push(oGroup);
          });
          if (!oktaUser) return null;
          const returnObj = oktaUser;
          const {
            profile: { firstName, lastName },
          } = returnObj;
          returnObj.passwordChanged = new Date(returnObj.passwordChanged);
          returnObj.lastLogin = new Date(returnObj.lastLogin);
          returnObj.profile.fullName = `${firstName} ${lastName}`.trim();
          returnObj.profile.id = `okta-profile-${user.oktaId}`;
          returnObj.roles = roles;
          const hasCMSAccessFlg =
            roles.map((role) => role.id).indexOf(oktaGroupDict.CMS) > -1;
          returnObj.hasCMSAccessFlg = hasCMSAccessFlg;
          returnObj.isEnrolledInGoogleAuthenticatorMFA =
            roles
              .map((role) => role.name)
              .indexOf('Users_Google_Authenticator_OptIn') > -1;
          // NOTE: Google Auth takes precedent over SMS in the case that the user is in both groups
          returnObj.isEnrolledInSMSMFA =
            !returnObj.isEnrolledInGoogleAuthenticatorMFA &&
            roles.map((role) => role.name).indexOf('Users_SMS_OptIn') > -1;
          return returnObj;
        },
        (e) => {
          console.error(
            `No Okta User found with id: ${user.oktaId}`.bold.red,
            e
          );
          return new ApolloError('User session has expired', 'AUTH', {
            error: e,
          });
        }
      );
    },
    pendingDwollaBalance: DwollaService.pendingDwollaBalance,
    plaidItems: (user) => user.getPlaidItems(),
    projectMapData: async (user, args, { models }) => {
      const subAccountFilter =
        args.accountFilter && args.accountFilter.allAccounts === false
          ? {
              subAccountId: args.accountFilter.subAccountId,
            }
          : null;
      const aPromises = [];
      aPromises.push(
        user.getInvestments({
          attributes: ['shares', 'portfolioId'],
          where: {
            cancelledDt: null,
            ...subAccountFilter,
          },
          include: [
            {
              attributes: ['id'],
              required: true,
              model: models.portfolio,
              include: [
                {
                  required: true,
                  model: models.project,
                  where: {
                    isPublic: true,
                  },
                },
              ],
            },
          ],
        })
      );
      aPromises.push(
        user.getSellOrders({
          where: {
            ...subAccountFilter,
            cancelledDt: null,
          },
          attributes: ['portfolioId'],
          include: [
            {
              attributes: ['soldShares'],
              model: models.shareTransfer,
              include: [
                {
                  attributes: [],
                  required: true,
                  where: {
                    cancelledDt: null,
                  },
                  model: models.investment,
                },
              ],
            },
          ],
        })
      );

      const [investments, sellOrders] = await Promise.all(aPromises);

      const portfolios = {};
      investments.forEach((investment) => {
        if (portfolios[String(investment.portfolioId)]) {
          portfolios[String(investment.portfolioId)].sharesOwned += parseFloat(
            investment.shares
          );
        } else {
          portfolios[String(investment.portfolioId)] = {
            sharesOwned: parseFloat(investment.shares),
            projects: investment.portfolio.projects,
          };
        }
      });
      sellOrders.forEach((sellOrder) => {
        sellOrder.shareTransfers.forEach((shareTransfer) => {
          if (portfolios[String(sellOrder.portfolioId)]) {
            portfolios[String(sellOrder.portfolioId)].sharesOwned -= parseFloat(
              shareTransfer.soldShares
            );
          } else {
            portfolios[String(sellOrder.portfolioId)] = {
              sharesOwned: parseFloat(-1 * shareTransfer.soldShares),
              projects: [],
            };
          }
        });
      });

      const aPortfolioIds = [];
      const projects = [];
      Object.keys(portfolios).forEach((portfolioId) => {
        if (portfolios[String(portfolioId)].sharesOwned > 0) {
          aPortfolioIds.push(String(portfolioId));
          portfolios[String(portfolioId)].projects.forEach((project) => {
            projects.push(project);
          });
        }
      });

      const colors = interpolateColors(aPortfolioIds.length);
      const res = [];
      projects.forEach((project) => {
        const lintedProject = project;
        lintedProject.color =
          colors[aPortfolioIds.indexOf(String(project.portfolioId))];
        res.push(lintedProject);
      });

      return res;
    },
    pendingSellOrders: (user, args) => {
      const subAccountFilter =
        args.accountFilter && args.accountFilter.allAccounts === false
          ? {
              subAccountId: args.accountFilter.subAccountId,
            }
          : null;
      return user.getSellOrders({
        where: {
          closedDt: null,
          cancelledDt: null,
          ...subAccountFilter,
        },
      });
    },
    sellOrders: (user) => user.getSellOrders({ where: { cancelledDt: null } }),
    soldShareValue: UserService.soldShareValue,
    showCommunityPropertyStateWarning: async (user) => {
      if (!user || !user.type || user.type !== 'personal') return false;
      const communityPropertyStates = [
        'AZ',
        'CA',
        'ID',
        'LA',
        'NV',
        'NM',
        'TX',
        'WA',
        'WI',
      ];
      if (communityPropertyStates.indexOf(user.state) > -1) {
        return true;
      }
      return false;
    },
    showOnboardingWizard: async (user) => {
      const requiredDbFieldsPersonal = [
        'address1',
        'city',
        'postalCode',
        'state',
        'countryCode',
        'confirmationDt',
        'citizenshipCountryCode',
        'dateOfBirth',
        'type',
        'isAccredited',
        'ssn',
        'primaryPhone',
      ];
      const requiredDbFieldsBusiness = [
        'businessType',
        'businessClassification',
        'businessName',
      ];

      let result = false;
      // return true;
      const requiredFields =
        user.type === 'business'
          ? requiredDbFieldsPersonal.concat(requiredDbFieldsBusiness)
          : requiredDbFieldsPersonal;
      for (let index = 0; index < requiredFields.length; index++) {
        const field = requiredFields[String(index)];
        if (
          field !== 'confirmationDt' &&
          ((!user[String(field)] && user[String(field)] !== false) ||
            user[String(field)] === '')
        ) {
          result = true;
          break;
        }
      }
      if (!user.isAccredited) {
        if (
          (user.netWorth !== 0 && !user.netWorth) ||
          user.netWorth === '' ||
          (user.annualIncome !== 0 && !user.annualIncome) ||
          user.annualIncome === ''
        ) {
          result = true;
        }
      }
      if (user.type === 'business' && !user.dwollaId) {
        result = true;
      }
      return result;
    },
    userPortfoliosInvested: UserService.portfoliosCurrentlyInvested,
    userPortfolioInvestments: async (user, params) =>
      UserService.getUserPortfolioInvestments(user, params),
    userPortfolioInvestmentNAVs: async (user, params) =>
      UserService.getUserPortfolioInvestmentNAVs(user, params),
    auth0Enrollments: async (user) => {
      if (!user || !user.authId) return null;
      const auth0Enrollments = await getEnrollments(user.authId).then(
        (resp) => resp.data
      );
      if (!auth0Enrollments || auth0Enrollments.length === 0) return null;
      const enrollmentDict = {
        authenticator: {
          label: 'Authenticator App',
          icon: 'fa-regular fa-qrcode',
          description:
            'Use an authenticator app like Google Authenticator, Authy, or Microsoft Authenticator to generate secure, time-based codes for login.',
        },
        phone: {
          icon: 'fa-solid fa-comment-sms',
          label: 'Phone (SMS)',
          description: 'Receive a text message with a code',
        },
        sms: {
          icon: 'fa-solid fa-comment-sms',
          label: 'Phone (SMS)',
          description: 'Receive a text message with a code',
        },
      };
      const statusDict = {
        confirmed: 'Currently Enrolled',
        pending:
          'Enrollment Pending - You will be prompted to complete the process when you next log in.',
      };
      return auth0Enrollments.map((enrollment) => {
        const label =
          enrollmentDict[String(enrollment.type)]?.label || 'Unknown Factor';
        const iconClass =
          enrollmentDict[String(enrollment.type)]?.icon ||
          'fa-solid fa-question';
        const returnObj = {
          ...enrollment,
          iconClass,
          label,
          statusLabel:
            statusDict[String(enrollment.status)] ||
            enrollmentDict[String(enrollment.type)]?.description ||
            'Unknown Status',
        };
        return returnObj;
      });
    },
    userPortfolioIRRs: async (user, args, context) => {
      const { models } = context;

      // Filters
      const subAccountFilter =
        args.accountFilter?.allAccounts === false
          ? { subAccountId: args.accountFilter.subAccountId }
          : null;

      const portfolioFilter = args.portfolioId
        ? { portfolioId: args.portfolioId }
        : null;

      // Helper to fetch IRR data
      const fetchPortfolioIRR = async (portfolioId) => {
        const [showIRR, navBasedIRR] = await Promise.all([
          UserService.showIRR(
            user,
            { portfolioId, accountFilter: subAccountFilter },
            { models }
          ),
          UserService.navBasedIRR(
            { user },
            { portfolioId, accountFilter: subAccountFilter }
          ),
        ]);

        const accountKey =
          args.accountFilter?.allAccounts !== false
            ? 'all'
            : args.accountFilter.subAccountId || 'ind';

        return {
          id: `${portfolioId}-${accountKey}`,
          portfolioId,
          showIRR,
          navBasedIRR,
        };
      };

      // If a specific portfolio is requested, return its IRR
      if (portfolioFilter) {
        return [await fetchPortfolioIRR(args.portfolioId)];
      }

      // Otherwise, get IRRs for all invested portfolios
      const investedPortfolios = await UserService.portfoliosCurrentlyInvested(
        user,
        args,
        context
      );

      const irrPromises = investedPortfolios.map((portfolio) =>
        fetchPortfolioIRR(portfolio.id)
      );

      return Promise.all(irrPromises);
    },
    userPortfolioCOCYields: async (user, args, context) => {
      // Filters
      const subAccountFilter =
        args.accountFilter?.allAccounts === false
          ? { subAccountId: args.accountFilter.subAccountId }
          : null;

      const portfolioFilter = args.portfolioId
        ? { portfolioId: args.portfolioId }
        : null;

      // Helper to fetch COC Yield data
      const fetchPortfolioCOCYield = async (portfolioId) => {
        const annualizedCOCYield = await UserService.principalBasedIRR(
          { user },
          { portfolioId, accountFilter: subAccountFilter }
        );

        const accountKey =
          args.accountFilter?.allAccounts !== false
            ? 'all'
            : args.accountFilter.subAccountId || 'ind';

        return {
          id: `${portfolioId}-${accountKey}`,
          portfolioId,
          annualizedCOCYield,
        };
      };

      // If a specific portfolio is requested, return its COC Yield
      if (portfolioFilter) {
        return [await fetchPortfolioCOCYield(args.portfolioId)];
      }

      // Otherwise, get COC Yields for all invested portfolios
      const investedPortfolios = await UserService.portfoliosCurrentlyInvested(
        user,
        args,
        context
      );

      const cocYieldPromises = investedPortfolios.map((portfolio) =>
        fetchPortfolioCOCYield(portfolio.id)
      );

      return Promise.all(cocYieldPromises);
    },
    shareTransferSum: (user) => UserService.getShareTransferSum(user),
    investmentSum: (user, args) => {
      const subAccountFilter =
        args.accountFilter && args.accountFilter.allAccounts === false
          ? {
              subAccountId: args.accountFilter.subAccountId,
            }
          : null;
      return (
        user
          .getInvestments({
            attributes: [[fn('sum', col('value')), 'investmentSum']],
            where: {
              cancelledDt: null,
              ...subAccountFilter,
            },
          })
          .then((investment) => investment?.[0]?.dataValues?.investmentSum) || 0
      );
    },
    principalInvestmentSum: (user, args) =>
      UserService.getPrincipalInvestmentSum(user, args),
    nonReinvestedDividendSum: async (user, args) =>
      UserService.getNonReinvestedDividendSum(user, args),
    dividendSum: UserService.dividendsReceived,
    dividendSum30DaysAgo: (user, args) => {
      const subAccountFilter =
        args.accountFilter && args.accountFilter.allAccounts === false
          ? {
              subAccountId: args.accountFilter.subAccountId,
            }
          : null;
      return user
        .getDividends({
          attributes: [[fn('sum', col('value')), 'dividendSum']],
          where: {
            date: {
              [Op.lt]: moment().add(-30, 'days').toDate(),
            },
            ...subAccountFilter,
          },
        })
        .then((res) => res[0]?.dataValues.dividendSum || 0);
    },
    // primaryPhone: (user) => {
    //   if (!user || !user.oktaId) return null;
    //   return oktaClient.getUser(user.oktaId).then(
    //     async (oktaUser) => {
    //       if (!oktaUser) return null;
    //       return oktaUser.profile.primaryPhone;
    //     },
    //     (e) => {
    //       console.error(
    //         `No Okta User found with id: ${user.oktaId}`.bold.red,
    //         e
    //       );
    //       return new ApolloError('User session has expired', 'AUTH', {
    //         error: e,
    //       });
    //     }
    //   );
    // },
    corporateEquityPrincipalInvestmentSum: (user, args, { models }) =>
      models.corporateShareAllocation
        .sum('value', {
          where: {
            userId: user.id,
            startDt: {
              [Op.not]: null,
            },
          },
        })
        .then((res) => res || 0),
    corporateEquitySharesOwned: (user, args, { models }) =>
      models.corporateShareAllocation
        .sum('shares', {
          where: {
            userId: user.id,
            startDt: {
              [Op.not]: null,
            },
          },
        })
        .then((res) => res || 0),
    corporateInvestorDashboardChartData: async (user, args, { models }) => {
      const [shareAllocations, energeaGlobalSharePrices] = await Promise.all([
        user.getCorporateShareAllocations({
          attributes: ['id', 'shares', 'startDt'],
          where: {
            startDt: {
              [Op.not]: null,
            },
          },
          order: [['startDt', 'ASC']],
        }),
        models.energeaGlobalSharePrice.findAll({
          attributes: ['id', 'sharePrice', 'date'],
          where: {
            date: {
              [Op.not]: null,
            },
          },
          order: [['date', 'ASC']],
        }),
      ]);

      const navEvents = [];
      shareAllocations.forEach((allocation) => {
        navEvents.push({
          date: moment(allocation.startDt),
          type: 'sharesPurchased',
          value: allocation.shares,
        });
      });
      energeaGlobalSharePrices.forEach((sharePriceChange) => {
        navEvents.push({
          date: moment(sharePriceChange.date),
          type: 'sharePriceChange',
          value: sharePriceChange.sharePrice,
        });
      });
      navEvents.sort((a, b) => (a.date.isBefore(b.date) ? -1 : 1));

      const results = [];
      let currentSharePrice = 0;
      let currentShares = 0;
      navEvents.forEach((event) => {
        if (event.type === 'sharesPurchased') {
          currentShares += event.value;
        }
        if (event.type === 'sharePriceChange') {
          currentSharePrice = event.value;
        }
        if (currentShares === 0) return;
        if (results.length === 0) {
          results.push({
            date: moment(event.date).add(-1, 'day').format('YYYY-MM-DD'),
            marketValue: 0,
            sharePrice: currentSharePrice,
            sharesOwned: 0,
          });
        }
        results.push({
          date: moment(event.date).format('YYYY-MM-DD'),
          marketValue: currentShares * currentSharePrice,
          sharePrice: currentSharePrice,
          sharesOwned: currentShares,
        });
      });
      results.push({
        date: moment().format('YYYY-MM-DD'),
        marketValue: currentShares * currentSharePrice,
        sharePrice: currentSharePrice,
        sharesOwned: currentShares,
      });

      return results;
    },
    corporateShareAllocations: (user) => user.getCorporateShareAllocations(),
    hasCorporateShares: async (user, args, { models }) =>
      models.corporateShareAllocation
        .count({
          where: {
            userId: user.id,
            startDt: {
              [Op.not]: null,
            },
            shares: {
              [Op.gt]: 0,
            },
          },
        })
        .then((res) => res > 0),
    currentYearRemainingInvestmentMax: async (user) => {
      // If they are accredited, there isn't a real max.
      if (user.isAccredited) return null;
      if (user.annualIncome === null && user.netWorth === null) return null;
      const userAnnualMaxPerPortfolio =
        (user.annualIncome || 0) > (user.netWorth || 0)
          ? user.annualIncome * 0.1
          : user.netWorth * 0.1;
      const portfolioIds =
        user.substantiveRelationshipFlg || user.isAccredited
          ? [1, 5, 7, 9]
          : [5, 7, 9];
      let total = 0;
      const portfolioYearTotalInvested = await user
        .getInvestments({
          attributes: [
            'portfolioId',
            [fn('sum', col('value')), 'totalInvested'],
          ],
          where: {
            cancelledDt: null,
            startDt: {
              [Op.gte]: moment().startOf('year'),
            },
          },
          group: 'portfolioId',
        })
        .then((res) => {
          const returnObj = {};
          res.forEach((portfolio) => {
            if (
              user.id !== 76 &&
              portfolioIds.indexOf(portfolio.portfolioId) === -1
            ) {
              SlackService.logToSlack({
                type: 'platform-error',
                title: `User.currentYearRemainingInvestmentMax found unhandled investment in portfolio ${portfolio.portfolioId}`,
              });
            }
            returnObj[String(portfolio.portfolioId)] = parseFloat(
              portfolio.dataValues.totalInvested
            );
          });
          return returnObj;
        });
      portfolioIds.forEach((portfolioId) => {
        total +=
          userAnnualMaxPerPortfolio -
          (portfolioYearTotalInvested[String(portfolioId)] || 0);
      });
      return total;
    },
    investablePortfolios: async (user, params, { models }) => {
      try {
        const aPromises = [];
        aPromises.push(
          models.portfolio.findAll({
            include: [
              {
                model: models.regulation,
                attributes: [
                  'id',
                  'prohibitedStates',
                  'numUnaccreditedInvestors',
                ],
              },
            ],
            where: {
              [Op.and]: [
                {
                  isPublic: true,
                },
                {
                  isAcceptingInvestments: true,
                },
              ],
            },
            order: [['orderNo', 'ASC']],
          })
        );
        aPromises.push(
          UserService.hasSubAccountWithBuyDirection(user, params, {
            models,
          })
        );
        aPromises.push(
          user.getInvestments({
            attributes: ['id'],
            where: {
              cancelledDt: null,
            },
          })
        );
        const [portfolios, hasSubAccountWBuyDirection, investments] =
          await Promise.all(aPromises);
        const userCopy = user;
        userCopy.hasSubAccountWBuyDirection = hasSubAccountWBuyDirection;
        userCopy.hasInvested = investments && investments.length > 0;
        const investablePortfolios = [];
        const promises = [];
        for (let index = 0; index < portfolios.length; index++) {
          const portfolio = portfolios[parseInt(index, 10)];
          const aPromises2 = [];
          // The results of this is needed before calling portfolioCanAcceptInvestments
          aPromises2.push(
            PortfolioService.getCurrentEquityData(portfolio, user)
          );
          promises.push(
            Promise.all(aPromises2).then(async ([currentEquityData]) => {
              portfolio.currentEquityData = currentEquityData;
              if (user && typeof user.isVerified === 'undefined') {
                user.isVerified = await UserService.isVerified(user);
              }
              await portfolioCanAcceptInvestments(portfolio, user).then(
                (portfolioStatus) => {
                  if (portfolioStatus.canInvest) {
                    investablePortfolios.push(portfolio);
                  }
                }
              );
            })
          );
        }
        await Promise.all(promises); // This populates investablePortfolios
        return investablePortfolios;
      } catch (error) {
        return [];
      }
    },
    investments: (user, args) => {
      const subAccountFilter =
        args.accountFilter && args.accountFilter.allAccounts === false
          ? {
              subAccountId: args.accountFilter.subAccountId,
            }
          : null;
      const { pagination } = args;
      return user.getInvestments({
        where: {
          ...subAccountFilter,
        },
        order: [['startDt', 'DESC']],
        offset: pagination ? pagination.page * pagination.perPage : 0,
        limit: pagination ? pagination.perPage : 1_000_000,
      });
    },
    investmentCount: (user, args, { models }) => {
      const subAccountFilter =
        args.accountFilter && args.accountFilter.allAccounts === false
          ? {
              subAccountId: args.accountFilter.subAccountId,
            }
          : null;
      return models.investment.count({
        where: {
          userId: user.id,
          ...subAccountFilter,
        },
      });
    },
    activeInvestments: (user, args) => {
      const subAccountFilter =
        args.accountFilter && args.accountFilter.allAccounts === false
          ? {
              subAccountId: args.accountFilter.subAccountId,
            }
          : null;
      const { pagination } = args;
      return user.getInvestments({
        where: {
          cancelledDt: null,
          ...subAccountFilter,
        },
        order: [['startDt', 'DESC']],
        offset: pagination ? pagination.page * pagination.perPage : 0,
        limit: pagination ? pagination.perPage : 1_000_000,
      });
    },
    activeInvestmentCount: (user, args, { models }) => {
      const subAccountFilter =
        args.accountFilter && args.accountFilter.allAccounts === false
          ? {
              subAccountId: args.accountFilter.subAccountId,
            }
          : null;
      return models.investment.count({
        where: {
          userId: user.id,
          cancelledDt: null,
          ...subAccountFilter,
        },
      });
    },
    nextProjectedPortfolioDividendDates: async (user, args, { models }) => {
      const subAccountFilter =
        args.accountFilter && args.accountFilter.allAccounts === false
          ? {
              subAccountId: args.accountFilter.subAccountId,
            }
          : null;
      const aPromises = [];
      aPromises.push(
        user.getInvestments({
          attributes: ['portfolioId', 'shares'],
          where: {
            cancelledDt: null,
            ...subAccountFilter,
          },
          include: [
            {
              attributes: ['id', 'subtitle'],
              model: models.portfolio,
            },
          ],
        })
      );
      aPromises.push(
        user.getSellOrders({
          where: {
            ...subAccountFilter,
            cancelledDt: null,
          },
          attributes: ['portfolioId'],
          include: [
            {
              attributes: ['soldShares'],
              model: models.shareTransfer,
              include: [
                {
                  attributes: [],
                  required: true,
                  where: {
                    cancelledDt: null,
                  },
                  model: models.investment,
                },
              ],
            },
            {
              attributes: ['id', 'subtitle'],
              model: models.portfolio,
            },
          ],
        })
      );

      const [investments, sellOrders] = await Promise.all(aPromises);

      const portfolios = {};
      investments.forEach((investment) => {
        if (portfolios[String(investment.portfolioId)]) {
          portfolios[String(investment.portfolioId)].sharesOwned += parseFloat(
            investment.shares
          );
        } else {
          portfolios[String(investment.portfolioId)] = {
            sharesOwned: parseFloat(investment.shares),
            portfolio: investment.portfolio,
          };
        }
      });
      sellOrders.forEach((sellOrder) => {
        sellOrder.shareTransfers.forEach((shareTransfer) => {
          if (portfolios[String(sellOrder.portfolioId)]) {
            portfolios[String(sellOrder.portfolioId)].sharesOwned -= parseFloat(
              shareTransfer.soldShares
            );
          } else {
            portfolios[String(sellOrder.portfolioId)] = {
              sharesOwned: parseFloat(-1 * shareTransfer.soldShares),
              portfolio: sellOrder.portfolio,
            };
          }
        });
      });

      const investedPortfolios = [];
      Object.keys(portfolios).forEach((portfolioId) => {
        // NOTE: if you don 't have 1/1000th of a share then you don't own any meaningful equity
        if (portfolios[String(portfolioId)].sharesOwned > 0.001) {
          investedPortfolios.push(portfolios[String(portfolioId)].portfolio);
        }
      });

      return investedPortfolios.map((investedPortfolio) => ({
        portfolio: investedPortfolio,
        date: PortfolioService.getProjectedNextDividendDt(
          investedPortfolio,
          args,
          {
            models,
          }
        ),
      }));
    },
    nextScheduledPortfolioInvestments: (user, args, { models }) =>
      user
        .getAutoInvestSubscriptions({
          attributes: ['id', 'value', 'portfolioId', 'dayOfMonth'],
          where: {
            inactive: {
              [Op.not]: true,
            },
          },
          include: [
            {
              attributes: ['id', 'subtitle'],
              model: models.portfolio,
            },
          ],
          order: [
            ['portfolioId', 'ASC'],
            ['dayOfMonth', 'ASC'],
          ],
        })
        .then((autoInvestSubscriptions) => {
          const today = new Date();
          const autoInvestTime = new Date(
            `${moment().format('MM-DD-YYYY')} ${
              constants.autoInvestHour
            }:00:00 GMT-0500`
          );
          const autoInvestTimePast = new Date() > autoInvestTime;
          return autoInvestSubscriptions.map((subscription) => {
            const scheduledInvestment = {
              value: parseFloat(subscription.value),
              portfolio: subscription.portfolio,
            };
            if (
              subscription.dayOfMonth > today.getDate() ||
              (!autoInvestTimePast &&
                subscription.dayOfMonth === today.getDate())
            ) {
              scheduledInvestment.date = new Date(
                moment().set('date', subscription.dayOfMonth)
              );
            } else {
              scheduledInvestment.date = new Date(
                moment().add(1, 'month').set('date', subscription.dayOfMonth)
              );
            }
            return scheduledInvestment;
          });
        }),
    roles: async (user, args, { user: contextUser, roles }) => {
      // Format roles to match Okta structure
      let lintedRoles;
      if (contextUser.id !== user.id) {
        lintedRoles = roles;
      } else {
        const auth0User = await getAuth0User(user.authId);
        lintedRoles = auth0User?.data?.user_metadata?.roles || [];
      }
      const formattedRoles = lintedRoles.map((roleName) => ({
        id: `auth0-role-${roleName}`,
        name: roleName,
        description: `Auth0 role: ${roleName}`,
      }));
      return formattedRoles;
    },
    hasCMSAccess: async (user, args, { user: contextUser, roles }) => {
      if (contextUser.id !== user.id) {
        return roles.map((role) => role.name).indexOf('CMS') > -1;
      }
      const auth0User = await getAuth0User(user.authId);
      return auth0User.data?.user_metadata?.roles?.includes('CMS');
    },
    hasAuth0Account: (user) => !!user.authId,
    hasPendingMicroDeposit: async (user, args, { models }) => {
      const plaidItems = await user.getPlaidItems({
        include: [
          {
            model: models.plaidAccount,
          },
        ],
      });
      if (!plaidItems || plaidItems.length === 0) {
        return false;
      }
      let resp = false;
      for (let index = plaidItems.length - 1; index >= 0; index--) {
        const plaidItem = plaidItems[parseInt(index, 10)];
        if (
          !resp &&
          plaidItem.plaidAccounts &&
          plaidItem.plaidAccounts.length > 0
        ) {
          plaidItem.plaidAccounts.forEach((plaidAccount) => {
            if (
              plaidAccount.verification_status === 'pending_manual_verification'
            ) {
              resp = true;
            }
          });
        }
      }
      return resp;
    },
    beneficiaries: (user) =>
      user.getBeneficiaries({
        order: [['primaryFlg', 'DESC']],
      }),
    corporateBeneficiaries: (user) =>
      user.getCorporateBeneficiaries({
        order: [['primaryFlg', 'DESC']],
      }),
    missingBusinessFieldsNo: (user) =>
      UserService.getMissingBusinessFieldsNo(user),
    missingPersonalFieldsNo: (user) =>
      UserService.getMissingPersonalFieldsNo(user),
    missingInvestorFieldsNo: (user) =>
      UserService.getMissingInvestorFieldsNo(user),
    missingSecurityFieldsNo: (user) =>
      UserService.getMissingSecurityFieldsNo(user),
    isVerified: UserService.isVerified,
    showIRR: UserService.showIRR,
    showIRR30DaysAgo: (user, args) => {
      const lintedArgs = args;
      lintedArgs.asOfDate = moment().add(-30, 'days');
      return UserService.showIRR(user, args);
    },
    hasSubAccountWBuyDirection: UserService.hasSubAccountWithBuyDirection,
    images: (user) => user.getImages(),
    image_ids: (user) =>
      user.getImages().then((images) => images.map((image) => image.id)),
    primaryImage: (user) =>
      user
        .getImages({
          where: {
            primaryFlg: true,
          },
          order: [['id', 'DESC']],
          limit: 1,
        })
        .then((images) => {
          if (!images || images.length < 1) return null;
          return images[0];
        }),
    avatarImageUrl: (user) =>
      user
        .getImages({
          attributes: ['public_id'],
          where: {
            primaryFlg: true,
          },
          order: [['id', 'DESC']],
          limit: 1,
        })
        .then((images) => {
          if (!images || images.length < 1) return null;
          return cl.url(images[0].public_id, {
            width: constants.imgDims.mobileFullScreen.width,
            crop: 'scale',
            quality: 'auto',
            format: 'WebP',
          });
        }),
    countryName: (parent) => {
      if (!parent.countryCode) return null;
      return countries.find(
        (country) =>
          country.code.toLowerCase() === parent.countryCode.toLowerCase()
      ).name;
    },
    stateName: (parent) => {
      if (!parent.state) return null;
      return states.find(
        (state) => state.code.toLowerCase() === parent.state.toLowerCase()
      ).name;
    },
    openSubAccounts: (user) =>
      user.getSubAccounts({ where: { closedDt: null } }),
    subAccounts: (user) => user.getSubAccounts(),
    transfers: (user) => user.getTransfers(),
    referrals: (user, args, { models }) =>
      models.referral.findAll({
        where: {
          referrerId: user.id,
        },
      }),
    referrer: (user, args, { models }) =>
      models.referral
        .findOne({
          where: {
            refereeId: user.id,
          },
          include: [
            {
              model: models.user,
              as: 'referrer',
              attributes: ['id', 'firstName', 'lastName'],
              required: true,
            },
          ],
        })
        .then((referral) => referral?.referrer || null),
    referralRewardInvestment: (user, args, { models }) =>
      user
        .getInvestments({
          where: {
            cancelledDt: null,
          },
          include: [
            {
              attributes: [],
              model: models.referral,
              where: {
                refereeId: user.id,
              },
              required: true,
            },
          ],
          limit: 1,
        })
        .then((resp) => resp?.[0] || null),
    totalReceivedFromReferrals: (user) =>
      user
        .getInvestments({
          attributes: [[fn('sum', col('value')), 'investmentSum']],
          where: {
            cancelledDt: null,
            referralId: {
              [Op.not]: null,
            },
          },
        })
        .then(
          (investment) =>
            (investment[0] && investment[0].dataValues.investmentSum) || 0
        ),
    isReferred: (user, args, { models }) =>
      models.referral
        .findOne({
          where: {
            refereeId: user.id,
            deactivatedDt: null,
          },
        })
        .then((referral) => !!referral),
    userEventTypeCommunicationPreferences: (user) =>
      user.getUserEventTypeCommunicationPreferences(),
  },
  Query: {
    allMerchUsers: async (
      parent,
      { filter, sort, pagination, exportConfig },
      { models, roles, user }
    ) => {
      const isAuthorized = getAuthorized('admin', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      const filterClauses = UserService.getUserFeedFilter(filter);

      const sortField = (sort && sort.field) || 'id';
      const sortOrder = (sort && sort.order) || 'DESC';

      if (exportConfig && exportConfig.emailResults) {
        const queue = new Queue('handleListEmailExport', bullOptions);
        await queue.add({
          sort,
          filter,
          exportConfig,
          list: 'user',
        });
        queue.close();
        return {
          rows: [],
          count: 0,
        };
      }

      if (sortField === 'investmentSum') {
        return models.user
          .findAndCountAll({
            group: ['user.id'],
            subQuery: false,
            offset: (pagination.page - 1) * pagination.perPage,
            limit: pagination.perPage,
            include: [
              {
                model: models.investment,
                attributes: [],
                required: false,
                where: {
                  cancelledDt: null,
                },
              },
              {
                model: models.modalAlertEvent,
                attributes: [],
                required: true,
                where: { type: 'retrieveMerchData' },
              },
            ],
            where: filterClauses,
            order: [
              [
                Sequelize.fn('sum', Sequelize.col('investments.value')),
                sortOrder === 'DESC' ? 'DESC NULLS LAST' : 'ASC NULLS FIRST',
              ],
            ],
          })
          .then((userWithInvestments) => {
            const returnObj = userWithInvestments;
            returnObj.count = returnObj.count.length;
            return returnObj;
          });
      }
      return models.user.findAndCountAll({
        order: [[sortField, sortOrder]],
        where: filterClauses,
        include: [
          {
            model: models.modalAlertEvent,
            attributes: [],
            required: true,
            where: { type: 'retrieveMerchData' },
          },
        ],
        offset: (pagination.page - 1) * pagination.perPage,
        limit: pagination.perPage,
      });
    },
    getDailyActiveUserData: async (parent, args, { models, roles, user }) => {
      const isAuthorized = getAuthorized('admin', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }

      const userLoginStartDt = moment('5/15/2023', 'M/D/YYYY').toDate();
      return models.userLogin
        .findAll({
          where: {
            loginDt: {
              [Op.gte]: userLoginStartDt,
            },
            application: 'Energea',
          },
          attributes: [
            // NOTE: Input America/New_York into the date_trunc function so that
            // 8pm logins are not attributed to the following day.
            [
              Sequelize.literal(
                `date_trunc('day', "loginDt", 'America/New_York')`
              ),
              'date',
            ],
            [Sequelize.literal('COUNT(DISTINCT("userId"))'), 'distinctUserIds'],
          ],
          group: [
            Sequelize.literal(
              `date_trunc('day', "loginDt", 'America/New_York')`
            ),
            'date',
          ],
        })
        .then((res) =>
          res.map((date) => ({
            date: date.dataValues.date,
            count: parseInt(date.dataValues.distinctUserIds, 10),
          }))
        );
    },
    getAccountantLedgerFeed: async (
      parent,
      {
        filter,
        // sort,
      },
      { models, roles, user }
    ) => {
      const isAuthorized = getAuthorized('admin', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }

      const portfolioFilter = {};
      if (filter.portfolio && filter.portfolio.id) {
        portfolioFilter['$monthlyPortfolioFinancialActual.portfolioId$'] =
          filter.portfolio.id;
      }
      const aPromises = [];
      const userAttributes = [
        'id',
        'fullName',
        'address1',
        'address2',
        'city',
        'state',
        'postalCode',
        'countryCode',
        'email',
      ];
      const investmentAttributes = [
        'id',
        'userId',
        'portfolioId',
        'startDt',
        'shares',
        'value',
      ];

      const dividendAttributes = [
        'id',
        'monthlyPortfolioFinancialActualId',
        'userId',
        'label',
        'date',
        'value',
      ];
      const shareTransferAttributes = [
        'id',
        'label',
        'soldShares',
        'sellDt',
        'value',
      ];

      const sellOrderAttributes = [
        'id',
        'portfolioId',
        'userId',
        'subAccountId',
      ];

      aPromises.push(
        models.investment
          .findAll({
            attributes: investmentAttributes,
            include: [
              {
                model: models.user,
                attributes: userAttributes,
              },
              {
                model: models.portfolio,
                attributes: ['id', 'name'],
              },
            ],
            where: {
              cancelledDt: null,
              portfolioId:
                filter.portfolio && filter.portfolio.id
                  ? filter.portfolio.id
                  : {
                      [Op.not]: null,
                    },
            },
          })
          .then((records) => {
            if (!filter.endDt) {
              return records;
            }
            const filteredRecords = [];
            const promises = records.map((record) =>
              record.completedDt.then((dt) => {
                if (dt <= filter.endDt) {
                  filteredRecords.push(record);
                }
              })
            );
            return Promise.all(promises).then(() => filteredRecords);
          })
      );

      aPromises.push(
        models.dividend
          .findAll({
            attributes: dividendAttributes,
            include: [
              {
                model: models.monthlyPortfolioFinancialActual,
                include: [
                  {
                    model: models.portfolio,
                    attributes: ['id', 'name'],
                  },
                ],
              },
              {
                model: models.user,
                attributes: userAttributes,
              },
            ],
            where: portfolioFilter,
            required: true,
          })
          .then((records) => {
            if (!filter.endDt) {
              return records;
            }
            const filteredRecords = [];
            const promises = records.map((record) =>
              record.completedDt.then((dt) => {
                if (dt <= filter.endDt) {
                  filteredRecords.push(record);
                }
              })
            );
            return Promise.all(promises).then(() => filteredRecords);
          })
      );

      aPromises.push(
        models.shareTransfer
          .findAll({
            attributes: shareTransferAttributes,
            include: [
              {
                model: models.sellOrder,
                where: { cancelledDt: null },
                attributes: sellOrderAttributes,
                include: [
                  {
                    model: models.user,
                    attributes: userAttributes,
                  },
                  {
                    model: models.portfolio,
                    attributes: ['id', 'name'],
                  },
                ],
              },
              {
                model: models.investment,
                attributes: investmentAttributes,
                where: {
                  cancelledDt: null,
                },
              },
            ],
            where: {
              '$sellOrder.portfolioId$':
                filter.portfolio && filter.portfolio.id
                  ? filter.portfolio.id
                  : {
                      [Op.not]: null,
                    },
            },
            required: true,
          })
          .then((records) => {
            if (!filter.endDt) {
              return records;
            }
            const filteredRecords = [];
            const promises = records.map((record) =>
              record.completedDt.then((dt) => {
                if (dt <= filter.endDt) {
                  filteredRecords.push(record);
                }
              })
            );
            return Promise.all(promises).then(() => filteredRecords);
          })
      );

      const [investments, dividends, shareTransfers] = await Promise.all(
        aPromises
      );

      const returnObj = {};
      const portfolioShareCounts = {};
      investments.forEach((investment) => {
        const sId = `${investment.portfolioId}-${investment.userId}`;
        const shares = parseFloat(investment.shares);
        const value = Math.round(parseFloat(investment.value) * 100) / 100;
        if (returnObj[String(sId)]) {
          returnObj[String(sId)].investments.push(investment);
          returnObj[String(sId)].totalInvested += value;
          returnObj[String(sId)].totalGrossOwnedShares += shares;
          returnObj[String(sId)].totalNetOwnedShares += shares;
        } else {
          returnObj[String(sId)] = {
            portfolio: investment.portfolio,
            user: investment.user,
            investments: [investment],
            dividends: [],
            shareTransfers: [],
            totalInvested: value,
            totalNetOwnedShares: shares,
            totalGrossOwnedShares: shares,
            totalGrossEarned: 0,
            totalDividends: 0,
            totalSoldShares: 0,
            totalSoldValue: 0,
          };
        }

        if (portfolioShareCounts[String(investment.portfolioId)]) {
          portfolioShareCounts[String(investment.portfolioId)] += shares;
        } else {
          portfolioShareCounts[String(investment.portfolioId)] = shares;
        }
      });
      dividends.forEach((dividend) => {
        const sId = `${dividend.monthlyPortfolioFinancialActual.portfolioId}-${dividend.userId}`;
        const value = Math.round(parseFloat(dividend.value) * 100) / 100;

        if (returnObj[String(sId)]) {
          returnObj[String(sId)].dividends.push(dividend);
          returnObj[String(sId)].totalDividends += value;
          returnObj[String(sId)].totalGrossEarned += value;
        } else {
          returnObj[String(sId)] = {
            portfolio: dividend.monthlyPortfolioFinancialActual.portfolio,
            user: dividend.user,
            investments: [],
            dividends: [dividend],
            shareTransfers: [],
            totalInvested: 0,
            totalNetOwnedShares: 0,
            totalGrossOwnedShares: 0,
            totalGrossEarned: value,
            totalDividends: value,
            totalSoldShares: 0,
            totalSoldValue: 0,
          };
        }
      });
      shareTransfers.forEach((shareTransfer) => {
        const sId = `${shareTransfer.sellOrder.portfolioId}-${shareTransfer.sellOrder.userId}`;
        const soldShares = parseFloat(shareTransfer.soldShares);
        const value = Math.round(parseFloat(shareTransfer.value) * 100) / 100;
        if (returnObj[String(sId)]) {
          returnObj[String(sId)].shareTransfers.push(shareTransfer);
          returnObj[String(sId)].totalNetOwnedShares -= soldShares;
          returnObj[String(sId)].totalSoldShares += soldShares;
          returnObj[String(sId)].totalSoldValue += value;
          returnObj[String(sId)].totalGrossEarned += value;
        } else {
          returnObj[String(sId)] = {
            portfolio: shareTransfer.sellOrder.portfolio,
            user: shareTransfer.sellOrder.user,
            investments: [],
            shareTransfers: [shareTransfer],
            totalInvested: 0,
            totalNetOwnedShares: soldShares * -1,
            totalGrossOwnedShares: 0,
            totalGrossEarned: value,
            totalDividends: 0,
            totalSoldShares: soldShares,
            totalSoldValue: value,
          };
        }
        if (portfolioShareCounts[String(shareTransfer.sellOrder.portfolioId)]) {
          portfolioShareCounts[String(shareTransfer.sellOrder.portfolioId)] -=
            soldShares;
        } else {
          portfolioShareCounts[String(shareTransfer.sellOrder.portfolioId)] =
            soldShares * -1;
        }
      });
      const rows = Object.keys(returnObj)
        .map((key) => {
          const el = returnObj[String(key)];
          el.id = key;
          if (el.portfolio) {
            const totalPortfolioShares =
              portfolioShareCounts[String(el.portfolio.id)];
            el.equity = (el.totalNetOwnedShares / totalPortfolioShares) * 100;
          } else {
            console.error('Missing portfolio', el);
          }
          return el;
        })
        .filter((row) => {
          if (filter.q && filter.q.length > 0) {
            const { firstName, lastName } = row.user.dataValues;
            if (
              !firstName.startsWith(filter.q) &&
              !lastName.startsWith(filter.q)
            ) {
              return false;
            }
          }
          return true;
        })
        .sort((a, b) => {
          const aIds = a.id.split('-').map((el) => parseInt(el, 10));
          const bIds = b.id.split('-').map((el) => parseInt(el, 10));
          if (aIds[0] < bIds[0]) {
            return -1;
          }
          if (aIds[0] > bIds[0]) {
            return 1;
          }
          return aIds[1] < bIds[1] ? -1 : 1;
        });
      return {
        rows,
        count: rows.length,
      };
    },
    getUserBlendedProductInvestmentRange: async (
      parent,
      { portfolioBlendPercentages, userId, blendedProductId },
      { models, user, roles }
    ) => {
      const isAuthorized = getAuthorized('admin', roles);
      if (!isAuthorized && !user) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      if (!isAuthorized && userId && userId !== user?.id) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      const dbUser = userId ? await models.user.findByPk(userId) : user;
      if (!dbUser) {
        return new ApolloError(`No database user found`, 'VALIDATION', {
          userId,
        });
      }

      // For some reason combining this query with the following using 'include' messed up the attributes in the regulation. Didn't return a value for unaccreditedInvestmentMax and for accreditedInvestmentMin it returned accreditedInvest: 10. Note the incomplete attr name
      const blendedProduct = await models.blendedProduct.findByPk(
        blendedProductId
      );
      if (!blendedProduct) {
        throw new ApolloError(
          `No database blended product found`,
          'VALIDATION',
          {
            blendedProductId,
          }
        );
      }
      const portfoliosWRegulation = await blendedProduct
        .getBlendedProductPortfolios({
          include: [
            {
              model: models.portfolio,
              include: [
                {
                  model: models.regulation,
                },
              ],
            },
          ],
        })
        .then((res) => {
          if (!res) {
            throw new ApolloError(
              `No database blended product portfolios found`,
              'VALIDATION',
              {
                blendedProductId,
              }
            );
          }
          return res.map(
            (blendedProductPortfolio) => blendedProductPortfolio.portfolio
          );
        });

      if (!portfoliosWRegulation || portfoliosWRegulation.length === 0) {
        return new ApolloError(
          `No database blended product found`,
          'VALIDATION',
          {
            blendedProductId,
          }
        );
      }

      const portfolioCapSpaceMap = {};
      const portfolioUserYearInvestmentTotals = {};
      const aPromises = [];
      for (let index = 0; index < portfoliosWRegulation.length; index += 1) {
        const portfolioWRegulation = portfoliosWRegulation[parseInt(index, 10)];
        portfolioCapSpaceMap[String(portfolioWRegulation.id)] = {
          portfolioName: portfolioWRegulation.subtitle,
        };
        aPromises.push(
          PortfolioService.getCurrentEquityData(portfolioWRegulation).then(
            (res) => {
              portfolioCapSpaceMap[String(portfolioWRegulation.id)].cap =
                res.investmentCap;
              portfolioCapSpaceMap[
                String(portfolioWRegulation.id)
              ].sumMinusUnsold = res.totalNetRaisedMinusUnsoldShareValue;
            }
          )
        );

        portfolioUserYearInvestmentTotals[String(portfolioWRegulation.id)] = {
          portfolioName: portfolioWRegulation.subtitle,
        };
        aPromises.push(
          UserService.portfolioInvestmentSumInCalendarYear(
            user,
            portfolioWRegulation.id
          ).then((res) => {
            portfolioUserYearInvestmentTotals[
              String(portfolioWRegulation.id)
            ].totalInvestedThisYear = parseFloat(res);
          })
        );
      }

      await Promise.all(aPromises);

      const portfolioBlendPercentageMap = {};
      let maxPortfolioPercentage = 0;
      portfolioBlendPercentages.portfolios.forEach((p) => {
        portfolioBlendPercentageMap[String(p.portfolioId)] = p.percentage;
        if (p.percentage > maxPortfolioPercentage) {
          maxPortfolioPercentage = p.percentage;
        }
      });

      // Find which portfolio will be the cause of the low cap space. This is a combo of the cap space and the percentage weighting towards it
      let limitingPortfolioCapSpace = null;
      let limitingPortfolio = null;
      let limitingPortfolioPercentage = null;
      let limitingRatio = null;
      Object.keys(portfolioCapSpaceMap).forEach((portfolioId) => {
        const data = portfolioCapSpaceMap[String(portfolioId)];
        const capSpace = data.cap - data.sumMinusUnsold;
        if (portfolioBlendPercentageMap[String(portfolioId)] > 0) {
          if (
            limitingRatio === null ||
            capSpace / portfolioBlendPercentageMap[String(portfolioId)] <
              limitingRatio
          ) {
            limitingRatio =
              capSpace / portfolioBlendPercentageMap[String(portfolioId)];
            limitingPortfolioCapSpace = capSpace;
            limitingPortfolio = data.portfolioName;
            limitingPortfolioPercentage =
              portfolioBlendPercentageMap[String(portfolioId)];
          }
        }
      });

      // Find which portfolio will be the cause of the unaccredited netWorth annualIncome max. This is a combo of the income/net worth and how much they've invested this calendar year
      let limitingIncomePortfolioMax = null;
      let limitingIncomePortfolioName = null;
      let limitingIncomePortfolioPercentage = null;
      let limitingIncomeRatio = null;
      let highestIncomeMax = 0;

      let limitingNetWorthPortfolioMax = null;
      let limitingNetWorthPortfolioName = null;
      let limitingNetWorthPortfolioPercentage = null;
      let limitingNetWorthRatio = null;
      let highestNetWorthMax = 0;

      Object.keys(portfolioUserYearInvestmentTotals).forEach((portfolioId) => {
        const data = portfolioUserYearInvestmentTotals[String(portfolioId)];

        const incomeMax = user.annualIncome
          ? user.annualIncome * 0.1 - data.totalInvestedThisYear
          : 0;
        highestIncomeMax += parseFloat(incomeMax);
        if (portfolioBlendPercentageMap[String(portfolioId)] > 0) {
          if (
            limitingIncomeRatio === null ||
            incomeMax / portfolioBlendPercentageMap[String(portfolioId)] <
              limitingIncomeRatio
          ) {
            limitingIncomeRatio =
              incomeMax / portfolioBlendPercentageMap[String(portfolioId)];
            limitingIncomePortfolioMax = incomeMax;
            limitingIncomePortfolioName = data.portfolioName;
            limitingIncomePortfolioPercentage =
              portfolioBlendPercentageMap[String(portfolioId)];
          }
        }

        const netWorthMax = user.netWorth
          ? user.netWorth * 0.1 - data.totalInvestedThisYear
          : 0;
        highestNetWorthMax += parseFloat(netWorthMax);
        if (portfolioBlendPercentageMap[String(portfolioId)] > 0) {
          if (
            limitingNetWorthRatio === null ||
            netWorthMax / portfolioBlendPercentageMap[String(portfolioId)] <
              limitingNetWorthRatio
          ) {
            limitingNetWorthRatio =
              netWorthMax / portfolioBlendPercentageMap[String(portfolioId)];
            limitingNetWorthPortfolioMax = netWorthMax;
            limitingNetWorthPortfolioName = data.portfolioName;
            limitingNetWorthPortfolioPercentage =
              portfolioBlendPercentageMap[String(portfolioId)];
          }
        }
      });

      let portfolioCapSpace = 0;
      let portfolioIncomeMax = 0;
      let portfolioNetWorthMax = 0;
      portfolioBlendPercentages.portfolios.forEach((p) => {
        portfolioCapSpace +=
          limitingPortfolioCapSpace *
          (p.percentage / limitingPortfolioPercentage);
        portfolioIncomeMax +=
          limitingIncomePortfolioMax *
          (p.percentage / limitingIncomePortfolioPercentage);
        portfolioNetWorthMax +=
          limitingNetWorthPortfolioMax *
          (p.percentage / limitingNetWorthPortfolioPercentage);
      });

      const { id, regulation } = portfoliosWRegulation[0];
      if (!regulation) {
        return new ApolloError(`No regulation found`, 'VALIDATION', {
          portfolioId: id,
        });
      }

      const { isAccredited } = user;
      // TODO: this is overriding to select the smallest Range...it may make more sense to prioritize by specificity
      const accMax = [
        regulation.accreditedInvestmentMax,
        user.investmentMax,
        portfolioCapSpace,
      ].filter((el) => el !== null && el !== undefined);
      const accMin = [
        constants.accreditedInvestmentMin,
        regulation.accreditedInvestmentMin,
        user.investmentMin,
      ].filter((el) => el !== null && el !== undefined);
      const unaccMax = [
        (user.annualIncome || user.annualIncome === 0) &&
        (user.netWorth || user.netWorth === 0)
          ? Math.max(portfolioIncomeMax, portfolioNetWorthMax)
          : null,
        constants.unaccreditedInvestmentMax,
        regulation.unaccreditedInvestmentMax,
        user.investmentMax,
        portfolioCapSpace,
      ].filter((el) => el !== null && el !== undefined);
      const unaccMin = [
        constants.unaccreditedInvestmentMin,
        regulation.unaccreditedInvestmentMin,
        user.investmentMin,
      ].filter((el) => el !== null && el !== undefined);
      let min;
      let max;
      let minFormatted;
      let maxFormatted;
      let minDescription;
      let maxDescription;
      if (isAccredited) {
        min = Math.max(...accMin) || 0;
        max = Math.min(...accMax) || 0;
        minFormatted = numeral(min).format('$0,0');
        maxFormatted = numeral(max).format('$0,0');
        if (min === constants.accreditedInvestmentMin) {
          minDescription = `${minFormatted} is currently the minimum investment that we accept from accredited investors.`;
        } else if (min === regulation.accreditedInvestmentMin) {
          minDescription = `${minFormatted} is currently the minimum investment that we accept for this type of investment (${regulation.name}).`;
        } else {
          minDescription = `${minFormatted} is currently the minimum investment that we accept from accredited investors for this type of investment (${regulation.name}).`;
        }
        if (max === constants.accreditedInvestmentMax) {
          maxDescription = `${maxFormatted} is currently the maximum investment that we accept from accredited investors.`;
        } else if (max === regulation.accreditedInvestmentMax) {
          maxDescription = `${maxFormatted} is currently the maximum investment that we accept for this type of investment (${regulation.name}).`;
        } else if (max === portfolioCapSpace) {
          maxDescription = `Only ${maxFormatted} can be invested at your selected weightings due to limited cap space in '${limitingPortfolio}'. You can increase the max by weighting your investment less towards '${limitingPortfolio}'.`;
        } else {
          maxDescription = `${maxFormatted} is currently the maximum investment that we accept from accredited investors for this type of investment (${regulation.name}).`;
        }
      } else {
        min = Math.max(...unaccMin) || 0;
        max = Math.min(...unaccMax) || 0;
        minFormatted = numeral(Math.ceil(min)).format('$0,0');
        maxFormatted = numeral(Math.floor(max)).format('$0,0');
        if (min === constants.unaccreditedInvestmentMin) {
          minDescription = `${minFormatted} is currently the minimum investment that we accept from unaccredited investors.`;
        } else if (min === regulation.unaccreditedInvestmentMin) {
          minDescription = `${minFormatted} is currently the minimum investment that we accept for this type of investment (${regulation.name}).`;
        } else {
          minDescription = `${minFormatted} is currently the minimum investment that we accept from an unaccredited investor for this type of investment (${regulation.name}).`;
        }
        if (max === portfolioIncomeMax) {
          maxDescription = `${maxFormatted} is 10% of your annual income (${numeral(
            user.annualIncome
          ).format(
            '$0,0'
          )}) minus the sum of your investments this calendar year per portfolio at the weightings you selected. You can increase the max to nearly ${numeral(
            highestIncomeMax
          ).format(
            '$0,0'
          )} by weighting your investment less towards '${limitingIncomePortfolioName}' or by updating your annual income on your settings page.`;
        } else if (max === portfolioNetWorthMax) {
          maxDescription = `${maxFormatted} is 10% of your net worth (${numeral(
            user.netWorth
          ).format(
            '$0,0'
          )}) minus the sum of your investments this calendar year per portfolio at the weightings you selected. You can increase the max to nearly ${numeral(
            highestNetWorthMax
          ).format(
            '$0,0'
          )} by weighting your investment less towards '${limitingNetWorthPortfolioName}'  or by updating your net worth on your settings page.`;
        } else if (max === constants.unaccreditedInvestmentMax) {
          maxDescription = `${maxFormatted} is currently the maximum investment that we accept from unaccredited investors.`;
        } else if (max === regulation.unaccreditedInvestmentMax) {
          maxDescription = `${maxFormatted} is currently the maximum investment that we accept for this type of investment (${regulation.name}).`;
        } else if (max === portfolioCapSpace) {
          maxDescription = `Only ${maxFormatted} can be invested at your selected weightings due to limited cap space in '${limitingPortfolio}'. You can increase the max by weighting your investment less towards '${limitingPortfolio}'.`;
        } else {
          maxDescription = `${maxFormatted} is currently the maximum investment that we accept from an unaccredited investor for this type of investment (${regulation.name}).`;
        }
      }
      // apply user overrides
      if (user.investmentMin && user.investmentMin < min) {
        min = user.investmentMin;
        minDescription = `Your investment minimum has been temporarily dropped to ${numeral(
          Math.ceil(min)
        ).format('$0,0')}.`;
      }
      return {
        min,
        minDescription,
        max,
        maxDescription,
      };
    },
    getUserPortfolioEquityBreakdown: async (
      parent,
      args,
      { models, roles, user }
    ) => {
      const isAuthorized = getAuthorized('admin', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      const aPromises = [];

      aPromises.push(
        models.investment
          .findAll({
            where: {
              userId: args.userId,
              portfolioId: args.portfolioId,
              subAccountId: args.subAccountId || null,
              cancelledDt: null,
            },
            attributes: [[fn('sum', col('shares')), 'sharesPurchased']],
          })
          .then((res) => parseFloat(res[0].dataValues.sharesPurchased || 0))
      );
      aPromises.push(
        models.sellOrder
          .findAll({
            where: {
              userId: args.userId,
              portfolioId: args.portfolioId,
              subAccountId: args.subAccountId || null,
              cancelledDt: null,
            },
            attributes: [
              [fn('sum', col('shares')), 'sharesRequestedForSaleTotal'],
            ],
          })
          .then((res) =>
            parseFloat(res[0].dataValues.sharesRequestedForSaleTotal || 0)
          )
      );
      aPromises.push(
        models.sellOrder
          .findAll({
            where: {
              userId: args.userId,
              portfolioId: args.portfolioId,
              subAccountId: args.subAccountId || null,
              cancelledDt: null,
            },
            attributes: ['id'],
            include: [
              {
                required: false,
                model: models.shareTransfer,
                attributes: ['soldShares'],
                include: [
                  {
                    attributes: [],
                    required: true,
                    model: models.investment,
                    where: {
                      cancelledDt: null,
                    },
                  },
                ],
              },
            ],
          })
          .then((sellOrders) => {
            let totalSoldShares = 0;
            sellOrders.forEach((sellOrder) => {
              sellOrder.shareTransfers.forEach((shareTransfer) => {
                totalSoldShares += parseFloat(shareTransfer.soldShares);
              });
            });
            return totalSoldShares;
          })
      );

      const [
        totalSharesPurchased,
        sharesRequestedForSaleTotal,
        totalSharesSold,
      ] = await Promise.all(aPromises);

      return {
        sharesAvailableForSale:
          totalSharesPurchased - sharesRequestedForSaleTotal,
        sharesPendingSale: sharesRequestedForSaleTotal - totalSharesSold,
      };
    },
    getUserPortfolioTaxSummaryFeed: (parent, args, { roles, user }) => {
      const isAuthorized = getAuthorized('admin', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      return UserService.getUserPortfolioTaxSummary(
        constants.taxYear,
        args.filter
      );
    },
    getUser: async (parent, { id, accountFilter }, { models, roles, user }) => {
      const isAuthorized = getAuthorized('admin', roles);

      // Check if user has access to this account
      if (!isAuthorized && user) {
        const accessCheck = await checkUserAccountAccess(user.id, id);
        if (!accessCheck.hasAccess) {
          return new ForbiddenError('You do not have access to this endpoint', {
            user,
          });
        }
      } else if (!isAuthorized && !user) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      if (accountFilter && accountFilter.subAccountId) {
        // Now that we know the caller is either an authorized user or the
        // context user id is the id of the user being requested, we just
        // need to make sure the id being requested and the subAccount being
        // requested belong together. Otherwise, someone could change the
        // subAccountId in the query string on the user dashboard and query a
        // different users data.
        await models.subAccount
          .findOne({
            attributes: ['id'],
            where: {
              id: accountFilter.subAccountId,
              userId: id,
            },
          })
          .then((subAccount) => {
            if (!subAccount) {
              throw new ForbiddenError(
                'User does not have access to this subAccount',
                {
                  user,
                  subAccountId: accountFilter.subAccountId,
                }
              );
            }
          });
      }
      return models.user.findByPk(id);
    },

    getUserByEmail: (parent, { email }, { models }) =>
      models.user.findOne({
        where: {
          email,
        },
      }),
    confirmEmail: (parent, { id, email, token }) =>
      confirmEmail({
        email,
        id,
        token,
      }),
    mia_confirmEmail: (parent, { id, email, token }) =>
      confirmEmail({
        email,
        id,
        token,
      }),
    me: (parentValue, args, { user, brContact }) => ({
      id: `me-${user?.id}-${brContact?.id}`,
      user,
      brContact,
    }),
    mia_me: (parentValue, args, { user, brContact }) => ({
      user,
      brContact,
    }),
    getUserPortfolioInvestmentRange: async (
      parent,
      { userId, portfolioId, isRecurringInvestment },
      { models, user, roles }
    ) => {
      if (!userId && !user) {
        return {
          min: 0,
          max: 0,
          minDescription: 'User must be logged in.',
          maxDescription: 'User must be logged in.',
        };
      }
      const isAuthorized = getAuthorized('admin', roles);
      if (!isAuthorized && !user) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      const dbUser = userId ? await models.user.findByPk(userId) : user;
      if (!dbUser) {
        return new ApolloError(`No database user found`, 'VALIDATION', {
          userId,
        });
      }
      const portfolioWRegulation = await models.portfolio.findByPk(
        portfolioId,
        {
          include: [
            {
              model: models.regulation,
            },
          ],
        }
      );

      if (!portfolioWRegulation) {
        return new ApolloError(`No database portfolio found`, 'VALIDATION', {
          portfolioId,
        });
      }
      const { regulation } = portfolioWRegulation;
      if (!regulation) {
        return new ApolloError(`No regulation found`, 'VALIDATION', {
          portfolioId,
        });
      }

      const aPromises = [
        PortfolioService.getCurrentEquityData(portfolioWRegulation),
        UserService.portfolioInvestmentSumInCalendarYear(dbUser, portfolioId),
        UserService.hasInvestedWithoutReferrals(user, {
          portfolioId,
        }),
      ];
      const [
        currentEquityData,
        userPortfolioCalendarYrInvestmentTotal,
        hasInvestedWithoutReferrals,
      ] = await Promise.all(aPromises);
      const portfolioCapSpace =
        currentEquityData.investmentCap -
        currentEquityData.totalNetRaisedMinusUnsoldShareValue;

      const { isAccredited } = user;
      // TODO: this is overriding to select the smallest Range...it may make more sense to prioritize by specificity
      const accMax = [
        regulation.accreditedInvestmentMax,
        user.investmentMax,
        portfolioCapSpace,
      ].filter((el) => el !== null && el !== undefined);
      const accMin = [
        constants.accreditedInvestmentMin,
        regulation.accreditedInvestmentMin,
        user.investmentMin,
      ].filter((el) => el !== null && el !== undefined);
      const annualIncomeMaxMinusYearlyInvestments = user.annualIncome
        ? user.annualIncome * 0.1 - userPortfolioCalendarYrInvestmentTotal
        : null;
      const netWorthMaxMinusYearlyInvestments = user.netWorth
        ? user.netWorth * 0.1 - userPortfolioCalendarYrInvestmentTotal
        : null;
      const unaccMax = [
        // max of 10% of your annual income of 10% of your net worth
        (user.annualIncome || user.annualIncome === 0) &&
        (user.netWorth || user.netWorth === 0)
          ? Math.max(
              annualIncomeMaxMinusYearlyInvestments,
              netWorthMaxMinusYearlyInvestments
            )
          : null,
        constants.unaccreditedInvestmentMax,
        regulation.unaccreditedInvestmentMax,
        user.investmentMax,
        portfolioCapSpace,
      ].filter((el) => el !== null && el !== undefined);
      const unaccMin = [
        isRecurringInvestment
          ? constants.unaccreditedAutoInvestMin
          : constants.unaccreditedInvestmentMin,
        regulation.unaccreditedInvestmentMin,
        user.investmentMin,
      ].filter((el) => el !== null && el !== undefined);
      let min;
      let max;
      let minFormatted;
      let maxFormatted;
      let minDescription;
      let maxDescription;
      if (isAccredited) {
        min = Math.max(...accMin) || 0;
        max = Math.min(...accMax) || 0;

        // Situational overrides
        if (hasInvestedWithoutReferrals && isRecurringInvestment)
          min = constants.accreditedAutoInvestMinOnceInvested;
        else if (isRecurringInvestment) min = constants.accreditedAutoInvestMin;
        else if (hasInvestedWithoutReferrals)
          min = constants.accreditedInvestmentMinOnceInvested;

        minFormatted = numeral(Math.ceil(min)).format('$0,0');
        maxFormatted = numeral(Math.floor(max)).format('$0,0');
        if (
          hasInvestedWithoutReferrals &&
          isRecurringInvestment &&
          min === constants.accreditedAutoInvestMinOnceInvested
        ) {
          minDescription = `${minFormatted} is currently the minimum recurring investment that we accept from accredited investors that have already met the investment minimum for this portfolio.`;
        } else if (
          isRecurringInvestment &&
          min === constants.accreditedAutoInvestMin
        ) {
          minDescription = `${minFormatted} is currently the minimum recurring investment that we accept from accredited investors.`;
        } else if (
          hasInvestedWithoutReferrals &&
          min === constants.accreditedInvestmentMinOnceInvested
        ) {
          minDescription = `${minFormatted} is currently the minimum investment that we accept from accredited investors that have already met the investment minimum for this portfolio.`;
        } else if (min === constants.accreditedInvestmentMin) {
          minDescription = `${minFormatted} is currently the minimum investment that we accept from accredited investors.`;
        } else if (min === regulation.accreditedInvestmentMin) {
          minDescription = `${minFormatted} is currently the minimum investment that we accept for this type of investment (${regulation.name}).`;
        } else {
          minDescription = `${minFormatted} is currently the minimum investment that we accept from accredited investors for this type of investment (${regulation.name}).`;
        }
        if (max === constants.accreditedInvestmentMax) {
          maxDescription = `${maxFormatted} is currently the maximum investment that we accept from accredited investors.`;
        } else if (max === regulation.accreditedInvestmentMax) {
          maxDescription = `${maxFormatted} is currently the maximum investment that we accept for this type of investment (${regulation.name}).`;
        } else if (max === portfolioCapSpace) {
          maxDescription = `There is only ${maxFormatted} left to be raised for this investment.`;
        } else {
          maxDescription = `${maxFormatted} is currently the maximum investment that we accept from accredited investors for this type of investment (${regulation.name}).`;
        }
      } else {
        min = Math.max(...unaccMin) || 0;
        max = Math.min(...unaccMax) || 0;

        // Situational overrides
        if (hasInvestedWithoutReferrals && isRecurringInvestment)
          min = constants.unaccreditedAutoInvestMinOnceInvested;
        else if (isRecurringInvestment)
          min = constants.unaccreditedAutoInvestMin;
        else if (hasInvestedWithoutReferrals)
          min = constants.unaccreditedInvestmentMinOnceInvested;

        minFormatted = numeral(Math.ceil(min)).format('$0,0');
        maxFormatted = numeral(Math.floor(max)).format('$0,0');
        if (
          hasInvestedWithoutReferrals &&
          isRecurringInvestment &&
          min === constants.unaccreditedAutoInvestMinOnceInvested
        ) {
          minDescription = `${minFormatted} is currently the minimum recurring investment that we accept from unaccredited investors that have already met the investment minimum for this portfolio.`;
        } else if (
          isRecurringInvestment &&
          min === constants.unaccreditedAutoInvestMin
        ) {
          minDescription = `${minFormatted} is currently the minimum recurring investment for unaccredited investors.`;
        } else if (
          hasInvestedWithoutReferrals &&
          min === constants.unaccreditedInvestmentMinOnceInvested
        ) {
          minDescription = `${minFormatted} is currently the minimum investment for investors that have already met the investment minimum for this portfolio.`;
        } else if (min === constants.unaccreditedInvestmentMin) {
          minDescription = `${minFormatted} is currently the minimum investment for unaccredited investors.`;
        } else if (min === regulation.unaccreditedInvestmentMin) {
          minDescription = `${minFormatted} is currently the minimum investment for this type of investment (${regulation.name}).`;
        } else {
          minDescription = `${minFormatted} is currently the minimum investment for an unaccredited investor in this type of investment (${regulation.name}).`;
        }
        if (max === annualIncomeMaxMinusYearlyInvestments) {
          maxDescription = `${maxFormatted} is 10% of your annual income (${numeral(
            user.annualIncome
          ).format(
            '$0,0'
          )}) minus the sum of your investments this calendar year (${numeral(
            userPortfolioCalendarYrInvestmentTotal
          ).format('$0,0')}).`;
        } else if (max === netWorthMaxMinusYearlyInvestments) {
          maxDescription = `${maxFormatted} is 10% of your net worth (${numeral(
            user.netWorth
          ).format(
            '$0,0'
          )}) minus the sum of your investments this calendar year (${numeral(
            userPortfolioCalendarYrInvestmentTotal
          ).format('$0,0')}).`;
        } else if (max === constants.unaccreditedInvestmentMax) {
          maxDescription = `${maxFormatted} is currently the maximum investment for unaccredited investors.`;
        } else if (max === regulation.unaccreditedInvestmentMax) {
          maxDescription = `${maxFormatted} is currently the maximum investment for this type of investment (${regulation.name}).`;
        } else if (max === portfolioCapSpace) {
          maxDescription = `There is ${maxFormatted} left to be raised for this investment.`;
        } else {
          maxDescription = `${maxFormatted} is currently the maximum investment for unaccredited investors in this type of investment (${regulation.name}).`;
        }
      }
      // apply user overrides
      if (user.investmentMin && user.investmentMin < min) {
        min = user.investmentMin;
        minDescription = `Your investment minimum has been temporarily dropped to ${numeral(
          Math.ceil(min)
        ).format('$0,0')}.`;
      }
      return {
        min,
        minDescription,
        max,
        maxDescription,
      };
    },
    userFeed: async (
      parent,
      { filter, sort, pagination, exportConfig },
      { models, roles, user }
    ) => {
      const isAuthorized = getAuthorized('admin', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      const filterClauses = UserService.getUserFeedFilter(filter);

      let sortField = (sort && sort.field) || 'id';
      let sortOrder = (sort && sort.order) || 'DESC';

      if (exportConfig && exportConfig.emailResults) {
        const queue = new Queue('handleListEmailExport', bullOptions);
        await queue.add({
          sort,
          filter,
          exportConfig,
          list: 'user',
        });
        queue.close();
        return {
          rows: [],
          count: 0,
        };
      }
      if (sortField === 'dwollaCustomer.status') {
        sortField = 'dwollaId';
      }
      if (sortField === 'environmentalImpactVsReturns') {
        sortOrder = sortOrder === 'DESC' ? 'DESC NULLS LAST' : 'ASC';
      }
      if (sortField === 'investmentSum') {
        sortOrder =
          sortOrder === 'DESC' ? 'DESC NULLS LAST' : 'ASC NULLS FIRST';
        return models.user
          .findAndCountAll({
            group: ['user.id'],
            subQuery: false,
            offset: (pagination.page - 1) * pagination.perPage,
            limit: pagination.perPage,
            include: [
              {
                model: models.investment,
                attributes: [],
                required: false,
                where: {
                  cancelledDt: null,
                },
              },
            ],
            where: filterClauses,
            order: [
              [
                Sequelize.fn('sum', Sequelize.col('investments.value')),
                sortOrder,
              ],
            ],
          })
          .then((userWithInvestments) => {
            const returnObj = userWithInvestments;
            returnObj.count = returnObj.count.length;
            return returnObj;
          });
      }
      if (sortField === 'dividendSum') {
        sortOrder =
          sortOrder === 'DESC' ? 'DESC NULLS LAST' : 'ASC NULLS FIRST';
        return models.user
          .findAndCountAll({
            group: ['user.id'],
            subQuery: false,
            offset: (pagination.page - 1) * pagination.perPage,
            limit: pagination.perPage,
            include: [
              {
                model: models.dividend,
                attributes: [],
                required: false,
              },
            ],
            where: filterClauses,
            order: [
              [
                Sequelize.fn('sum', Sequelize.col('dividends.value')),
                sortOrder,
              ],
            ],
          })
          .then((userWithDividends) => {
            const lintedUserWithDividends = userWithDividends;
            lintedUserWithDividends.count = userWithDividends.count.length;
            return lintedUserWithDividends;
          });
      }
      return models.user.findAndCountAll({
        order: [[sortField, sortOrder]],
        where: filterClauses,
        offset: (pagination.page - 1) * pagination.perPage,
        limit: pagination.perPage,
      });
    },
    // getTopInvestorStates: async (
    //   parent,
    //   { input },
    //   { models, roles, user }
    // ) => {
    //   // check context and check for role
    //   const isAuthorized = getAuthorized('admin', roles);
    //   if (!isAuthorized) {
    //     return new ForbiddenError('You do not have access to this endpoint', {
    //       user,
    //     });
    //   }

    //   const { accreditedFilter } = input;
    //   const userFilter = {
    //     state: {
    //       [Op.not]: null,
    //     },
    //   };
    //   if (accreditedFilter === 'unaccreditedOnly') {
    //     userFilter.isAccredited = false;
    //   } else if (accreditedFilter === 'accreditedOnly') {
    //     userFilter.isAccredited = true;
    //   }

    //   return models.user
    //     .findAll({
    //       group: ['state'],
    //       where: userFilter,
    //       include: [
    //         {
    //           required: true,
    //           model: models.investment,
    //           attributes: [],
    //           where: { cancelledDt: null },
    //         },
    //       ],
    //       attributes: ['state', [fn('COUNT', 'state'), 'numberOfUsers']],
    //     })
    //     .then((resp) =>
    //       resp
    //         .map((el) => el.dataValues)
    //         .sort((a, b) =>
    //           parseInt(a.numberOfUsers, 10) < parseInt(b.numberOfUsers, 10)
    //             ? 1
    //             : -1
    //         )
    //         .slice(0, 5)
    //     );
    // },
    // getAvgEnvironmentalScore: async (parent, args, { models, roles, user }) => {
    //   // check context and check for role
    //   const isAuthorized = getAuthorized('admin', roles);
    //   if (!isAuthorized) {
    //     return new ForbiddenError('You do not have access to this endpoint', {
    //       user,
    //     });
    //   }
    //   const resp = await models.user.findAll({
    //     attributes: [
    //       [
    //         Sequelize.fn('AVG', Sequelize.col('environmentalImpactVsReturns')),
    //         'avgEnvironmentalImpactVsReturns',
    //       ],
    //     ],
    //     where: [
    //       {
    //         environmentalImpactVsReturns: {
    //           [Op.not]: null,
    //         },
    //       },
    //       {
    //         environmentalImpactVsReturns: {
    //           [Op.not]: 50,
    //         },
    //       },
    //     ],
    //   });
    //   return resp[0].dataValues.avgEnvironmentalImpactVsReturns;
    // },
    // getAvgUserAge: async (parent, { input }, { roles, user }) => {
    //   // check context and check for role
    //   const isAuthorized = getAuthorized('admin', roles);
    //   if (!isAuthorized) {
    //     return new ForbiddenError('You do not have access to this endpoint', {
    //       user,
    //     });
    //   }
    //   const resp = await database.query(
    //     'SELECT AVG(NOW() - "dateOfBirth") FROM "users" where "dateOfBirth" IS NOT NULL',
    //     {
    //       type: Sequelize.QueryTypes.SELECT,
    //     }
    //   );
    //   return resp[0].avg.days / 365;
    // },
    // getAvgInvestorAge: async (parent, { input }, { roles, user }) => {
    //   // check context and check for role
    //   const isAuthorized = getAuthorized('admin', roles);
    //   if (!isAuthorized) {
    //     return new ForbiddenError('You do not have access to this endpoint', {
    //       user,
    //     });
    //   }
    //   const resp = await database.query(
    //     'SELECT AVG(NOW() - "dateOfBirth") FROM "users" where "dateOfBirth" IS NOT NULL',
    //     {
    //       type: Sequelize.QueryTypes.SELECT,
    //     }
    //   );
    //   return resp[0].avg.days / 365;
    // },
    getNewUserCountThisWeek: (parent, args, { models, roles, user }) => {
      const isAuthorized = getAuthorized('admin', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      return models.user.count({
        where: {
          id: { [Op.not]: 19063 }, // Exclude review user from new user counts
          createdAt: {
            [Op.gte]: moment().startOf('week').toDate(),
          },
        },
      });
    },
    getNewInvestorCountThisWeek: async (
      parent,
      args,
      { models, roles, user }
    ) => {
      const isAuthorized = getAuthorized('admin', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      const [investorCountYesterday, investorCountToday] = await Promise.all([
        models.user.findAll({
          attributes: ['id'],
          include: [
            {
              attributes: [],
              required: true,
              model: models.investment,
              where: {
                cancelledDt: null,
                startDt: {
                  [Op.lt]: moment().startOf('week').toDate(),
                },
              },
            },
          ],
        }),
        models.user.findAll({
          attributes: ['id'],
          include: [
            {
              attributes: [],
              required: true,
              model: models.investment,
              where: {
                cancelledDt: null,
              },
            },
          ],
        }),
      ]);
      return investorCountToday.length - investorCountYesterday.length;
    },
    getInvestmentTotalThisWeek: (parent, args, { models, roles, user }) => {
      // check context and check for role
      const isAuthorized = getAuthorized('admin', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      return models.investment
        .findAll({
          attributes: [[fn('sum', col('value')), 'totalInvested']],
          where: {
            startDt: {
              [Op.gte]: moment().startOf('week').toDate(),
            },
            cancelledDt: null,
          },
        })
        .then((res) => parseFloat(res[0].dataValues.totalInvested || 0));
    },
    getNewUserCountThisMonth: (parent, args, { models, roles, user }) => {
      const isAuthorized = getAuthorized('admin', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      return models.user.count({
        where: {
          createdAt: {
            [Op.gte]: moment().startOf('month').toDate(),
          },
        },
      });
    },
    getMonthlyInvestmentTotalGoal: () => 2_000_000,
    getWeeklyInvestmentTotalGoal: () => 500_000,
    getMonthlyNewInvestorCountGoal: () => 120,
    getWeeklyNewInvestorCountGoal: () => 30,
    getMonthlyNewUserCountGoal: () => 500,
    getWeeklyNewUserCountGoal: () => 120,
    getNewInvestorCountThisMonth: async (
      parent,
      args,
      { models, roles, user }
    ) => {
      const isAuthorized = getAuthorized('admin', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      const [investorCountYesterday, investorCountToday] = await Promise.all([
        models.user.findAll({
          attributes: ['id'],
          where: {
            id: { [Op.not]: 19063 }, // Exclude review user from investor counts
          },
          include: [
            {
              attributes: [],
              required: true,
              model: models.investment,
              where: {
                cancelledDt: null,
                startDt: {
                  [Op.lt]: moment().startOf('month').toDate(),
                },
              },
            },
          ],
        }),
        models.user.findAll({
          attributes: ['id'],
          where: {
            id: { [Op.not]: 19063 }, // Exclude review user from investor counts
          },
          include: [
            {
              attributes: [],
              required: true,
              model: models.investment,
              where: {
                cancelledDt: null,
              },
            },
          ],
        }),
      ]);
      return investorCountToday.length - investorCountYesterday.length;
    },
    getInvestmentTotalThisMonth: (parent, args, { models, roles, user }) => {
      // check context and check for role
      const isAuthorized = getAuthorized('admin', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      return models.investment
        .findAll({
          attributes: [[fn('sum', col('value')), 'totalInvested']],
          where: {
            startDt: {
              [Op.gte]: moment().startOf('month').toDate(),
            },
            cancelledDt: null,
          },
        })
        .then((res) => parseFloat(res[0].dataValues.totalInvested || 0));
    },
    top10MostLoginsLast30Days: (parent, args, { models, roles, user }) => {
      const isAuthorized = getAuthorized('admin', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      return models.userLogin
        .findAll({
          attributes: ['userId', [fn('count', 'id'), 'loginCount']],
          where: {
            loginDt: {
              [Op.gte]: moment().add(-30, 'days').toDate(),
            },
          },
          include: [
            {
              model: models.user,
              attributes: ['id', 'firstName', 'lastName'],
              required: true,
            },
          ],
          group: ['userId', 'user.id'],
          order: [[fn('count', 'id'), 'desc']],
          limit: 10,
        })
        .then(
          (res) =>
            res.map((userLoginCount) => ({
              user: userLoginCount.user,
              value: parseInt(userLoginCount.dataValues.loginCount, 10),
            })),
          (err) => {
            throw new ApolloError(
              "Error getting user's login count",
              'APPLICATION',
              { err }
            );
          }
        );
    },
    top10LargestInvestors: (parent, args, { models, roles, user }) => {
      const isAuthorized = getAuthorized('admin', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      return models.investment
        .findAll({
          attributes: ['userId', [fn('sum', col('value')), 'totalInvested']],
          where: {
            cancelledDt: null,
            userId: {
              [Op.not]: 76,
            },
          },
          include: [
            {
              model: models.user,
              attributes: ['id', 'firstName', 'lastName'],
              required: true,
            },
          ],
          group: ['userId', 'user.id'],
          order: [[fn('sum', col('value')), 'desc']],
          limit: 10,
        })
        .then(
          (res) =>
            res.map((investmentUser) => ({
              user: investmentUser.user,
              value: parseFloat(investmentUser.dataValues.totalInvested),
            })),
          (err) => {
            throw new ApolloError(
              "Error getting user's login count",
              'APPLICATION',
              { err }
            );
          }
        );
    },
    platformUserActivityFeed: async (user, args, { models, roles }) => {
      // check context and check for role
      const isAuthorized = getAuthorized('admin', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }

      const activityItems = [];
      // TODO: Activity feed entries:
      // User left review

      const hours = 2;
      const startDt = moment().add(-1 * hours, 'hours');
      const aPromises = [];

      // User created subAccount
      aPromises.push(
        models.subAccount
          .findAll({
            attributes: ['id', 'createdAt'],
            where: {
              createdAt: {
                [Op.gte]: startDt.toDate(),
              },
            },
            include: [
              {
                model: models.user,
                attributes: ['firstName', 'lastName'],
                required: true,
              },
            ],
          })
          .then((subAccounts) => {
            subAccounts.forEach((subAccount) => {
              activityItems.push({
                id: `new-subaccount-${subAccount.id}`,
                message: `${subAccount.user.firstName} ${subAccount.user.lastName} created a subAccount`,
                date: subAccount.createdAt,
                type: 'NEW_SUBACCOUNT',
              });
            });
          })
      );

      // User created subAccount
      aPromises.push(
        models.millenniumTrustFundingSession
          .findAll({
            attributes: ['id', 'createdAt'],
            where: {
              createdAt: {
                [Op.gte]: startDt.toDate(),
              },
            },
            include: [
              {
                model: models.subAccount,
                attributes: ['id'],
                required: true,
                include: [
                  {
                    model: models.user,
                    attributes: ['firstName', 'lastName'],
                    required: true,
                  },
                ],
              },
            ],
          })
          .then((fundingSessions) => {
            fundingSessions.forEach((fundingSession) => {
              activityItems.push({
                id: `funded-subaccount-${fundingSession.id}`,
                message: `${fundingSession.subAccount.user.firstName} ${fundingSession.subAccount.user.lastName} funded a subAccount`,
                date: fundingSession.createdAt,
                type: 'FUNDED_SUBACCOUNT',
              });
            });
          })
      );

      // User requested to sell shares
      aPromises.push(
        models.sellOrder
          .findAll({
            attributes: ['id', 'createdAt'],
            where: {
              createdAt: {
                [Op.gte]: startDt.toDate(),
              },
            },
            include: [
              {
                model: models.user,
                attributes: ['firstName', 'lastName'],
                required: true,
              },
              {
                model: models.portfolio,
                attributes: ['subtitle'],
                required: true,
              },
            ],
          })
          .then((newSellOrders) => {
            newSellOrders.forEach((newSellOrder) => {
              activityItems.push({
                id: `new-sell-order-${newSellOrder.id}`,
                message: `${newSellOrder.user.firstName} ${newSellOrder.user.lastName} requested to sell shares in ${newSellOrder.portfolio.subtitle}`,
                date: newSellOrder.createdAt,
                type: 'NEW_SELL_ORDER',
              });
            });
          })
      );

      // User sold shares
      aPromises.push(
        models.shareTransfer
          .findAll({
            attributes: ['id', 'soldShares', 'createdAt'],
            where: {
              createdAt: {
                [Op.gte]: startDt.toDate(),
              },
            },
            include: [
              {
                model: models.sellOrder,
                attributes: ['id', 'giftFlg'],
                required: true,
                include: [
                  {
                    model: models.user,
                    attributes: ['firstName', 'lastName'],
                    required: true,
                  },
                  {
                    model: models.portfolio,
                    attributes: ['subtitle'],
                    required: true,
                  },
                ],
              },
            ],
          })
          .then((newShareTransfers) => {
            newShareTransfers.forEach((newShareTransfer) => {
              activityItems.push({
                id: `new-share-transfer-${newShareTransfer.id}`,
                message: `${newShareTransfer.sellOrder.user.firstName} ${
                  newShareTransfer.sellOrder.user.lastName
                } ${
                  newShareTransfer.sellOrder.giftFlg ? 'gifted' : 'sold'
                } ${numeral(newShareTransfer.soldShares).format(
                  '0,0[.]00'
                )} shares in ${newShareTransfer.sellOrder.portfolio.subtitle}`,
                date: newShareTransfer.createdAt,
                type: 'NEW_SHARE_TRANSFER',
              });
            });
          })
      );

      // User verified
      aPromises.push(
        models.user
          .findAll({
            attributes: ['id', 'firstName', 'lastName', 'verifiedDt'],
            where: {
              verifiedDt: {
                [Op.gte]: startDt.toDate(),
              },
            },
          })
          .then((verifiedUsers) => {
            verifiedUsers.forEach((verifiedUser) => {
              activityItems.push({
                id: `verified-user-${verifiedUser.id}`,
                message: `${verifiedUser.firstName} ${verifiedUser.lastName} got verified`,
                date: verifiedUser.verifiedDt,
                type: 'VERIFIED_USER',
              });
            });
          })
      );

      // User confirmed email
      aPromises.push(
        models.user
          .findAll({
            attributes: ['id', 'firstName', 'lastName', 'confirmationDt'],
            where: {
              confirmationDt: {
                [Op.gte]: startDt.toDate(),
              },
            },
          })
          .then((confirmedUsers) => {
            confirmedUsers.forEach((confirmedUser) => {
              activityItems.push({
                id: `user-confirmed-email-${confirmedUser.id}`,
                message: `${confirmedUser.firstName} ${confirmedUser.lastName} confirmed their email`,
                date: confirmedUser.confirmationDt,
                type: 'USER_CONFIRMED_EMAIL',
              });
            });
          })
      );

      // User created
      aPromises.push(
        models.user
          .findAll({
            attributes: ['id', 'firstName', 'lastName', 'createdAt'],
            where: {
              createdAt: {
                [Op.gte]: startDt.toDate(),
              },
            },
          })
          .then((newUsers) => {
            newUsers.forEach((newUser) => {
              activityItems.push({
                id: `new-user-${newUser.id}`,
                message: `${newUser.firstName} ${newUser.lastName} created an account`,
                date: newUser.createdAt,
                type: 'NEW_USER',
              });
            });
          })
      );

      // User invested
      aPromises.push(
        models.investment
          .findAll({
            attributes: ['id', 'value', 'createdAt'],
            where: {
              createdAt: {
                [Op.gte]: startDt.toDate(),
              },
              cancelledDt: null,
            },
            include: [
              {
                model: models.user,
                attributes: ['firstName', 'lastName'],
                required: true,
              },
              {
                model: models.portfolio,
                attributes: ['subtitle'],
                required: true,
              },
            ],
          })
          .then((newInvestments) => {
            newInvestments.forEach((newInvestment) => {
              activityItems.push({
                id: `new-investment-${newInvestment.id}`,
                message: `${newInvestment.user.firstName} ${
                  newInvestment.user.lastName
                } invested ${numeral(newInvestment.value).format(
                  '$0,0[.]00'
                )} in ${newInvestment.portfolio.subtitle}`,
                date: newInvestment.createdAt,
                type: 'NEW_INVESTMENT',
              });
            });
          })
      );

      // User logged in
      aPromises.push(
        models.userLogin
          .findAll({
            attributes: ['id', 'createdAt'],
            where: {
              createdAt: {
                [Op.gte]: startDt.toDate(),
              },
            },
            include: [
              {
                model: models.user,
                attributes: ['firstName', 'lastName'],
                required: true,
              },
            ],
          })
          .then((newLogins) => {
            newLogins.forEach((newLogin) => {
              activityItems.push({
                id: `new-login-${newLogin.id}`,
                message: `${newLogin.user.firstName} ${newLogin.user.lastName} logged in`,
                date: newLogin.createdAt,
                type: 'NEW_LOGIN',
              });
            });
          })
      );

      // User created reinvest indicator
      aPromises.push(
        models.autoReinvestIndicator
          .findAll({
            attributes: ['id', 'createdAt'],
            where: {
              createdAt: {
                [Op.gte]: startDt.toDate(),
              },
            },
            include: [
              {
                model: models.user,
                attributes: ['firstName', 'lastName'],
                required: true,
              },
              {
                model: models.portfolio,
                attributes: ['subtitle'],
                required: true,
              },
            ],
          })
          .then((newReinvests) => {
            newReinvests.forEach((newReinvest) => {
              activityItems.push({
                id: `new-auto-reinvest-indicator-${newReinvest.id}`,
                message: `${newReinvest.user.firstName} ${newReinvest.user.lastName} started reinvesting dividends in ${newReinvest.portfolio.subtitle}`,
                date: newReinvest.createdAt,
                type: 'NEW_AUTO_REINVEST_INDICATOR',
              });
            });
          })
      );

      // User created monthly investment subscription
      aPromises.push(
        models.autoInvestSubscription
          .findAll({
            attributes: ['id', 'value', 'createdAt'],
            where: {
              createdAt: {
                [Op.gte]: startDt.toDate(),
              },
            },
            include: [
              {
                model: models.user,
                attributes: ['firstName', 'lastName'],
                required: true,
              },
              {
                model: models.portfolio,
                attributes: ['subtitle'],
                required: true,
              },
            ],
          })
          .then((newAutoInvests) => {
            newAutoInvests.forEach((newAutoInvest) => {
              activityItems.push({
                id: `new-auto-invest-subscription-${newAutoInvest.id}`,
                message: `${newAutoInvest.user.firstName} ${
                  newAutoInvest.user.lastName
                } set up recurring monthly investment of ${numeral(
                  newAutoInvest.value
                ).format('$0,0[.]00')} in ${newAutoInvest.portfolio.subtitle}`,
                date: newAutoInvest.createdAt,
                type: 'NEW_AUTO_INVEST_SUBSCRIPTION',
              });
            });
          })
      );

      // User added funding source
      aPromises.push(
        database
          .query(
            `select "users"."firstName", "users"."lastName", "dwollaWebhookEvents"."createdAt", "dwollaWebhookEvents"."id" from "dwollaWebhookEvents"
        JOIN "plaidAccounts" ON "dwollaWebhookEvents"."resourceId" = "plaidAccounts"."dwollaFundingSourceId"
        JOIN "plaidItems" ON "plaidAccounts"."plaidItemId" = "plaidItems"."id"
        JOIN "users" on "plaidItems"."userId" = "users"."id"
        where "topic" = 'customer_funding_source_verified'
        and "dwollaWebhookEvents"."createdAt" >= :startDt
        ;`,
            {
              replacements: {
                startDt: startDt.toDate(),
              },
              type: Sequelize.QueryTypes.SELECT,
            }
          )
          .then((verifiedFundingSources) => {
            verifiedFundingSources.forEach((fundingSource) => {
              activityItems.push({
                id: `verified-funding-source-${fundingSource.id}`,
                message: `${fundingSource.firstName} ${fundingSource.lastName} added a funding source`,
                date: fundingSource.createdAt,
                type: 'NEW_FUNDING_SOURCE',
              });
            });
          })
      );

      return Promise.all(aPromises).then(() => {
        activityItems.sort((a, b) => (a.date < b.date ? 1 : -1));
        return activityItems.slice(0, 100);
      });
    },
    getUserCount: UserService.getUserCount,
    getUserCountCached: UserService.getUserCount,
    getInvestorCount: UserService.getInvestorCount,
    getInvestorCountCached: UserService.getInvestorCount,
    getNewInvestorCount: async (parent, args, { models, roles, user }) => {
      // check context and check for role
      const isAuthorized = getAuthorized('admin', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      const usersWFirstInvestmentDt = await models.user.findAll({
        attributes: [
          'id',
          [fn('min', col('investments.startDt')), 'firstInvestmentDt'],
        ],
        where: {
          id: { [Op.not]: 19063 }, // Exclude review user from new investor counts
        },
        group: 'user.id',
        include: [
          {
            attributes: [],
            required: true,
            model: models.investment,
            where: {
              cancelledDt: null,
            },
          },
        ],
      });

      let count = 0;
      usersWFirstInvestmentDt.forEach((user1) => {
        if (
          user1.dataValues.firstInvestmentDt &&
          (args?.endDt
            ? moment(user1.dataValues.firstInvestmentDt).isSameOrBefore(
                moment(args.endDt)
              )
            : true) &&
          (args?.startDt
            ? moment(user1.dataValues.firstInvestmentDt).isSameOrAfter(
                moment(args.startDt)
              )
            : true)
        ) {
          count += 1;
        }
      });

      return count;
    },
    getInvestorCountExcludingReferrals: async (
      parent,
      args,
      { models, roles, user }
    ) => {
      // check context and check for role
      const isAuthorized = getAuthorized('admin', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      const returnObj = await models.investment.findAll({
        attributes: [
          [
            Sequelize.fn('DISTINCT', Sequelize.col('userId')),
            'distinctUserIds',
          ],
        ],
        group: ['distinctUserIds'],
        where: {
          cancelledDt: null,
          referralId: null,
          dividendId: null,
          userId: { [Op.not]: 19063 }, // Exclude review user from investor counts
        },
      });

      return returnObj.length;
    },
    getProfileCompleteUserCount: (parent, args, { models, roles, user }) => {
      // check context and check for role
      const isAuthorized = getAuthorized('admin', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      return models.user.count({
        where: {
          dwollaId: {
            [Op.not]: null,
          },
        },
      });
    },
    allUsers: (parent, args, { models, roles, user }) => {
      // check context and check for role
      const isAuthorized = getAuthorized('admin', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      const userFilter = {};
      if (args.excludePermanentlySuspendedFlg) {
        userFilter.permSuspendedFlg = {
          [Op.not]: true,
        };
      }
      return models.user.findAll({
        attributes: ['id', 'firstName', 'lastName', 'createdAt'], // NOTE: This is only used in the CMS so this is safe
        where: userFilter,
      });
    },
    allInvestors: (parent, args, { models, roles, user }) => {
      const isAuthorized = getAuthorized('admin', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      return models.user.findAll({
        attributes: ['id', 'firstName', 'lastName'], // NOTE: This is only used in the CMS so this is safe
        include: [
          {
            attributes: [],
            required: true,
            model: models.investment,
            where: {
              cancelledDt: null,
            },
          },
        ],
      });
    },
    allInvestorsCached: (parent, args, { models, roles, user }) => {
      const isAuthorized = getAuthorized('admin', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      return models.user.findAll({
        attributes: ['id', 'firstName', 'lastName'],
        include: [
          {
            attributes: [],
            required: true,
            model: models.investment,
            where: {
              cancelledDt: null,
            },
          },
        ],
      });
    },
    allAnonymousInvestorsCached: async (parent, args, { models }) =>
      models.user.findAll({
        attributes: [],
        where: { id: { [Op.not]: 76 } }, // NOTE:Removing users with sold shares
        include: [
          {
            attributes: [],
            required: true,
            model: models.investment,
            where: {
              cancelledDt: null,
            },
          },
        ],
      }),
    allInvestorsExcludingReferralRewards: (
      parent,
      args,
      { models, roles, user }
    ) => {
      const isAuthorized = getAuthorized('admin', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      return models.user.findAll({
        attributes: ['id', 'firstName', 'lastName'], // NOTE: This is only used in the CMS so this is safe
        include: [
          {
            attributes: [],
            required: true,
            model: models.investment,
            where: {
              cancelledDt: null,
              referralId: null,
              dividendId: null,
            },
          },
        ],
      });
    },
    getSignature: async (parents, args) => {
      const signature = await cloudinary.v2.utils.api_sign_request(
        JSON.parse(args.data),
        process.env.CLOUDINARY_API_SECRET
      );
      return signature;
    },
    // getDwollaUserTransferFeed
    getUserTransferFeed: async (
      parent,
      { filter, sort, pagination },
      { models, user, roles }
    ) => {
      const isAuthorized = getAuthorized('admin', roles);
      if (!isAuthorized && !user) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }

      const transferFilter = {
        userId: (filter && filter.userId) || user.id,
      };
      if (filter && filter.transferType && filter.transferType !== 'all') {
        transferFilter.type = filter.transferType;
      }

      const userTransfers = await models.transfer.findAndCountAll({
        where: transferFilter,
        order: [
          [(sort && sort.field) || 'createdAt', (sort && sort.order) || 'DESC'],
        ],
        offset: pagination.page * pagination.perPage,
        limit: pagination.perPage,
      });

      userTransfers.transferTypes = models.transfer
        .findAll({
          attributes: [
            [Sequelize.fn('DISTINCT', Sequelize.col('type')), 'distinctTypes'],
          ],
          group: ['distinctTypes'],
          where: {
            userId: (filter && filter.userId) || user.id,
          },
        })
        .then((res) => {
          const types = [];
          res.forEach((t) => {
            if (types.indexOf(t.dataValues.distinctTypes) === -1) {
              types.push(t.dataValues.distinctTypes);
            }
          });
          return types;
        });

      return userTransfers;
    },

    getFrontendUserTransferFeed: async (
      parent,
      { filter, sort, pagination },
      { models, user, roles }
    ) => {
      const isAuthorized = getAuthorized('admin', roles);
      if (!isAuthorized && !user) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      const transferFilter = {
        userId: (filter && filter.userId) || user.id,
      };
      if (filter && filter.transferType && filter.transferType !== 'all') {
        transferFilter.type = filter.transferType;
      }

      const userTransfers = await models.transfer.findAndCountAll({
        where: transferFilter,
        order: [
          [(sort && sort.field) || 'createdAt', (sort && sort.order) || 'DESC'],
        ],
        offset: pagination.page * pagination.perPage,
        limit: pagination.perPage,
        include: [
          {
            attributes: ['id'],
            model: models.buyDirection,
            required: false,
            include: [
              {
                attributes: ['id', 'accountId'],
                model: models.subAccount,
                required: false,
                include: [
                  {
                    attributes: ['id', 'name'],
                    model: models.subAccountType,
                    required: false,
                  },
                ],
              },
            ],
          },
          {
            attributes: ['id'],
            model: models.dividend,
            required: false,
            include: [
              {
                attributes: ['id'],
                model: models.monthlyPortfolioFinancialActual,
                required: false,
                include: [
                  {
                    attributes: ['id', 'subtitle'],
                    model: models.portfolio,
                    required: false,
                  },
                ],
              },
            ],
          },
          {
            attributes: ['id'],
            model: models.investment,
            required: false,
            include: [
              {
                attributes: ['id', 'subtitle'],
                model: models.portfolio,
                required: false,
              },
            ],
          },
          {
            attributes: ['id'],
            model: models.shareTransfer,
            required: false,
            include: [
              {
                model: models.sellOrder,
                required: false,
                attributes: ['id'],
                include: [
                  {
                    model: models.portfolio,
                    attributes: ['id', 'subtitle'],
                    required: false,
                  },
                ],
              },
            ],
          },
        ],
      });

      const transferTypes = await models.transfer
        .findAll({
          attributes: [
            [Sequelize.fn('DISTINCT', Sequelize.col('type')), 'distinctTypes'],
          ],
          group: ['distinctTypes'],
          where: {
            userId: (filter && filter.userId) || user.id,
          },
        })
        .then((res) => {
          const types = [];
          res.forEach((t) => {
            if (types.indexOf(t.dataValues.distinctTypes) === -1) {
              types.push(t.dataValues.distinctTypes);
            }
          });
          return types;
        });
      const { count, rows } = userTransfers;
      return {
        transferTypes,
        count,
        rows: rows.map(async (transfer) => {
          const { id, createdAt, type, completedDt } = transfer;
          const [dwollaTransfers, millenniumTrustInvestmentTransfer] =
            await Promise.all([
              DwollaService.getDwollaUserTransfersFromDbTransfer(transfer),
              getMillenniumTrustInvestmentTransferFromDbTransfer(transfer),
            ]);
          let bankTransfers = null;
          const energeaLogoIconClassName = constants.iconClasses.energeaLogo;
          const bankIconClassName = constants.iconClasses.bank;
          const missingIconClassName = constants.iconClasses.missing;
          // Handle Dwolla transfers
          if (dwollaTransfers) {
            bankTransfers = dwollaTransfers.map(async (t) => {
              const {
                id: dtId,
                status,
                resourceType,
                source,
                destination,
                amount,
              } = t;
              let sourceName = '';
              let destinationName = '';
              let sourceIconClass = missingIconClassName;
              let destinationIconClass = missingIconClassName;

              if (source?.name) {
                sourceName = await source.name;
                sourceIconClass = bankIconClassName;
              }
              if (destination?.name) {
                destinationName = await destination.name;
                destinationIconClass = bankIconClassName;
              }
              if (resourceType === 'customer') {
                sourceName = 'Energea Wallet';
                sourceIconClass = energeaLogoIconClassName;
              }
              if (type === 'investment') {
                destinationName =
                  transfer.investment?.portfolio?.subtitle || '';
                destinationIconClass = energeaLogoIconClassName;
              }
              if (type === 'shareTransfer') {
                sourceName =
                  transfer?.shareTransfer?.sellOrder?.portfolio?.subtitle ||
                  'Energea';
                sourceIconClass = energeaLogoIconClassName;
              }
              if (type === 'dividend') {
                sourceName =
                  transfer?.dividend?.monthlyPortfolioFinancialActual?.portfolio
                    ?.subtitle;
                sourceIconClass = energeaLogoIconClassName;
              }
              if (type === 'buyDirection') {
                sourceName = `${transfer?.buyDirection?.subAccount?.subAccountType?.name} ( #
                ${transfer?.buyDirection?.subAccount?.accountId})`;
                // TODO: replace with entrust logo:
                sourceIconClass = energeaLogoIconClassName;
                destinationName = `Energea IRA Balance (#${transfer.buyDirection.subAccount.accountId})`;
                destinationIconClass = energeaLogoIconClassName;
              }
              const returnSource = {
                id: source?.id || `${dtId}-source`,
                name: sourceName || '',
                // TODO: set icon logic
                iconClass: sourceIconClass,
              };
              const returnDestination = {
                id: source?.id || `${dtId}-destination`,
                name: destinationName || '',
                // TODO: set icon logic
                iconClass: destinationIconClass,
              };
              return {
                id: dtId,
                status,
                amount: parseFloat(amount?.value || 0),
                source: returnSource,
                destination: returnDestination,
              };
            });
          }
          // Handle Millennium Trust investment transfer
          // console.log('hit', millenniumTrustInvestmentTransfer);
          if (millenniumTrustInvestmentTransfer) {
            const { amount } = millenniumTrustInvestmentTransfer;
            const returnDestination = {
              id: '1-temp',
              name: transfer.investment.portfolio.subtitle,
              iconClass: energeaLogoIconClassName,
            };
            const returnSource = {
              id: '2-temp',
              name: millenniumTrustInvestmentTransfer.source || '',
              // TODO: replace with MT logo
              iconClass: bankIconClassName,
            };

            bankTransfers = [
              {
                id: 1,
                status: status || 'error',
                amount,
                source: returnSource,
                destination: returnDestination,
              },
            ];
          }

          const getStatus = (transfer) => {
            const getTransferPending = (transfer) => {
              const pendingMTInvestmentStatuses = [
                'Awaiting FCM Account Open',
                'Awaiting Funds',
                'Awaiting Pre-Custody',
                'Awaiting Resolution',
                'Reviewing',
                'Received',
              ];
              let pending = false;
              if (transfer.bankTransfers && transfer.bankTransfers.length > 0) {
                pending =
                  transfer.bankTransfers.filter(
                    (dwTransfer) => dwTransfer.status === 'pending'
                  ).length > 0;
              } else if (transfer.millenniumTrustInvestmentTransfer) {
                pending =
                  pendingMTInvestmentStatuses.indexOf(
                    transfer.millenniumTrustInvestmentTransfer.status
                  ) > -1;
              }
              return pending;
            };
            const getTransferCancelledOrFailed = (transfer) => {
              const cancelledMTInvestmentStatuses = ['Cancelled'];

              let cancelledOrFailed = false;
              if (transfer.bankTransfers && transfer.bankTransfers.length > 0) {
                cancelledOrFailed =
                  transfer.bankTransfers.filter(
                    (dwTransfer) =>
                      dwTransfer.status === 'cancelled' ||
                      dwTransfer.status === 'failed'
                  ).length > 0;
              } else if (transfer.millenniumTrustInvestmentTransfer) {
                cancelledOrFailed =
                  cancelledMTInvestmentStatuses.indexOf(
                    transfer.millenniumTrustInvestmentTransfer.status
                  ) > -1;
              }
              return cancelledOrFailed;
            };
            const isPending = getTransferPending(transfer);
            const isCancelledOrFailed = getTransferCancelledOrFailed(transfer);
            if (isPending) {
              return 'pending';
            }
            if (isCancelledOrFailed) {
              return 'cancelled/failed';
            }
            return 'completed';
          };

          const getTypeLabelText = (transferType) => {
            switch (transferType) {
              case 'deposit':
                return 'Transfer to Wallet';
              case 'buyDirection':
                return 'IRA Transfer';
              case 'shareTransfer':
                return 'Share Transfer';
              case 'investment':
                return 'Investment';
              case 'dividend':
                return 'Dividend';
              case 'withdrawal':
                return 'Withdrawal';
              default:
                return transferType;
            }
          };

          const returnObj = {
            id: id || 1,
            label: `${moment(createdAt).format('M/D/Y')} - ${getTypeLabelText(
              type
            )}`,
            source: bankTransfers?.[0]?.source || null,
            destination: bankTransfers?.[0]?.destination || null,
            bankTransfers,
            createdAt,
            completedDt,
            type,
          };
          const status = getStatus(transfer);
          returnObj.status = status;
          let amount = 0;
          if (bankTransfers && bankTransfers.length > 0) {
            for (const t of bankTransfers) {
              const awaitedResponse = await t;
              const counter = awaitedResponse.amount;
              amount += counter || 0;
            }
          }
          returnObj.amount = amount;
          if (returnObj.amount === 0) {
            returnObj.amount = null;
          }
          return returnObj;
        }),
      };
    },
    checkUserExists: async (parent, { email }, { models }) => {
      try {
        if (!email) {
          return { exists: false };
        }

        // Check if user exists with this email
        const user = await models.user.findOne({
          where: {
            email: email.toLowerCase().trim(),
          },
          attributes: ['id'], // Only select id to minimize data transfer
        });

        return { exists: !!user };
      } catch (error) {
        console.error('Error checking if user exists:', error);
        return { exists: false };
      }
    },
    myAccessibleAccounts: async (
      parent,
      args,
      { accessibleAccounts, user }
    ) => {
      if (!user) {
        throw new ApolloError('User not authenticated', 'UNAUTHENTICATED');
      }

      // Return the accessible accounts from context if available
      if (accessibleAccounts) {
        return accessibleAccounts;
      }

      // Fallback to fetching directly if not in context
      try {
        return await getUserAccessibleAccounts(user.id);
      } catch (error) {
        console.error('Error fetching accessible accounts:', error);
        throw new ApolloError(
          'Failed to fetch accessible accounts',
          'INTERNAL_ERROR'
        );
      }
    },

    getSMSMFAEnrollments: async (parent, args, { user }) => {
      if (!user || !user.authId) {
        throw new AuthenticationError('Authentication required');
      }

      try {
        const enrollments = await getSMSMFAEnrollments(user.authId);
        return enrollments;
      } catch (error) {
        console.error('Error getting SMS MFA enrollments:', error);
        throw new ApolloError('Failed to get SMS MFA enrollments');
      }
    },
  },
  Mutation: {
    claimIRAPromo2025: async (parent, args, { user, models }) => {
      if (!user) {
        return new ApolloError('User not found', 'VALIDATION');
      }
      const mExpirationDt = moment(constants.iraPromo2025.expirationDt);
      const now = moment();
      if (now.isAfter(mExpirationDt)) {
        return new ApolloError('Promo has expired', 'VALIDATION');
      }
      if (user.type !== 'personal') {
        return new ApolloError(
          'Promo is not valid for business users',
          'VALIDATION'
        );
      }
      const claimedPromo = await user.getPromoCodes({
        attributes: ['id'],
        where: {
          promoType: constants.iraPromo2025.typeName,
        },
        raw: true,
      });
      if (claimedPromo.length > 0) {
        return new ApolloError(
          'User has already claimed this promo',
          'VALIDATION'
        );
      }

      return models.promoCode
        .create({
          userId: user.id,
          promoType: constants.iraPromo2025.typeName,
          expirationDt: constants.iraPromo2025.expirationDt,
        })
        .then((resp) => !!resp);
    },
    manuallyConfirmEmail: async (
      parent,
      { id },
      { models, user: contextUser, roles }
    ) => {
      const isAuthorized = getAuthorized('admin', roles);
      if (!isAuthorized && !contextUser) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user: contextUser,
        });
      }

      const dbUser = await models.user.findByPk(id, {
        attributes: ['id', 'confirmationDt'],
      });
      if (!dbUser) {
        throw new ApolloError(`No user exists with id ${id}`, 'VALIDATION');
      }
      if (dbUser.confirmationDt) {
        throw new ApolloError('User email already confirmed', 'VALIDATION');
      }

      const updatedUser = dbUser;
      updatedUser.confirmationDt = new Date();
      return updatedUser.save({
        authId: contextUser?.authId || contextUser?.oktaId,
      });
    },
    setAllInvestorsLongLat: async (
      parent,
      args,
      { models, user: contextUser, roles }
    ) => {
      const isAuthorized = getAuthorized('admin', roles);
      if (!isAuthorized && !contextUser) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user: contextUser,
        });
      }
      const allInvestors = await models.user.findAll({
        where: {
          address1: {
            [Op.not]: null,
          },
          state: {
            [Op.not]: null,
          },
          postalCode: {
            [Op.not]: null,
          },
          latitude: null,
          longitude: null,
        },
        include: [
          {
            attributes: [],
            model: models.investment,
            required: true,
          },
        ],
      });
      for (let index = 0; index < allInvestors.length; index += 1) {
        const user = allInvestors[parseInt(index, 10)];
        if (!user.latitude || !user.longitude) {
          try {
            // eslint-disable-next-line no-await-in-loop
            await GoogleMapsService.getUserLatitudeLongitude(user).then(
              (res) => {
                if (!res) {
                  console.error('No lat/lng returned');
                  return null;
                }
                const { latitude, longitude } = res;
                const updatedUser = user;
                updatedUser.longitude = longitude;
                updatedUser.latitude = latitude;
                return updatedUser.save();
              }
            );
          } catch (error) {
            console.error('Error saving lat/lon to investor', error);
          }
        }
      }

      return true;
    },
    handleUserModalForm: async (
      parent,
      { input },
      { models, user, roles, isTestUser }
    ) => {
      if (isTestUser) {
        return true;
      }
      const isAuthorized = getAuthorized('admin', roles);
      if (!isAuthorized && user.id !== input?.userId) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      const dbUser = await models.user.findByPk(input.userId);
      if (!dbUser) {
        return new ApolloError(`No database user found`, 'VALIDATION', {
          userId: input.userId,
        });
      }

      const existingModalAlertEvent = await ModalAlertEvent.findOne({
        where: {
          userId: input.userId,
          type: input.type,
        },
      }).catch((e) => {
        const eMsg = 'Error retrieving ModalAlertEvent';
        console.error(eMsg, e);
        return new ApolloError(eMsg);
      });

      if (existingModalAlertEvent) {
        const updateObj = existingModalAlertEvent;
        updateObj.completedDt = new Date();
        updateObj.save(user).catch((e) => {
          const eMsg = 'Error updating ModalAlertEvent';
          console.error(eMsg, e);
          return new ApolloError(eMsg);
        });
      } else {
        ModalAlertEvent.create(
          {
            userId: input.userId,
            completedDt: new Date(),
            type: input.type,
          },
          user
        ).catch((e) => {
          const eMsg = 'Error creating ModalAlertEvent';
          console.error(eMsg, e);
          return new ApolloError(eMsg);
        });
      }
      const data = JSON.parse(input.data);
      if (!data) {
        return true;
      }
      switch (input.type) {
        case 'unknownLeadSource':
          SlackService.logToSlack({
            type: 'lead-source-updates',
            title: `Click here to update HubSpot lead source override for ${user.fullName}`,
            url: `https://app.hubspot.com/contacts/********/contact/${user.hubSpotContactId}/`,
            data: [
              data?.channel && {
                label: 'Channel',
                value: data?.channel,
              },
              {
                label: 'Comment',
                value: data?.comments,
              },
            ],
            // NOTE: in progress
            // taggedUsers: ['gray'],
          });
          break;

        default:
          break;
      }
      return true;
    },
    createUserActivitySummaryDownload: async (
      parent,
      { input },
      { models, user, roles }
    ) => {
      const isAuthorized = getAuthorized('admin', roles);
      if (!isAuthorized && !user) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }

      const { userId, accountFilter } = input;

      const dbUser = await models.user.findByPk(userId);
      if (!dbUser) {
        return new ApolloError(`No database user found`, 'VALIDATION', {
          userId,
        });
      }
      const lintedAccountFilter = accountFilter;
      if (
        accountFilter &&
        accountFilter.allAccounts === false &&
        !accountFilter.subAccountId
      ) {
        lintedAccountFilter.subAccountId = null;
      }
      return UserService.navBasedIRRExcelDownload(
        dbUser,
        {
          accountFilter: lintedAccountFilter,
          userId,
        },
        {
          models,
        }
      );
    },
    suspendUser: async (parent, { id }, { roles, user, models }) => {
      const isAuthorized = getAuthorized('userEdit', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      const dbUser = await User.findByPk(id, {
        include: [
          {
            model: models.autoInvestSubscription,
            required: false,
            where: {
              inactive: {
                [Op.not]: true,
              },
            },
          },
          {
            model: models.subAccount,
            required: false,
            where: {
              closedDt: null,
              subAccountTypeId: constants.millenniumTrustSubAccountTypeID,
            },
          },
        ],
      });

      if (!dbUser)
        return new ApolloError('No database user found', 'VALIDATION', {
          userId: id,
        });

      if (
        dbUser.autoInvestSubscriptions &&
        dbUser.autoInvestSubscriptions.length > 0
      ) {
        dbUser.autoInvestSubscriptions.forEach((autoInvestSubscription) => {
          const updatedAutoInvestSubscription = autoInvestSubscription;
          updatedAutoInvestSubscription.inactive = true;
          updatedAutoInvestSubscription.save().then(
            () => {},
            (err) => {
              console.error(
                `Error deactivating autoInvestSubscription ${autoInvestSubscription.id} for manually suspended user ${id}.`,
                err
              );
              sendIssueEmail({
                description:
                  'Error deactivating autoInvestSubscription for suspended user',
                oData: {
                  dbUser,
                  error: err,
                  autoInvestSubscription,
                },
              });
            }
          );
        });
      }

      if (dbUser.subAccounts?.length > 0) {
        sendNotifyMTCSuspendedUserEmail({
          fullName: `${dbUser.firstName} ${dbUser.lastName}`,
          email: dbUser.email,
        });
      }

      const updatedUser = dbUser;
      updatedUser.permSuspendedFlg = true;
      const res = await updatedUser.save();
      if (res.dwollaId) {
        // NOTE: sendCustomerPermanentlySuspendedEmail is sent in the dwolla suspended webhook handler
        await DwollaService.suspendUser(res.dwollaId).catch((e) =>
          console.error('Error suspending dwolla user', e)
        );
      } else {
        console.warn(
          `Skipping suspend dwolla user on user ${dbUser.id}. No dwolla id.`
        );
        sendCustomerPermanentlySuspendedEmail({
          firstName: dbUser.firstName,
          lastName: dbUser.lastName,
          email: dbUser.email,
        });
      }
      if (res.hubSpotContactId) {
        HubSpotService.updateContact(dbUser.hubSpotContactId, {
          permSuspendedFlg: true,
        });
      }
      return res;
    },
    deactivateUser: async (parent, { id }, { roles, user, models }) => {
      const isAuthorized = getAuthorized('userEdit', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      const dbUser = await User.findByPk(id, {
        include: [
          {
            model: models.autoInvestSubscription,
            required: false,
            where: {
              inactive: {
                [Op.not]: true,
              },
            },
          },
        ],
      });

      if (!dbUser)
        return new ApolloError('No database user found', 'VALIDATION', {
          userId: id,
        });

      if (
        dbUser.autoInvestSubscriptions &&
        dbUser.autoInvestSubscriptions.length > 0
      ) {
        dbUser.autoInvestSubscriptions.forEach((autoInvestSubscription) => {
          const updatedAutoInvestSubscription = autoInvestSubscription;
          updatedAutoInvestSubscription.inactive = true;
          updatedAutoInvestSubscription.save().then(
            () => {},
            (err) => {
              console.error(
                `Error deactivating autoInvestSubscription ${autoInvestSubscription.id} for manually deactivated user ${id}.`,
                err
              );
              sendIssueEmail({
                description:
                  'Error deactivating autoInvestSubscription for deactivated user',
                oData: {
                  dbUser,
                  error: err,
                  autoInvestSubscription,
                },
              });
            }
          );
        });
      }

      if (dbUser.dwollaId) {
        await DwollaService.deactivateUser(dbUser.dwollaId);
      } else {
        console.warn(
          `Skipping deactivate dwolla user on user ${dbUser.id}. No dwolla id.`
        );
      }

      return dbUser;
    },
    reactivateUser: async (parent, { id }, { roles, user }) => {
      const isAuthorized = getAuthorized('userEdit', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      const dbUser = await User.findByPk(id);
      if (!dbUser)
        return new ApolloError('No database user found', 'VALIDATION', {
          userId: id,
        });

      if (dbUser.dwollaId) {
        await DwollaService.reactivateUser(dbUser.dwollaId);
      } else {
        console.warn(
          `Skipping reactivate dwolla user on user ${dbUser.id}. No dwolla id.`
        );
      }

      return dbUser;
    },
    syncOktaUserFirstLastName: async (
      parent,
      args,
      { models, user, roles }
    ) => {
      const isAuthorized = getAuthorized('userEdit', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      return models.user.findAndCountAll().then((users) => {
        users.rows.forEach((usr) => {
          oktaClient.getUser(usr.dataValues.oktaId).then((oktaUser) => {
            const {
              profile: { firstName, lastName },
            } = oktaUser;
            const saveObj = usr;
            saveObj.firstName = firstName;
            saveObj.lastName = lastName;
            return saveObj.save();
          });
        });
      });
    },
    syncOktaUserEmail: async (parent, args, { models, user, roles }) => {
      const isAuthorized = getAuthorized('userEdit', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      return models.user.findAndCountAll().then((users) => {
        users.rows.forEach((usr) => {
          oktaClient
            .getUser(usr.dataValues.oktaId)
            .then(
              (oktaUser) => {
                const {
                  profile: { email },
                } = oktaUser;
                const saveObj = usr;
                saveObj.email = email;
                return saveObj.save();
              },
              (e) => {
                console.error(
                  `Failed to sync email for user. Error: ${e}`.red.bold,
                  usr
                );
              }
            )
            .catch((e) =>
              console.error(
                `Failed to sync email for user. Error: ${e}`.red.bold,
                usr
              )
            );
        });
      });
    },
    syncOktaUserSSN: async (parent, args, { models, user, roles }) => {
      const isAuthorized = getAuthorized('userEdit', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      return models.user
        .findAndCountAll({
          where: {
            ssn: null,
          },
        })
        .then((users) => {
          users.rows.forEach((usr) => {
            oktaClient
              .getUser(usr.dataValues.oktaId)
              .then(
                (oktaUser) => {
                  const {
                    profile: { ssn },
                  } = oktaUser;
                  const saveObj = usr;
                  saveObj.ssn = ssn;
                  return saveObj.save();
                },
                (e) => {
                  console.error(
                    `Failed to sync SSN for user. Error: ${e}`.red.bold,
                    usr
                  );
                }
              )
              .catch((e) =>
                console.error(
                  `Failed to sync SSN for user. Error: ${e}`.red.bold,
                  usr
                )
              );
          });
        });
    },
    syncUserVerifiedDt: async (parent, args, { models, user, roles }) => {
      const isAuthorized = getAuthorized('userEdit', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      return models.user.findAll().then((users) =>
        users.map((usr) =>
          models.dwollaWebhookEvent
            .findAll({
              where: {
                topic: 'customer_verified',
                resourceId: usr.dwollaId,
              },
            })
            .then((dwWebhookEvents) => {
              if (dwWebhookEvents && dwWebhookEvents.length > 0) {
                const saveObj = usr;
                saveObj.verifiedDt = dwWebhookEvents[0].updatedAt;
                return saveObj.save();
              }
              return usr.save();
            })
        )
      );
    },
    createUser: async (parent, { input }, { models, user, roles }) => {
      const changeObj = { ...input };
      if (input.employeeFlg) {
        const isAuthorized = getAuthorized('IT', roles);
        if (!isAuthorized) {
          return new ForbiddenError('You do not have access to this endpoint', {
            user,
          });
        }
        const profile = {
          given_name: changeObj.firstName,
          family_name: changeObj.lastName,
          email: changeObj.email,
          name: `${changeObj.firstName} ${changeObj.lastName}`,
        };
        const randomPassword = passwordGenerator.generate({
          length: 10,
          numbers: true,
          strict: true,
          symbols: true,
          uppercase: true,
          lowercase: true,
          excludeSimilarCharacters: true,
        });

        // Create Auth0 User
        // TODO: handle error
        const auth0User = await createAuth0User({
          firstName: changeObj.firstName,
          lastName: changeObj.lastName,
          email: profile.email,
          password: randomPassword,
          user_metadata: {
            roles: ['CMS', 'Admin'],
            forcePasswordReset: true,
          },
        }).catch((e) => {
          console.error('Error creating Auth0 user', e);
          throw new ApolloError('Error creating Auth0 user');
        });

        if (!auth0User?.user_id) {
          console.error('Error creating Auth0 user', auth0User);
          throw new ApolloError('Error creating Auth0 user');
        }
        changeObj.authId = auth0User.user_id;
        const { employeeTitle } = input;
        delete changeObj.employeeTitle;
        delete changeObj.employeeFlg;

        // Create Internal User - Database
        const internalUser = await models.user.create(changeObj, user);

        const { firstName, lastName, email, id } = internalUser;

        const employeeObj = {
          firstName,
          lastName,
          email,
          userId: id,
          jobTitle: employeeTitle,
        };

        // Create Employee - Database
        const existingEmployees = await models.employee.findOne({
          where: { email: input.email },
        });

        if (!existingEmployees) {
          await models.employee.create(employeeObj, user).then(
            (newEmployee) => {
              // NOTE: jobTitle is also on employeeObj and should be saved in .create() above, but its not... -JS 3/8/2024
              const updatedEmployee = newEmployee;
              updatedEmployee.jobTitle = employeeTitle;
              updatedEmployee.save();
              sendNewEmployeeEmail({
                firstName,
                lastName,
                email,
                password: randomPassword,
                loginLink: `${process.env.CMS_HOST}/login`,
              });

              return newEmployee.setUser(internalUser);
            },
            (e) => console.error('Error creating employee', e)
          );
        }

        return internalUser;
      }

      // Handle Non-Employee Users
      delete changeObj.employeeFlg;
      delete changeObj.employeeTitle;

      if (!changeObj.type) {
        changeObj.type = 'personal';
      }

      return models.user.create(changeObj, user);
    },
    deleteUser: async (
      parent,
      { id },
      { models, roles, user: contextUser }
    ) => {
      const isAuthorized =
        contextUser?.id === id || getAuthorized('userEdit', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user: contextUser,
        });
      }
      if (id === constants.shareBuybackUserId) {
        throw new ApolloError(
          `Cannot delete protected user id ${constants.shareBuybackUserId}`,
          'VALIDATION'
        );
      }
      const dbUser = await User.findByPk(id);
      const { firstName, lastName, email } = dbUser;
      if (!dbUser)
        return new ApolloError('No user found', 'VALIDATION', {
          userId: id,
        });
      const canDelete = await UserService.canAutoDeleteAccount(dbUser);
      if (!canDelete) {
        return new ApolloError(
          'User cannot be automatically deleted. Offboarding steps still remain.',
          'VALIDATION',
          {
            userId: id,
          }
        );
      }

      if (process.env.NODE_ENV === 'production') {
        try {
          // 1) find and delete Okta User
          if (dbUser.oktaId) {
            const oktaUser = await oktaClient.getUser(dbUser.oktaId);
            if (oktaUser) {
              oktaUser
                .deactivate()
                .then(() => oktaUser.delete()) // TODO: consider only deactivating from this endpoint to prevent mistakes
                .then(
                  () =>
                    console.log('Okta User successfully deleted'.green.bold),
                  (e) => console.error('Error deactivating Okta User', e)
                );
            } else {
              console.error('Okta User not found'.red.bold, oktaUser);
            }
          }
          // 2) find and delete Auth0 User
          if (dbUser.authId) {
            await deleteAuth0User(dbUser.authId);
          }
          // 3) find and delete Dwolla User
          if (dbUser.dwollaId) {
            DwollaService.deactivateUser(dbUser.dwollaId);
          }
          // 4) find and delete HubSpot Contact
          if (dbUser.hubSpotContactId) {
            await HubSpotService.deleteContact(dbUser.hubSpotContactId)
              .then(() =>
                console.log('HubSpot contact successfully deleted'.green.bold)
              )
              .catch((error) =>
                console.error('Error deleting HubSpot contact', error)
              );
          }
        } catch (e) {
          console.error('Error deleting user', e);
        }
      }
      // 5) find and delete DB User
      return models.user
        .destroy({
          where: {
            id,
          },
          authId: contextUser?.authId || contextUser?.oktaId,
        })
        .then(() => {
          sendAccountDeletionConfirmationEmail({ firstName, lastName, email });
          return {
            id,
          };
        });
    },
    signup: (
      parent,
      {
        input: {
          email,
          firstName,
          lastName,
          password,
          primaryPhone,
          referral,
          promoCode,
          type,
          intendedFundingSrcType,
          identity,
        },
      },
      { req }
    ) =>
      signup({
        email,
        firstName,
        lastName,
        password,
        primaryPhone,
        referral,
        promoCode,
        req,
        type,
        intendedFundingSrcType,
        identity,
      }), // return the promise so Graphql knows to wait
    getAuth0Tokens: (parent, { input }, { req }) => {
      return getAuth0Tokens(input, req);
    },
    enrollInMFA, // return the promise so Graphql knows to wait
    disableMFA, // return the promise so Graphql knows to wait
    sendPasswordResetEmail,
    createTOTPEnrollment: async (parent, args, { user }) => {
      if (!user || !user.authId) {
        throw new ApolloError('User not authenticated or missing Auth0 ID');
      }

      try {
        const enrollment = await createTOTPEnrollment(user.authId);
        return enrollment;
      } catch (error) {
        console.error('Error creating TOTP enrollment:', error);
        throw new ApolloError('Failed to create TOTP enrollment');
      }
    },
    confirmTOTPEnrollment: async (parent, { otp, enrollmentId }, { user }) => {
      if (!user || !user.authId) {
        throw new ApolloError('User not authenticated or missing Auth0 ID');
      }

      if (!otp) {
        throw new ApolloError('OTP code is required');
      }

      try {
        const confirmation = await confirmTOTPEnrollment(
          user.authId,
          otp,
          enrollmentId
        );
        return confirmation;
      } catch (error) {
        console.error('Error confirming TOTP enrollment:', error);
        throw new ApolloError('Failed to confirm TOTP enrollment');
      }
    },
    createSMSMFAEnrollment: async (
      parent,
      { phoneNumber, mfaToken },
      { user }
    ) => {
      // Allow either authenticated user OR MFA token for incomplete enrollments
      if (!user?.authId && !mfaToken) {
        throw new ApolloError(
          'Either user authentication or MFA token is required'
        );
      }

      if (!phoneNumber) {
        throw new ApolloError('Phone number is required');
      }

      try {
        const enrollment = await createSMSMFAEnrollment(
          user?.authId || null,
          phoneNumber,
          mfaToken
        );
        return enrollment;
      } catch (error) {
        console.error('Error creating SMS MFA enrollment:', error);
        throw new ApolloError('Failed to create SMS MFA enrollment');
      }
    },
    sendSMSVerificationCode: async (parent, { enrollmentId }, { user }) => {
      if (!user || !user.authId) {
        throw new ApolloError('User not authenticated or missing Auth0 ID');
      }

      if (!enrollmentId) {
        throw new ApolloError('Enrollment ID is required');
      }

      try {
        const response = await sendSMSVerificationCodeById(enrollmentId);
        return response;
      } catch (error) {
        console.error('Error sending SMS verification code:', error);
        throw new ApolloError('Failed to send SMS verification code');
      }
    },
    confirmSMSMFAEnrollment: async (
      parent,
      { enrollmentId, verificationCode },
      { user }
    ) => {
      if (!user || !user.authId) {
        throw new ApolloError('User not authenticated or missing Auth0 ID');
      }

      if (!enrollmentId) {
        throw new ApolloError('Enrollment ID is required');
      }

      if (!verificationCode) {
        throw new ApolloError('Verification code is required');
      }

      try {
        const confirmation = await confirmSMSMFAEnrollment(
          user.authId,
          enrollmentId,
          verificationCode
        );
        return confirmation;
      } catch (error) {
        console.error('Error confirming SMS MFA enrollment:', error);
        throw new ApolloError('Failed to confirm SMS MFA enrollment');
      }
    },

    deleteSMSMFAEnrollment: async (parent, { enrollmentId }, { user }) => {
      if (!user || !user.authId) {
        throw new AuthenticationError('Authentication required');
      }

      if (!enrollmentId) {
        throw new ApolloError('Enrollment ID is required');
      }

      try {
        const result = await deleteSMSMFAEnrollment(user.authId, enrollmentId);
        return result;
      } catch (error) {
        console.error('Error deleting SMS MFA enrollment:', error);
        throw new ApolloError('Failed to delete SMS MFA enrollment');
      }
    },

    createDwollaCustomer: (parent, { userId }, { models, roles, user }) => {
      const isAuthorized = getAuthorized('userEdit', roles);
      if (!isAuthorized && (!user || userId !== user.id)) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      return models.user.findByPk(userId).then((dbUser) => {
        if (!dbUser || !dbUser.oktaId) return null;
        if (dbUser.dwollaId)
          throw new ApolloError(
            'New user already has Dwolla account.',
            'VALIDATION',
            {
              userId,
            }
          );
        return oktaClient.getUser(dbUser.oktaId).then((oktaUser) => {
          if (!oktaUser) return null;
          const returnObj = oktaUser;
          const {
            profile: { firstName, lastName, email },
          } = returnObj;

          if (!firstName || !lastName)
            throw new ApolloError(
              'User requires firstName and lastName',
              'VALIDATION'
            );
          return dwolla
            .post('customers', {
              firstName,
              lastName,
              email,
            })
            .then(
              (res) => {
                const location = res.headers.get('location');
                const aLocation = location.split('/');
                const dwollaId = aLocation[aLocation.length - 1];
                const saveObj = user;
                saveObj.dwollaId = dwollaId;
                return saveObj.save(user);
              },
              (error) =>
                new ApolloError('Error creating Dwolla user.', 'DWOLLA', {
                  error,
                })
            );
        });
      });
    },
    sendPortfolioFollowRequest: async (parent, { input }, { models }) => {
      // TODO: protect this endpoint
      const { portfolioId, email, firstName, lastName } = input;
      const portfolio = await models.portfolio.findByPk(portfolioId, {
        attributes: ['hubSpotWaitListId'],
      });
      if (!portfolio || !portfolio.hubSpotWaitListId) {
        console.error('Portfolio wait list error', portfolio);
        return new ApolloError(
          'Error subscribing you to this wait list, please try again later.'
        );
      }
      return HubSpotService.addContactToList({
        hubSpotListId: portfolio.hubSpotWaitListId,
        email,
        firstName,
        lastName,
      }).then(
        () => 'success',
        (error) => {
          const eMsg = 'Error signing up for mailing list';
          console.error(eMsg, error);
          return new ApolloError(eMsg, 'APPLICATION', {
            email: input.email,
            error,
          });
        }
      );
    },
    joinGlobalArtExchangeEmailList: async (parent, { input }) => {
      // TODO: protect this endpoint with ipquality check
      const { email, firstName, lastName } = input;
      const hubSpotListId = 'global_art_exchange_follower';
      return HubSpotService.addContactToList({
        hubSpotListId,
        email,
        firstName,
        lastName,
      }).then(
        () => 'success',
        (error) => {
          console.error('Error signing up for mailing list', error);
          return new ApolloError(
            'Error signing up for mailing list',
            'APPLICATION',
            {
              email: input.email,
              error,
            }
          );
        }
      );
    },
    joinIRAWaitlist: async (parent, { input }) => {
      // TODO: protect this endpoint with ipquality check
      const { email, firstName, lastName } = input;
      const hubSpotListId = 'ira_wait_list_follower';
      return HubSpotService.addContactToList({
        hubSpotListId,
        email,
        firstName,
        lastName,
      }).then(
        () => 'success',
        (error) =>
          new ApolloError('Error signing up for mailing list', 'APPLICATION', {
            email: input.email,
            error,
          })
      );
    },
    joinWealthAdvisorsEmailList: async (parent, { input }) => {
      // TODO: protect this endpoint with ipquality check
      const { email, firstName, lastName } = input;
      const hubSpotListId = 'wealth_advisors_follower'; // You'll provide the actual list ID later
      return HubSpotService.addContactToList({
        hubSpotListId,
        email,
        firstName,
        lastName,
      }).then(
        () => 'success',
        (error) => {
          console.error(
            'Error signing up for wealth advisors mailing list',
            error
          );
          return new ApolloError(
            'Error signing up for mailing list',
            'APPLICATION',
            {
              email: input.email,
              error,
            }
          );
        }
      );
    },
    joinMobileBetaUserList: async (parent, { input }) => {
      // TODO: protect this endpoint with ipquality check
      const { email, firstName, lastName } = input;
      const hubSpotListId = 'mobile_beta_user';
      return HubSpotService.addContactToList({
        hubSpotListId,
        email,
        firstName,
        lastName,
      }).then(
        () => 'success',
        (error) => {
          console.error('Error signing up for mobile beta user list', error);
          return new ApolloError(
            'Error signing up for mobile beta user list',
            'APPLICATION',
            {
              email: input.email,
              error,
            }
          );
        }
      );
    },
    sendForgotPasswordEmail: (parent, { email }, { req }) =>
      sendForgotPasswordEmail({
        email,
        req,
      }),
    sendAuth0ForgotPasswordEmail: (parent, { email }, { req }) => {
      // TODO: Finish Code
      // Step 1: Validate Email
      // Step 2: Look up user and retrieve auth0Id
      // Step 3: Start Auth0 forgot password flow
      // Step 4: Send Auth0 forgot password email with link depending on if it is mobile or not
      // Step 5: Return true
    },
    sendRegisterEmail: (parent, { id, email, client }, context) =>
      sendRegisterEmail(id, email, context, client),
    sendContactUsEmail: async (
      parent,
      { email, firstName, lastName, message, identity },
      { user, models }
    ) => {
      const isFormattedAsEmail = validator.isEmail(email || '');
      if (!isFormattedAsEmail) {
        return new ApolloError('Email provided is not formatted correctly');
      }
      if (identity && !user) {
        const [isEmailValid] = await Promise.all([
          ValidatorService.validateEmail({
            email,
            useCase: 'Contact form submission',
            scoreThreshold: 100,
          }),
        ]);
        if (!isEmailValid) {
          return new ApolloError(
            'Email provided determined to be invalid, or associated with suspicious activity and the message has been blocked.'
          );
        }

        // if (!isIPValid) {
        //   return new ApolloError(
        //     'Your ip address has been flagged as suspicious and your email has not been sent.'
        //   );
        // }
      }

      const userWMatchingEmail = await models.user.findOne({
        attributes: ['id', 'oktaId', 'dwollaId', 'hubSpotContactId'],
        where: { email },
      });
      let dwollaUrl;
      let dwollaId;
      let oktaUrl;
      let hubSpotUrl;
      let cmsUrl = `${process.env.CMS_HOST}/User`;
      const dwollaSupportUrl = process.env.DWOLLA_SUPPORT_EMAIL;
      if (userWMatchingEmail) {
        cmsUrl = `${process.env.CMS_HOST}/User/${userWMatchingEmail.id}`;
        if (userWMatchingEmail.dwollaId) {
          dwollaUrl = `https://dashboard.dwolla.com/customers/${userWMatchingEmail.dwollaId}`;
          dwollaId = userWMatchingEmail.dwollaId;
        }
        if (userWMatchingEmail.oktaId) {
          oktaUrl = `https://dev-421165-admin.okta.com/admin/user/profile/view/${userWMatchingEmail.oktaId}`;
        }
        if (userWMatchingEmail.hubSpotContactId) {
          hubSpotUrl = `https://app.hubspot.com/contacts/********/record/${userWMatchingEmail.hubSpotContactId}`;
        }
      } else {
        // TODO: Add user to HubSpot
      }

      return sendContactUsEmail({
        email,
        firstName,
        lastName,
        message,
        cmsUrl,
        dwollaUrl,
        dwollaSupportUrl,
        dwollaId,
        oktaUrl,
        hubSpotUrl,
      }).then(() => ({
        id: 1,
      }));
    },
    sendFeedbackEmail: async (
      parent,
      { email, firstName, lastName, location, message },
      { models, user }
    ) => {
      let dwollaUrl;
      let dwollaId;
      let oktaUrl;
      let hubSpotUrl;
      const dwollaSupportUrl = process.env.DWOLLA_SUPPORT_EMAIL;
      let cmsUrl = `${process.env.CMS_HOST}/User`;
      const lintedEmail = email || user?.email;
      const lintedFirstName = firstName || user?.firstName;
      const lintedLastName = lastName || user?.lastName;
      if (lintedEmail) {
        const userWMatchingEmail = await models.user.findOne({
          attributes: ['id', 'oktaId', 'dwollaId', 'hubSpotContactId'],
          where: { email: lintedEmail },
        });
        if (userWMatchingEmail) {
          cmsUrl = `${process.env.CMS_HOST}/User/${userWMatchingEmail.id}`;
          if (userWMatchingEmail.dwollaId) {
            dwollaUrl = `https://dashboard.dwolla.com/customers/${userWMatchingEmail.dwollaId}`;
            dwollaId = userWMatchingEmail.dwollaId;
          }
          if (userWMatchingEmail.oktaId) {
            oktaUrl = `https://dev-421165-admin.okta.com/admin/user/profile/view/${userWMatchingEmail.oktaId}`;
          }
          if (userWMatchingEmail.hubSpotContactId) {
            hubSpotUrl = `https://app.hubspot.com/contacts/********/record/${userWMatchingEmail.hubSpotContactId}`;
          }
        }
      }
      return sendFeedbackEmail({
        email: lintedEmail,
        firstName: lintedFirstName,
        lastName: lintedLastName,
        location,
        message,
        cmsUrl,
        dwollaUrl,
        dwollaSupportUrl,
        dwollaId,
        oktaUrl,
        hubSpotUrl,
      });
    },
    sendBrazilContactUsEmail: (
      parent,
      { email, firstName, lastName, message },
      { req }
    ) =>
      sendBrazilContactUsEmail({
        email,
        firstName,
        lastName,
        message,
        req,
      }),
    disputeTransfers: async (
      parent,
      { description, transferIds },
      { user }
    ) => {
      // TODO: add authorization check
      const cancelStatus = {
        success: [],
        fail: [],
      };
      const cancelTransferPromises = transferIds.map((transferId) =>
        DwollaService.cancelDwollaTransfer(transferId).then(
          () => {
            cancelStatus.success.push(transferId);
          },
          (err) => {
            console.error('Error cancelling Dwolla transfer', err);
            cancelStatus.fail.push(transferId);
          }
        )
      );
      await Promise.all(cancelTransferPromises);

      SlackService.logToSlack({
        type: 'platform-user-activity',
        title: 'Investor Cancelled/Disputed Transfer(s)',
        url: `${process.env.CMS_HOST}/User/${user.id}`,
        data: [
          {
            label: 'User',
            value: `${user.firstName} ${user.lastName}`,
          },
          { label: 'Reason', value: description },
          {
            label: 'Cancelled Dwolla Transfer ID(s)',
            value: cancelStatus.success.join(',\n'),
          },
          {
            label: 'Disputed Dwolla Transfer ID(s)',
            value: cancelStatus.fail.join(',\n'),
          },
        ],
      });

      const returnObj = {
        allCancelled: cancelStatus.success.length === transferIds.length,
        someCancelled:
          cancelStatus.success.length > 0 && cancelStatus.fail.length > 0,
        noneCancelled: cancelStatus.fail.length === transferIds.length,
      };

      if (returnObj.allCancelled) {
        // Send confirmation email to investor. No need to notify energea
        sendCustomerTransfersSelfCancelledEmail({
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
        });
      } else {
        // In the case that only some were cancelled, we will be notified of all of them.
        // This is because Joe thinks it will be confusing to the investor to receive an email that
        // some were cancelled but others were not yet. I'd rather just tell them we have been
        // notified and we can handle it.
        sendTransferDisputeEmail({
          user,
          description,
          transferIds,
        });
      }
      return returnObj;
    },
    confirmEmail: (parent, { email, id, token }, { req }) =>
      confirmEmail({
        email,
        token,
        id,
        req,
      }), // return the promise so Graphql knows to wait
    updateUser,
    mia_updateUser: updateUser,
    // TODO: migrate updateUserEmail
    updateUserEmail: async (
      parent,
      { id, email: newEmail, sendConfirmationEmails, client },
      context
    ) => {
      const {
        // models,
        roles,
        user,
      } = context;
      const isAuthorized = getAuthorized('userEdit', roles);
      if (!isAuthorized && (!user || id !== user.id)) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      // Make sure no other user has this email
      const users = await User.findAll({
        attributes: ['id'],
        where: {
          email: newEmail,
        },
      });
      if (users.length > 0) {
        throw new ApolloError(
          'A user with this email already exists.',
          'VALIDATION'
        );
      }

      const dbUser = await User.findByPk(id, {
        attributes: [
          'id',
          'oktaId',
          'authId',
          'email',
          'emailUpdateRequest',
          'hubSpotContactId',
        ],
      });

      const saveObj = dbUser;
      if (!sendConfirmationEmails) {
        // Update the users email immediately without any confirmation emails
        saveObj.email = newEmail;
        saveObj.emailUpdateRequest = null;
        saveObj.confirmationDt = new Date();
        saveObj.invalidEmailFlg = false;
        return saveObj.save(user).then((savedUser) => {
          // Update hubspot email and confirmationDt
          if (dbUser.hubSpotContactId) {
            HubSpotService.updateContact(dbUser.hubSpotContactId, {
              confirmationDt: savedUser.confirmationDt,
              email: savedUser.email,
            });
          }
          // Update okta email and login
          // Update auth0 email
          updateAuth0User(dbUser.authId, { email: savedUser.email });
          // oktaClient.getUser(dbUser.oktaId).then((oktaUser) => {
          //   oktaUser.profile.login =
          //     savedUser.email; /* eslint-disable-line no-param-reassign */
          //   oktaUser.profile.email =
          //     savedUser.email; /* eslint-disable-line no-param-reassign */
          //   oktaUser.update().then(
          //     () => console.log('Okta User successfully saved'.bold.green),
          //     (e) => console.error('Error saving okta user'.bold.red, e)
          //   );
          // });
        });
      }

      sendEmailAddressChangeEmail(id, dbUser.email);
      sendRegisterEmail(id, newEmail, context, client);
      saveObj.emailUpdateRequest = newEmail;
      saveObj.invalidEmailFlg = false;
      return saveObj.save(user);
    },
    createDeleteAccountRequest: async (parent, { id }, context) => {
      const { roles, user } = context;
      const isAuthorized = getAuthorized('userEdit', roles);
      if (!isAuthorized && (!user || id !== user.id)) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }

      const dbUser = await User.findByPk(id, {
        attributes: [
          'id',
          'email',
          'firstName',
          'lastName',
          'deleteAccountRequestDt',
        ],
      });

      if (!dbUser) {
        return new ApolloError(`No database user found`, 'VALIDATION', {
          userId: id,
        });
      }

      const saveObj = dbUser;
      saveObj.deleteAccountRequestDt = new Date();
      sendIssueEmail({
        description: `User (id: ${dbUser.id}, email: ${dbUser.email}) has requested their account be deleted. Please review and take action.`,
        oData: {
          dbUser,
        },
      });
      return saveObj.save(user).catch((e) => {
        throw new ApolloError(
          'Error updating User to request account deletion.',
          'APPLICATION',
          {
            e,
          }
        );
      });
    },
    deleteUserEmailUpdateRequest: async (parent, { id }, context) => {
      const {
        // models,
        roles,
        user,
      } = context;
      const isAuthorized = getAuthorized('userEdit', roles);
      if (!isAuthorized && (!user || id !== user.id)) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }

      const dbUser = await User.findByPk(id, {
        attributes: ['id', 'emailUpdateRequest'],
      });

      if (!dbUser) {
        return new ApolloError(`No database user found`, 'VALIDATION', {
          userId: id,
        });
      }

      const saveObj = dbUser;
      saveObj.emailUpdateRequest = null;
      return saveObj.save(user).catch((e) => {
        throw new ApolloError(
          'Error updating User to cancel request to update email.',
          'APPLICATION',
          {
            e,
          }
        );
      });
    },
    uploadDwollaAuthDoc: DwollaService.uploadDwollaAuthDoc,
    resetMobileDevelopmentTestUserForIRAOnboarding: async (
      parents,
      args,
      { models }
    ) => {
      if (process.env.NODE_ENV === 'production') {
        console.log(
          'Blocking use of resetMobileDevelopmentTestUserForIRAOnboarding in production'
        );
        return false;
      }
      const testUser = await models.user.findOne({
        where: {
          id: 133,
        },
        include: [
          {
            attributes: ['id'],
            model: models.subAccount,
            required: false,
            include: [
              {
                attributes: ['id'],
                model: models.investment,
              },
              {
                attributes: ['id'],
                model: models.dividend,
              },
              {
                attributes: ['id'],
                model: models.sellOrder,
                include: [{ attributes: ['id'], model: models.shareTransfer }],
              },
              {
                attributes: ['id'],
                model: models.autoReinvestIndicator,
              },
              {
                attributes: ['id'],
                model: models.millenniumTrustFundingSession,
              },
              {
                attributes: ['id'],
                model: models.buyDirection,
              },
            ],
          },
        ],
      });
      if (!testUser) {
        console.log(
          'User not found in resetMobileDevelopmentTestUserForIRAOnboarding'
        );
        return false;
      }
      const aPromises = [];
      testUser.subAccounts.forEach((subAccount) => {
        const aSubAcctPromises = [];
        subAccount.investments.forEach((investment) => {
          aSubAcctPromises.push(
            models.transfer
              .destroy({
                where: {
                  investmentId: investment.id,
                },
              })
              .then(() => investment.destroy())
          );
        });
        subAccount.dividends.forEach((dividend) => {
          aSubAcctPromises.push(
            models.transfer
              .destroy({
                where: {
                  dividendId: dividend.id,
                },
              })
              .then(() => dividend.destroy())
          );
        });
        subAccount.sellOrders.forEach((sellOrder) => {
          const aSellOrderPromises = [];
          sellOrder.shareTransfers.forEach((shareTransfer) => {
            aSellOrderPromises.push(
              models.transfer
                .destroy({
                  where: {
                    shareTransferId: shareTransfer.id,
                  },
                })
                .then(() => shareTransfer.destroy())
            );
          });
          aSubAcctPromises.push(
            Promise.all(aSellOrderPromises).then(() =>
              models.eventNotification
                .destroy({
                  where: {
                    sellOrderId: sellOrder.id,
                  },
                })
                .then(() => sellOrder.destroy())
            )
          );
        });
        subAccount.millenniumTrustFundingSessions.forEach(
          (millenniumTrustFundingSession) => {
            aSubAcctPromises.push(millenniumTrustFundingSession.destroy());
          }
        );
        subAccount.autoReinvestIndicators.forEach((autoReinvestIndicator) => {
          aSubAcctPromises.push(autoReinvestIndicator.destroy());
        });
        subAccount.buyDirections.forEach((buyDirection) => {
          aSubAcctPromises.push(
            models.transfer
              .destroy({
                where: {
                  buyDirectionId: buyDirection.id,
                },
              })
              .then(() => buyDirection.destroy())
          );
        });
        aPromises.push(
          Promise.all(aSubAcctPromises).then(() =>
            models.quarterlyUserFinancialStatement
              .destroy({
                where: {
                  subAccountId: subAccount.id,
                },
              })
              .then(() => subAccount.destroy())
          )
        );
      });
      await Promise.all(aPromises);
      await models.millenniumTrustAuthSession.destroy({
        where: {
          userId: testUser.id,
        },
      });
      testUser.millenniumTrustContactId = null;
      testUser.ssn = String(Math.floor(********* + Math.random() * *********));
      await testUser.save();
      return true;
    },
    importOktaUsers: async (parent, args, { roles, user }) => {
      const isAuthorized = getAuthorized('IT', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
        });
      }
      return importOktaUsers();
    },
    runAuth0Migration: async (parent, { input = {} }, { user, roles }) => {
      // Check for authorized user (IT role required)
      const isAuthorized = getAuthorized('IT', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
          roles,
        });
      }

      try {
        const { dryRun = false, batchSize = 500 } = input;

        console.log(
          `Auth0 migration triggered by user ${user?.id} (${user?.email})`
        );
        console.log(`Parameters: dryRun=${dryRun}, batchSize=${batchSize}`);

        const results = await runAuth0Migration({ dryRun, batchSize });

        return {
          found: results.found,
          created: results.created,
          mapped: results.mapped,
          emailed: results.emailed,
          errors: results.errors.map((error) => ({
            email: error.email || null,
            error: error.error,
            details: error.details || null,
            statusCode: error.statusCode || null,
          })),
          duration: Math.round(results.duration / 1000), // Convert to seconds
          success: results.errors.length === 0,
        };
      } catch (error) {
        console.error('Auth0 migration failed:', error);
        return new ApolloError('Migration failed', 'APPLICATION', {
          error: error.message,
        });
      }
    },
  },
};
