import { ApolloError, ForbiddenError } from 'apollo-server-express';
import moment from 'moment';
import { Op } from 'sequelize';
import { Cloudinary } from 'cloudinary-core';

import { getAuthorized } from '../../../utils/authMatrix';
// eslint-disable-next-line import/named
import { sendEventEmail } from '../../services/mail';
import { getUsersByCommunicationGroupId } from '../../services/communicationGroup';

import { constants } from '../../../utils/global';

const cl = new Cloudinary({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  secure: true,
});

export default {
  Query: {
    getEvent: (parent, { id }, { models }) => models.event.findByPk(id),
    eventFeed: (parent, { sort, pagination, filter }, { models }) => {
      const whereClause = {};
      if (filter.project) {
        whereClause.projectId = filter.project.id;
      }
      if (filter.portfolio) {
        whereClause.portfolioId = filter.portfolio.id;
      }
      if (filter.eventType) {
        whereClause.eventTypeId = filter.eventType.id;
      }
      return models.event.findAndCountAll({
        where: whereClause,
        order: [[sort.field, sort.order]],
        offset: (pagination.page - 1) * pagination.perPage,
        limit: pagination.perPage,
      });
    },
    allEvents: (parent, args, { models }) =>
      models.event.findAll({
        order: [['orderNo', 'ASC']],
      }),
  },
  Event: {
    eventAuthor: (event) => event.getEventAuthor(),
    eventPageLink: (event) => `https://www.energea.com/updates/${event.id}`,
    isComplete: (event) => !!event.completedDt,
    // isCompleteForPage: async (event) => {
    //   const { completedDt, description, eventDt, title, summary } = event;
    //   const primaryImage = await event.getEventImages({
    //     where: {
    //       primaryFlg: true,
    //     },
    //   });
    //   return !!(
    //     primaryImage &&
    //     primaryImage[0] &&
    //     completedDt &&
    //     description &&
    //     eventDt &&
    //     title &&
    //     summary
    //   );
    // },
    socialMediaPosts: (event) => event.getSocialMediaPosts(),
    eventType: (event) => event.getEventType(),
    portfolio: (event, args, { models }) => {
      if (event.portfolioId) {
        return event.getPortfolio();
      }
      if (event.projectId) {
        return event
          .getProject({
            include: [
              {
                model: models.portfolio,
              },
            ],
          })
          .then((project) => project.portfolio);
      }
      return null;
    },
    remainingNotificationUsers: (event) =>
      getUsersByCommunicationGroupId({
        event,
        emailOrNotification: 'notification',
      }),
    remainingEmailUsers: (event) =>
      getUsersByCommunicationGroupId({
        event,
        emailOrNotification: 'email',
      }),
    project: (event) => event.getProject(),
    requiresApproval: (event) => {
      const {
        isComplete,
        includeInPortfolioTimelineFlg,
        includeInInvestorDashboardFlg,
        includeInGlobalFeedFlg,
        createNotificationsFlg,
        createEmailsFlg,
      } = event;
      if (
        !isComplete &&
        (includeInPortfolioTimelineFlg ||
          includeInInvestorDashboardFlg ||
          includeInGlobalFeedFlg ||
          createNotificationsFlg ||
          createEmailsFlg)
      ) {
        return true;
      }
      return false;
    },
    communicationGroup: (event) => event.getCommunicationGroup(),
    sendgridEmailTemplate: (event) => event.getSendgridEmailTemplate(),
    eventImages: (event) => event.getEventImages(),
    bannerImage: async (event) => {
      const primaryImage = await event.getEventImages({
        where: {
          primaryFlg: true,
        },
      });
      return (primaryImage && primaryImage[0]) || null;
    },
    bannerImageCardUrl: async (event) => {
      const primaryImage = await event.getEventImages({
        where: {
          primaryFlg: true,
        },
      });
      return (
        (primaryImage?.[0]?.dataValues?.public_id &&
          cl.url(primaryImage[0].dataValues.public_id, {
            width: constants.imgDims.mobileCard.width,
            height: constants.imgDims.mobileCard.height,
            quality: 'auto',
            crop: 'fill',
            format: 'WebP',
          })) ||
        null
      );
    },
    bannerVideo: async (event) => {
      const primaryVideo = await event.getEventVideos({
        where: {
          primaryFlg: true,
        },
      });
      return (primaryVideo && primaryVideo[0]) || null;
    },
    eventVideos: (event) => event.getEventVideos(),
    eventNotifications: (event) => event.getEventNotifications(),
    readEventNotificationsCount: (event, args, { models }) =>
      models.eventNotification.count({
        where: [
          {
            readDt: {
              [Op.not]: null,
            },
          },
          {
            eventId: event.id,
          },
        ],
      }),
    eventEmails: (event) => event.getEventEmails(),
  },
  Mutation: {
    createEvent: (parent, args, { models, user, roles }) => {
      // Check for authorized user
      const isAuthorized = getAuthorized('allUsers', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
          roles,
        });
      }
      return models.event.create(args.input, user);
    },
    updateEvent: (parent, args, { models, user, roles }) => {
      const { input } = args;
      if (!input.id) {
        throw new ApolloError('You must provide an id', 'VALIDATION');
      }
      // Check for authorized user
      const isAuthorized = getAuthorized('allUsers', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
          roles,
        });
      }
      return models.event
        .findByPk(input.id, {
          include: [
            {
              model: models.eventImage,
            },
            {
              model: models.eventAuthor,
            },
          ],
        })
        .then(
          async (event) => {
            const saveObj = event;
            let emailsSent = null;
            let notificationsCreated = null;
            // handle notifications
            Object.keys(input).forEach((key) => {
              if (['id'].indexOf(key) < 0) {
                const value = input[String(key)];
                saveObj[String(key)] =
                  value instanceof Date
                    ? moment.utc(value).format('MM-DD-YYYY')
                    : value;
              }
            });
            if (input.createTestEmail) {
              if (!input.testEmail) {
                return new ApolloError('Set a test email to send to.');
              }
              if (!saveObj.createEmailsFlg) {
                return new ApolloError('Created Emails Flag is set to false.');
              }
              if (!saveObj.eventTypeId) {
                return new ApolloError(
                  'Event type must be selected before creating notifications.'
                );
              }
              if (!saveObj.communicationGroupId) {
                return new ApolloError(
                  'Communication Group must be selected before creating notifications.'
                );
              }
              if (!saveObj.sendgridEmailTemplateId) {
                return new ApolloError(
                  'Email template must be set before sending emails.'
                );
              }
              const oUser = user;

              const msg = {
                to: {
                  email: input.testEmail,
                  fullName: 'Test Email',
                },
              };
              const tempEmailObj = {
                msg,
                sendgridEmailTemplateId: saveObj.sendgridEmailTemplateId,
                eventEmail: {
                  userId: oUser.id,
                  eventId: event.id,
                },
                dbUser: oUser,
                dbEvent: saveObj,
                isTest: true,
              };
              try {
                await sendEventEmail(tempEmailObj);
              } catch (error) {
                console.error('Error sending event email'.bold.red, error);
                return error;
              }
            }
            if (input.createNotifications) {
              if (!saveObj.createNotificationsFlg) {
                return new ApolloError(
                  'Create Notifications Flag is set to false.'
                );
              }
              if (!saveObj.completedDt) {
                return new ApolloError(
                  'Event must be marked as complete before creating notifications.'
                );
              }
              if (!saveObj.eventTypeId) {
                return new ApolloError(
                  'Event type must be selected before creating notifications.'
                );
              }
              if (!saveObj.communicationGroupId) {
                return new ApolloError(
                  'Communication Group must be selected before creating notifications.'
                );
              }
              const oUsers = await getUsersByCommunicationGroupId({
                event: saveObj,
                emailOrNotification: 'notification',
              });
              const userIds = oUsers
                .map((oUser) => {
                  // NOTE: Check for pre-existing notifications for the specific event
                  if (oUser.eventNotifications.length > 0) {
                    console.warn(
                      'Skipping duplicate notification creation for user',
                      oUser.id
                    );
                    return null;
                  }
                  return oUser.id;
                })
                .filter((id) => id !== null);
              const newNotifications =
                await models.eventNotification.bulkCreate(
                  userIds.map((userId) => ({
                    userId,
                    eventId: saveObj.id,
                  })),
                  {
                    returning: true,
                    oktaId: user?.oktaId,
                  }
                );
              notificationsCreated = newNotifications.length;
            }
            // handle emails
            if (input.createEmails) {
              if (!saveObj.createEmailsFlg) {
                return new ApolloError('Created Emails Flag is set to false.');
              }
              if (!saveObj.completedDt) {
                return new ApolloError(
                  'Event must be marked as complete before creating emails.'
                );
              }
              if (!saveObj.eventTypeId) {
                return new ApolloError(
                  'Event type must be selected before creating emails.'
                );
              }
              if (!saveObj.communicationGroupId) {
                return new ApolloError(
                  'Communication Group must be selected before creating emails.'
                );
              }
              if (!saveObj.sendgridEmailTemplateId) {
                return new ApolloError(
                  'Email template must be set before sending emails.'
                );
              }

              const users = await getUsersByCommunicationGroupId({
                event: saveObj,
                emailOrNotification: 'email',
              });
              users.forEach(async (oUser, index) => {
                // TODO: implement the event check (similar to the notification check above) to prevent emails going out to previously sent users
                if (
                  oUser.email &&
                  (process.env.NODE_ENV === 'production' || index < 10) //only send the first 10 emails in dev
                ) {
                  const msg = {
                    to: {
                      email: oUser.email,
                      fullName: `${oUser.firstName} ${oUser.lastName}`,
                    },
                  };
                  const tempEmailObj = {
                    msg,
                    sendgridEmailTemplateId: saveObj.sendgridEmailTemplateId,
                    eventEmail: {
                      userId: oUser.id,
                      eventId: event.id,
                    },
                    dbUser: oUser,
                    dbEvent: saveObj,
                  };
                  sendEventEmail(tempEmailObj);
                }
              });
              emailsSent = users.length;
            }

            if (input.addEventImages) {
              if (
                !input.primaryEventImageId &&
                input.addEventImages[0] &&
                event.eventImages?.filter((el) => el.primaryFlg).length === 0
              ) {
                input.addEventImages[0].primaryFlg = true;
              }
              input.addEventImages.forEach(async (eventImage) => {
                const eventImageSaveObj = eventImage;
                eventImageSaveObj.eventId = input.id;
                await models.eventImage.create(eventImageSaveObj, user);
              });
            }
            if (input.primaryEventImageId || input.primaryEventImageId === 0) {
              await models.eventImage.update(
                {
                  primaryFlg: false,
                },
                {
                  where: {
                    [Op.and]: [
                      {
                        eventId: input.id,
                      },
                      {
                        primaryFlg: true,
                      },
                    ],
                  },
                  oktaId: user.oktaId, // This is passing okta id to the afterUpdate sequelize hook
                }
              );
              await models.eventImage.update(
                {
                  primaryFlg: true,
                },
                {
                  where: {
                    id: input.primaryEventImageId,
                  },
                  oktaId: user.oktaId, // This is passing okta id to the afterUpdate sequelize hook
                }
              );
            }
            if (input.deleteEventImage || input.deleteEventImage === 0) {
              await models.eventImage.destroy({
                where: {
                  id: input.deleteEventImage,
                },
                oktaId: user?.oktaId,
              });
            }

            if (input.addEventVideos) {
              input.addEventVideos.forEach(async (eventVideo) => {
                const eventVideoSaveObj = eventVideo;
                eventVideoSaveObj.eventId = input.id;
                await models.eventVideo.create(eventVideoSaveObj, user);
              });
            }
            if (input.primaryEventVideoId || input.primaryEventVideoId === 0) {
              await models.eventVideo.update(
                {
                  primaryFlg: false,
                },
                {
                  where: {
                    [Op.and]: [
                      {
                        eventId: input.id,
                      },
                      {
                        primaryFlg: true,
                      },
                    ],
                  },
                  oktaId: user.oktaId, // This is passing okta id to the afterUpdate sequelize hook
                }
              );
              await models.eventVideo.update(
                {
                  primaryFlg: true,
                },
                {
                  where: {
                    id: input.primaryEventVideoId,
                  },
                  oktaId: user.oktaId, // This is passing okta id to the afterUpdate sequelize hook
                }
              );
            }
            if (input.deleteEventVideo || input.deleteEventVideo === 0) {
              await models.eventVideo.destroy({
                where: {
                  id: input.deleteEventVideo,
                },
                oktaId: user?.oktaId,
              });
            }
            return saveObj.save(user).then((savedEvent) => ({
              event: savedEvent,
              emailsSent,
              notificationsCreated,
            }));
          },
          (error) =>
            new ApolloError('Error updating this event', 'APPLICATION', {
              error,
            })
        );
    },
    deleteEvent: (parent, { id }, { models, user, roles }) => {
      // Check for authorized user
      const isAuthorized = getAuthorized('allUsers', roles);
      if (!isAuthorized) {
        return new ForbiddenError('You do not have access to this endpoint', {
          user,
          roles,
        });
      }
      return models.event
        .destroy({
          where: {
            id,
          },
        })
        .then(() => ({
          id,
        }));
    },
  },
};
