import schedule from 'node-schedule';
import moment from 'moment';
import CronService from './services/cron';
import { constants } from '../utils/global';

// Schedule Cron Jobs

const setMonthlyLtvCacChartDataCacheRule = new schedule.RecurrenceRule();
setMonthlyLtvCacChartDataCacheRule.minute = 0;
setMonthlyLtvCacChartDataCacheRule.tz = 'America/New_York';
schedule.scheduleJob(setMonthlyLtvCacChartDataCacheRule, () => {
  CronService.setMonthlyLtvCacChartDataCache();
}).nameOverride = 'Set Monthly LTV/CAC Chart Data Cache';

const setLtvToCacDataCacheRule = new schedule.RecurrenceRule();
setLtvToCacDataCacheRule.minute = 0;
setLtvToCacDataCacheRule.tz = 'America/New_York';
schedule.scheduleJob(setLtvToCacDataCacheRule, () => {
  CronService.setLtvToCacDataCache();
}).nameOverride = 'Set LTV/CAC Chart Data Cache';

const recurringInvestmentsRule = new schedule.RecurrenceRule();
recurringInvestmentsRule.hour = constants.autoInvestHour; // scheduled to run each day at 9am
recurringInvestmentsRule.minute = 0;
recurringInvestmentsRule.tz = 'America/New_York';
schedule.scheduleJob(recurringInvestmentsRule, () => {
  CronService.checkForRecurringInvestmentSubscriptions();
}).nameOverride = 'Check For Recurring Investment Subscriptions';

const checkBrConsumerUnitPreviousContractTerminationDates =
  new schedule.RecurrenceRule();
checkBrConsumerUnitPreviousContractTerminationDates.hour = 9; // scheduled to run each day at 6am
checkBrConsumerUnitPreviousContractTerminationDates.minute = 0;
checkBrConsumerUnitPreviousContractTerminationDates.tz = 'America/Sao_Paulo';
schedule.scheduleJob(
  checkBrConsumerUnitPreviousContractTerminationDates,
  () => {
    CronService.checkBrConsumerUnitPreviousContractTerminationDates();
  }
).nameOverride = 'Check BrConsumerUnit Previous Contract Termination Dates';

const updateInvestorReturns = new schedule.RecurrenceRule();
updateInvestorReturns.hour = 6; // scheduled to run each day at 6am
updateInvestorReturns.minute = 0;
updateInvestorReturns.tz = 'America/New_York';
schedule.scheduleJob(updateInvestorReturns, () => {
  CronService.updateInvestorReturns();
}).nameOverride = 'Update Investor Returns Table';

const updatingSharePriceRule = new schedule.RecurrenceRule();
updatingSharePriceRule.hour = [20]; // scheduled to run each day at 8pm(0utc) and 8am
updatingSharePriceRule.minute = [0];
updatingSharePriceRule.tz = 'America/New_York';
schedule.scheduleJob(updatingSharePriceRule, () => {
  CronService.recalculateSharePrices();
}).nameOverride = 'Recalculate Share Prices';

const updatingSharePriceRedundancyCheckRule = new schedule.RecurrenceRule();
updatingSharePriceRedundancyCheckRule.hour = 23;
updatingSharePriceRedundancyCheckRule.minute = 0;
updatingSharePriceRedundancyCheckRule.tz = 'America/New_York';
schedule.scheduleJob(updatingSharePriceRedundancyCheckRule, () => {
  CronService.checkSharePricesRecalculated();
}).nameOverride = 'Check Share Prices Recalculated';

const syncTransferCompletedDtRule = new schedule.RecurrenceRule();
syncTransferCompletedDtRule.minute = [0, 15, 30, 45]; // scheduled for every 15 minutes
const previousMinutesToSync = 20;
schedule.scheduleJob(syncTransferCompletedDtRule, () => {
  CronService.syncTransferCompletedDt(previousMinutesToSync);
}).nameOverride = 'Transfers Table completedDt Syncing';

const updateMonthlyInvestorRecapKPIsInHubSpotRule =
  new schedule.RecurrenceRule();
updateMonthlyInvestorRecapKPIsInHubSpotRule.date = 1;
updateMonthlyInvestorRecapKPIsInHubSpotRule.hour = 22;
updateMonthlyInvestorRecapKPIsInHubSpotRule.minute = 0;
updateMonthlyInvestorRecapKPIsInHubSpotRule.tz = 'America/New_York';
schedule.scheduleJob(updateMonthlyInvestorRecapKPIsInHubSpotRule, () => {
  CronService.updateUserMonthlyInvestorRecapKPIsInHubSpot();
}).nameOverride = 'Update HubSpot Contact Monthly Investor Recap KPIs';

const updateUserPortfolioInvestedTotalHubSpotRule =
  new schedule.RecurrenceRule();
updateUserPortfolioInvestedTotalHubSpotRule.hour = [4, 12, 21];
updateUserPortfolioInvestedTotalHubSpotRule.minute = 0;
updateUserPortfolioInvestedTotalHubSpotRule.tz = 'America/New_York';
schedule.scheduleJob(updateUserPortfolioInvestedTotalHubSpotRule, () => {
  CronService.updateUserInvestmentKPIsInHubSpot();
}).nameOverride =
  'Update HubSpot Contact Investment KPIs (total invested, total invested per portfolio, first investment dt)';

const syncHubSpotContactsRule = new schedule.RecurrenceRule();
syncHubSpotContactsRule.hour = [5];
syncHubSpotContactsRule.minute = 0;
syncHubSpotContactsRule.tz = 'America/New_York';
schedule.scheduleJob(syncHubSpotContactsRule, () => {
  CronService.syncHubSpotContacts();
}).nameOverride = 'Sync HubSpot Contact properties with Energea Users';

const syncHubSpotBrContactsRule = new schedule.RecurrenceRule();
syncHubSpotBrContactsRule.hour = [6];
syncHubSpotBrContactsRule.minute = 0;
syncHubSpotBrContactsRule.tz = 'America/Sao_Paulo';
schedule.scheduleJob(syncHubSpotBrContactsRule, () => {
  CronService.syncHubSpotBrContacts();
}).nameOverride = 'Sync HubSpot Contact properties with Energea BrContacts';

const sendBrInvoiceDueReminderEmailRule = new schedule.RecurrenceRule();
sendBrInvoiceDueReminderEmailRule.hour = 10;
sendBrInvoiceDueReminderEmailRule.minute = 0;
sendBrInvoiceDueReminderEmailRule.tz = 'America/Sao_Paulo';
schedule.scheduleJob(sendBrInvoiceDueReminderEmailRule, () => {
  CronService.sendBrInvoiceDueReminderEmails();
}).nameOverride = 'Send BrInvoice Due Reminder Emails';

const syncHubSpotSubAccountsRule = new schedule.RecurrenceRule();
syncHubSpotSubAccountsRule.hour = [8, 18];
syncHubSpotSubAccountsRule.minute = 30;
syncHubSpotSubAccountsRule.tz = 'America/New_York';
schedule.scheduleJob(syncHubSpotSubAccountsRule, () => {
  CronService.syncHubSpotSubAccounts();
}).nameOverride = 'Sync HubSpot SubAccount properties with Energea SubAccounts';

const syncUserLeadSourceFromHubSpotRule = new schedule.RecurrenceRule();
syncUserLeadSourceFromHubSpotRule.hour = 0;
syncUserLeadSourceFromHubSpotRule.minute = 0;
syncUserLeadSourceFromHubSpotRule.tz = 'America/New_York';
schedule.scheduleJob(syncUserLeadSourceFromHubSpotRule, () => {
  CronService.pullUserLeadSourceFromHubSpot();
}).nameOverride = 'Pull User Lead Source from HubSpot';

const deactivateExpiredReferralsRule = new schedule.RecurrenceRule();
deactivateExpiredReferralsRule.hour = 0;
deactivateExpiredReferralsRule.minute = 0;
deactivateExpiredReferralsRule.tz = 'America/New_York';
schedule.scheduleJob(deactivateExpiredReferralsRule, () => {
  CronService.deactivateExpiredReferrals();
}).nameOverride = 'Deactivate Expired Referrals';

const emailMonthlyPortfolioReconciliationRule = new schedule.RecurrenceRule();
emailMonthlyPortfolioReconciliationRule.date = 1;
emailMonthlyPortfolioReconciliationRule.hour = 5;
emailMonthlyPortfolioReconciliationRule.minute = 0;
emailMonthlyPortfolioReconciliationRule.tz = 'America/New_York';
schedule.scheduleJob(emailMonthlyPortfolioReconciliationRule, () => {
  CronService.emailMonthlyPortfolioReconciliation();
}).nameOverride = 'Email Monthly Portfolio Reconciliation Excel Documents';

const sellOrderHealthCheckRule = new schedule.RecurrenceRule();
sellOrderHealthCheckRule.hour = 8;
sellOrderHealthCheckRule.minute = 15;
sellOrderHealthCheckRule.tz = 'America/New_York';
schedule.scheduleJob(sellOrderHealthCheckRule, () => {
  CronService.runSellOrderHealthCheck();
}).nameOverride = 'Run Sell Order Health Check';

const cashoutEarlyExitSellOrderRule = new schedule.RecurrenceRule();
cashoutEarlyExitSellOrderRule.hour = 7;
cashoutEarlyExitSellOrderRule.minute = 10;
cashoutEarlyExitSellOrderRule.tz = 'America/New_York';
schedule.scheduleJob(cashoutEarlyExitSellOrderRule, () => {
  CronService.cashoutEarlyExitSellOrders();
}).nameOverride = 'Cashout Early Exit Sell Order';

const recalculatePlatformIRR = new schedule.RecurrenceRule();
recalculatePlatformIRR.minute = [0, 15, 30, 45];
recalculatePlatformIRR.tz = 'America/New_York';
schedule.scheduleJob(recalculatePlatformIRR, () => {
  CronService.recalculatePlatformIRR();
}).nameOverride = 'Recalculating Platform IRR';

const createQuarterlyUserFinancialStatements = new schedule.RecurrenceRule();
createQuarterlyUserFinancialStatements.month = [0, 3, 6, 9];
createQuarterlyUserFinancialStatements.date = 1;
createQuarterlyUserFinancialStatements.hour = 13;
createQuarterlyUserFinancialStatements.minute = 0;
createQuarterlyUserFinancialStatements.tz = 'America/New_York';
schedule.scheduleJob(createQuarterlyUserFinancialStatements, () => {
  CronService.createQuarterlyUserFinancialStatements();
}).nameOverride = 'Create Quarterly User Financial Statements';

const scheduleOMPreventativeMaintenanceTasks = new schedule.RecurrenceRule();
scheduleOMPreventativeMaintenanceTasks.dayOfWeek = 5;
scheduleOMPreventativeMaintenanceTasks.hour = 18;
scheduleOMPreventativeMaintenanceTasks.minute = 0;
scheduleOMPreventativeMaintenanceTasks.tz = 'America/Sao_Paulo';
schedule.scheduleJob(scheduleOMPreventativeMaintenanceTasks, () => {
  CronService.scheduleOMPreventativeMaintenanceTasks();
}).nameOverride = 'Schedule preventative maintenance tasks';

// const checkInsurancePolicyExpirationsRule = new schedule.RecurrenceRule();
// checkInsurancePolicyExpirationsRule.minute = 30;
// checkInsurancePolicyExpirationsRule.hour = 7;
// checkInsurancePolicyExpirationsRule.tz = 'America/New_York';
// schedule.scheduleJob(checkInsurancePolicyExpirationsRule, () => {
//   CronService.checkInsurancePolicyExpirations();
// }).nameOverride = 'Check insurance policy expiration dates';

const prepMillenniumTrustMonthlyReconciliationReportsRule =
  new schedule.RecurrenceRule();
prepMillenniumTrustMonthlyReconciliationReportsRule.date = 1;
prepMillenniumTrustMonthlyReconciliationReportsRule.hour = 8;
prepMillenniumTrustMonthlyReconciliationReportsRule.minute = 0;
prepMillenniumTrustMonthlyReconciliationReportsRule.tz = 'America/New_York';
schedule.scheduleJob(
  prepMillenniumTrustMonthlyReconciliationReportsRule,
  () => {
    CronService.prepMillenniumTrustMonthlyReconciliationReports();
  }
).nameOverride = 'Sending IT MTC reconciliation reports';

if (process.env.NODE_ENV === 'production') {
  const checkMillenniumTrustCashBalanceRule = new schedule.RecurrenceRule();
  checkMillenniumTrustCashBalanceRule.minute = 0;
  checkMillenniumTrustCashBalanceRule.hour = 2;
  checkMillenniumTrustCashBalanceRule.dayOfWeek = [1, 2, 3, 4, 5];
  checkMillenniumTrustCashBalanceRule.tz = 'America/New_York';
  schedule.scheduleJob(checkMillenniumTrustCashBalanceRule, () => {
    CronService.checkMillenniumTrustCashBalances();
  }).nameOverride = 'Check Millennium Trust account cash balances';

  const checkMillenniumTrustTransferStatusesRule =
    new schedule.RecurrenceRule();
  checkMillenniumTrustTransferStatusesRule.minute = 30;
  checkMillenniumTrustTransferStatusesRule.hour = 12;
  checkMillenniumTrustTransferStatusesRule.tz = 'America/New_York';
  schedule.scheduleJob(checkMillenniumTrustTransferStatusesRule, () => {
    CronService.checkMillenniumTrustTransferStatuses();
  }).nameOverride = 'Check Millennium Trust transfer/contribution statuses';

  const checkMillenniumTrustInvestmentStatusesRule =
    new schedule.RecurrenceRule();
  checkMillenniumTrustInvestmentStatusesRule.minute = 30;
  checkMillenniumTrustInvestmentStatusesRule.hour = 13;
  checkMillenniumTrustInvestmentStatusesRule.tz = 'America/New_York';
  schedule.scheduleJob(checkMillenniumTrustInvestmentStatusesRule, () => {
    CronService.checkMillenniumTrustInvestmentStatuses();
  }).nameOverride = 'Check Millennium Trust investment statuses';

  const checkMillenniumTrustVerificationStatusesRule =
    new schedule.RecurrenceRule();
  checkMillenniumTrustVerificationStatusesRule.minute = 0;
  checkMillenniumTrustVerificationStatusesRule.hour = [
    8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20,
  ];
  checkMillenniumTrustVerificationStatusesRule.tz = 'America/New_York';
  schedule.scheduleJob(checkMillenniumTrustVerificationStatusesRule, () => {
    CronService.checkMillenniumTrustVerificationStatuses();
  }).nameOverride = 'Check Millennium Trust verification statuses';

  const checkMillenniumTrustAccountStatusesRule = new schedule.RecurrenceRule();
  checkMillenniumTrustAccountStatusesRule.minute = 30;
  checkMillenniumTrustAccountStatusesRule.hour = 11;
  checkMillenniumTrustAccountStatusesRule.tz = 'America/New_York';
  schedule.scheduleJob(checkMillenniumTrustAccountStatusesRule, () => {
    CronService.checkMillenniumTrustAccountStatuses();
  }).nameOverride = 'Check Millennium Trust account statuses';
}

// Monitoring cron jobs (different queues run at the same time, so stagger memory-intensive jobs)
if (process.env.NODE_ENV === 'production') {
  // Don't run in dev for sake of daily rate limits (SolarEdge especially)

  /* -------------------------------------------------------------------------- */
  /*                           SolarEdge Polling Jobs                           */
  /* -------------------------------------------------------------------------- */
  const solarEdgeSiteEnergyPollingRule = new schedule.RecurrenceRule();
  solarEdgeSiteEnergyPollingRule.minute = [10, 25, 40, 55]; // scheduled for every 15 minutes + 1 min each hour
  schedule.scheduleJob(solarEdgeSiteEnergyPollingRule, () => {
    CronService.pollLiveSolarEdgeSiteProductionData();
  }).nameOverride = 'SolarEdge Site Energy Polling';

  const solarEdgeSiteSensorDataPollingRule = new schedule.RecurrenceRule();
  solarEdgeSiteSensorDataPollingRule.minute = [15]; // scheduled for every 15 minutes + 2 min each hour (so we don't make these requests at the same time as the production poll)
  schedule.scheduleJob(solarEdgeSiteSensorDataPollingRule, () => {
    CronService.pollLiveSolarEdgeSiteSensorData();
  }).nameOverride = 'SolarEdge Site Sensor Data Polling';

  const solarEdgeInverterPowerPollingRule = new schedule.RecurrenceRule();
  solarEdgeInverterPowerPollingRule.minute = 0; // scheduled to run start of each hour
  schedule.scheduleJob(solarEdgeInverterPowerPollingRule, () => {
    CronService.pollHourlySolarEdgeInverterProductionData();
  }).nameOverride = 'SolarEdge Inverter Power Polling';

  const solarEdgeDailyInverterGenerationPollingRule =
    new schedule.RecurrenceRule();
  solarEdgeDailyInverterGenerationPollingRule.minute = 59; // scheduled to run end of each hour
  schedule.scheduleJob(solarEdgeDailyInverterGenerationPollingRule, () => {
    CronService.pollRecentSolarEdgeDailyInverterGenerationData();
  }).nameOverride = 'SolarEdge Daily Inverter Generation Polling';

  /* -------------------------------------------------------------------------- */
  /*                            SCADA Polling Jobs                              */
  /* -------------------------------------------------------------------------- */

  const scadaSystemAlarmPollingRule = new schedule.RecurrenceRule();
  scadaSystemAlarmPollingRule.minute = [0, 15, 30, 45];
  schedule.scheduleJob(scadaSystemAlarmPollingRule, () => {
    CronService.pollScadaSystemAlarms();
  }).nameOverride = 'SCADA System Alarm Polling';

  const scadaSystemAlarmStatusSyncRule = new schedule.RecurrenceRule();
  scadaSystemAlarmStatusSyncRule.minute = [10, 25, 40, 55];
  schedule.scheduleJob(scadaSystemAlarmStatusSyncRule, () => {
    CronService.syncScadaSystemAlarmStatuses();
  }).nameOverride = 'SCADA System Alarm Status Syncing';

  const scadaSystemInverterPowerPollingRule = new schedule.RecurrenceRule();
  scadaSystemInverterPowerPollingRule.minute = [0, 15, 30, 45];
  schedule.scheduleJob(scadaSystemInverterPowerPollingRule, () => {
    CronService.pollRecentScadaSystemInverterPowerData();
  }).nameOverride = 'SCADA System Inverter power Polling';

  const scadaSystemSensorDataPollingRule = new schedule.RecurrenceRule();
  scadaSystemSensorDataPollingRule.minute = [30];
  schedule.scheduleJob(scadaSystemSensorDataPollingRule, () => {
    CronService.pollRecentScadaSystemSensorData();
  }).nameOverride = 'SCADA System Sensor data Polling';

  const scadaSystemGenerationPollingRule = new schedule.RecurrenceRule();
  scadaSystemGenerationPollingRule.minute = [10, 25, 40, 55];
  schedule.scheduleJob(scadaSystemGenerationPollingRule, () => {
    CronService.pollRecentScadaSystemGenerationData();
  }).nameOverride = 'SCADA System generation Polling';

  const scadaSystemDailyInverterGenerationRule = new schedule.RecurrenceRule();
  // NOTE: Make sure this runs daily before monitoringPunchListReportEmailRule
  scadaSystemDailyInverterGenerationRule.hour = [1, 4];
  scadaSystemDailyInverterGenerationRule.minute = 0;
  scadaSystemDailyInverterGenerationRule.tz = 'America/Sao_Paulo';
  schedule.scheduleJob(scadaSystemDailyInverterGenerationRule, () => {
    CronService.saveLastDaysScadaInverterGeneration();
  }).nameOverride = 'SCADA System Daily Inverter Generation Fetch';

  /* -------------------------------------------------------------------------- */
  /*                              AMMP Polling Jobs                             */
  /* -------------------------------------------------------------------------- */

  const ammpEnergyPollingRule = new schedule.RecurrenceRule();
  ammpEnergyPollingRule.minute = 0; // NOTE: As of 6/22/2022, data is only posted once a day. No need to fetch too often
  schedule.scheduleJob(ammpEnergyPollingRule, () => {
    CronService.pollLiveSunExchangeSiteProductionData();
  }).nameOverride = 'AMMP Energy Polling';

  const ammpSensorDataPollingRule = new schedule.RecurrenceRule();
  ammpSensorDataPollingRule.minute = [29, 59];
  schedule.scheduleJob(ammpSensorDataPollingRule, () => {
    CronService.pollRecentAMMPSensorData();
  }).nameOverride = 'AMMP Sensor Data Polling';

  const ammpInverterPowerDataPollingRule = new schedule.RecurrenceRule();
  ammpInverterPowerDataPollingRule.minute = [28, 58];
  schedule.scheduleJob(ammpInverterPowerDataPollingRule, () => {
    CronService.pollRecentAMMPInverterPowerData();
  }).nameOverride = 'AMMP Inverter Power Data Polling';

  const ammpDailyInverterGenerationDataPollingRule =
    new schedule.RecurrenceRule();
  ammpDailyInverterGenerationDataPollingRule.minute = [59];
  schedule.scheduleJob(ammpDailyInverterGenerationDataPollingRule, () => {
    CronService.pollRecentAMMPDailyInverterGenerationData();
  }).nameOverride = 'AMMP Inverter Daily Generation Data Polling';

  /* -------------------------------------------------------------------------- */
  /*                              SGD Polling Jobs                              */
  /* -------------------------------------------------------------------------- */

  const sgdGenerationPollingRule = new schedule.RecurrenceRule();
  // NOTE: For now, we get site generation by summing inverters for Itaguais.
  // See note in sgd service file as to why. SGD Inverter daily generation
  // totals dont reset their value until the next day starts generating. So
  // we dont fetch early in the day because we will get the previous days total.
  sgdGenerationPollingRule.minute = [0, 59];
  sgdGenerationPollingRule.hour = [11, 13, 15, 17, 19, 21, 23];
  sgdGenerationPollingRule.tz = 'America/Sao_Paulo';
  schedule.scheduleJob(sgdGenerationPollingRule, () => {
    CronService.pollRecentSGDGenerationData();
  }).nameOverride = 'SGD System generation Polling';

  const sgdInverterSnapshotPollingRule = new schedule.RecurrenceRule();
  sgdInverterSnapshotPollingRule.minute = [0, 15, 30, 45];
  schedule.scheduleJob(sgdInverterSnapshotPollingRule, () => {
    CronService.pollRecentSGDInverterData();
  }).nameOverride = 'SGD System inverter snapshot Polling';

  const sgdInverterGenerationPollingRule = new schedule.RecurrenceRule();
  // NOTE: Fetch at the last minute of the hour so we can hit the last point of
  // the day even though generation should stop when the sun goes down.
  // NOTE: SGD Inverter daily generation totals dont reset their value until
  // the next day starts generating. So we dont fetch early in the day because
  // we will get the previous days total
  sgdInverterGenerationPollingRule.minute = [59];
  sgdInverterGenerationPollingRule.hour = [11, 13, 15, 17, 19, 21, 23];
  sgdInverterGenerationPollingRule.tz = 'America/Sao_Paulo';
  schedule.scheduleJob(sgdInverterGenerationPollingRule, () => {
    CronService.pollRecentSGDInverterGenerationData();
  }).nameOverride = 'SGD System inverter generation Polling';

  const sgdSensorPollingRule = new schedule.RecurrenceRule();
  sgdSensorPollingRule.minute = [0, 15, 30, 45];
  schedule.scheduleJob(sgdSensorPollingRule, () => {
    CronService.pollRecentSGDSensorData();
  }).nameOverride = 'SGD System sensor snapshot Polling';

  /* -------------------------------------------------------------------------- */
  /*                              SMA Polling Jobs                              */
  /* -------------------------------------------------------------------------- */
  const smaGenerationPollingRule = new schedule.RecurrenceRule();
  smaGenerationPollingRule.minute = [59];
  smaGenerationPollingRule.hour = [7, 9, 11, 13, 15, 17, 19, 21, 23];
  smaGenerationPollingRule.tz = 'America/Sao_Paulo';
  schedule.scheduleJob(smaGenerationPollingRule, () => {
    CronService.pollRecentSMAGenerationData();
  }).nameOverride = 'SMA Site generation Polling';

  const smaInverterSnapshotPollingRule = new schedule.RecurrenceRule();
  smaInverterSnapshotPollingRule.minute = [0, 15, 30, 45];
  schedule.scheduleJob(smaInverterSnapshotPollingRule, () => {
    CronService.pollRecentSMAInverterData();
  }).nameOverride = 'SMA Site inverter snapshot Polling';

  const smaInverterGenerationPollingRule = new schedule.RecurrenceRule();
  // NOTE: Fetch at the last minute of the hour so we can hit the last point of
  // the day even though generation should stop when the sun goes down.
  smaInverterGenerationPollingRule.minute = [59];
  smaInverterGenerationPollingRule.hour = [7, 9, 11, 13, 15, 17, 19, 21, 23];
  smaInverterGenerationPollingRule.tz = 'America/Sao_Paulo';
  schedule.scheduleJob(smaInverterGenerationPollingRule, () => {
    CronService.pollRecentSMAInverterGenerationData();
  }).nameOverride = 'SMA System inverter generation Polling';

  // const smaSensorPollingRule = new schedule.RecurrenceRule();
  // smaSensorPollingRule.minute = [0, 15, 30, 45];
  // schedule.scheduleJob(smaSensorPollingRule, () => {
  //   CronService.pollRecentSMASensorData();
  // }).nameOverride = 'SMA System sensor snapshot Polling';

  /* -------------------------------------------------------------------------- */
  /*                            Sungrow Polling Jobs                            */
  /* -------------------------------------------------------------------------- */
  const sungrowGenerationPollingRule = new schedule.RecurrenceRule();
  sungrowGenerationPollingRule.minute = [0];
  sungrowGenerationPollingRule.hour = [5, 7, 9, 11, 13, 15, 17, 19, 21, 23];
  sungrowGenerationPollingRule.tz = 'America/Sao_Paulo';
  schedule.scheduleJob(sungrowGenerationPollingRule, () => {
    CronService.pollRecentSungrowGenerationData();
  }).nameOverride = 'Sungrow system generation Polling';

  const sungrowInverterSnapshotPollingRule = new schedule.RecurrenceRule();
  sungrowInverterSnapshotPollingRule.minute = [0, 15, 30, 45];
  schedule.scheduleJob(sungrowInverterSnapshotPollingRule, () => {
    CronService.pollRecentSungrowInverterData();
  }).nameOverride = 'Sungrow system inverter snapshot Polling';

  const sungrowInverterGenerationPollingRule = new schedule.RecurrenceRule();
  sungrowInverterGenerationPollingRule.minute = [0];
  sungrowInverterGenerationPollingRule.hour = [
    5, 7, 9, 11, 13, 15, 17, 19, 21, 23,
  ];
  sungrowInverterGenerationPollingRule.tz = 'America/Sao_Paulo';
  schedule.scheduleJob(sungrowInverterGenerationPollingRule, () => {
    CronService.pollRecentSungrowInverterGenerationData();
  }).nameOverride = 'Sungrow System inverter generation Polling';

  const sungrowSensorPollingRule = new schedule.RecurrenceRule();
  sungrowSensorPollingRule.minute = [0, 15, 30, 45];
  schedule.scheduleJob(sungrowSensorPollingRule, () => {
    CronService.pollRecentSungrowSensorData();
  }).nameOverride = 'Sungrow System sensor snapshot Polling';

  /* -------------------------------------------------------------------------- */
  /*                          PowerFactors Polling Jobs                         */
  /* -------------------------------------------------------------------------- */

  const powerFactorEnergyPollingRule = new schedule.RecurrenceRule();
  powerFactorEnergyPollingRule.minute = [0, 15, 30, 45]; // scheduled for every 15 minutes + 1 min each hour
  schedule.scheduleJob(powerFactorEnergyPollingRule, () => {
    CronService.pollLivePowerFactorSystemProductionData();
  }).nameOverride = 'PowerFactor System Energy Polling';

  const powerFactorSensorDataPollingRule = new schedule.RecurrenceRule();
  powerFactorSensorDataPollingRule.minute = [0, 15, 30, 45];
  schedule.scheduleJob(powerFactorSensorDataPollingRule, () => {
    CronService.pollLivePowerFactorSensorData();
  }).nameOverride = 'PowerFactor System Sensor Data Polling';

  const powerFactorInverterPowerPollingRule = new schedule.RecurrenceRule();
  powerFactorInverterPowerPollingRule.minute = [0, 15, 30, 45];
  schedule.scheduleJob(powerFactorInverterPowerPollingRule, () => {
    CronService.pollLivePowerFactorInverterData();
  }).nameOverride = 'PowerFactor Inverter Power Polling';

  // const powerFactorDailyInverterGenerationPollingRule =
  //   new schedule.RecurrenceRule();
  //   powerFactorDailyInverterGenerationPollingRule.minute = 59; // scheduled to run end of each hour
  // schedule.scheduleJob(powerFactorDailyInverterGenerationPollingRule, () => {
  //   CronService.pollRecentPowerFactorDailyInverterGenerationData();
  // }).nameOverride = 'PowerFactor Daily Inverter Generation Polling';

  /* -------------------------------------------------------------------------- */
  /*                             Solis Polling Jobs                             */
  /* -------------------------------------------------------------------------- */

  const solisGenerationPollingRule = new schedule.RecurrenceRule();
  solisGenerationPollingRule.minute = [5, 35];
  schedule.scheduleJob(solisGenerationPollingRule, () => {
    CronService.pollRecentSolisGenerationData();
  }).nameOverride = 'Solis System generation Polling';

  const solisInverterSnapshotPollingRule = new schedule.RecurrenceRule();
  solisInverterSnapshotPollingRule.minute = [5, 20, 35, 50];
  schedule.scheduleJob(solisInverterSnapshotPollingRule, () => {
    CronService.pollRecentSolisInverterSnapshotData();
  }).nameOverride = 'Solis System inverter snapshot Polling';

  const solisInverterGenerationPollingRule = new schedule.RecurrenceRule();
  // NOTE: Make sure this runs daily before monitoringPunchListReportEmailRule
  solisInverterGenerationPollingRule.minute = 0;
  solisInverterGenerationPollingRule.hour = [1, 4];
  solisInverterGenerationPollingRule.tz = 'America/Sao_Paulo';
  schedule.scheduleJob(solisInverterGenerationPollingRule, () => {
    CronService.pollRecentSolisInverterGenerationData();
  }).nameOverride = 'Solis System inverter generation Polling';

  const solisSensorPollingRule = new schedule.RecurrenceRule();
  solisSensorPollingRule.minute = [5, 20, 35, 50];
  schedule.scheduleJob(solisSensorPollingRule, () => {
    CronService.pollRecentSolisSensorData();
  }).nameOverride = 'Solis System sensor snapshot Polling';

  /* -------------------------------------------------------------------------- */
  /*                            GreenAnt Polling Jobs                           */
  /* -------------------------------------------------------------------------- */

  const greenAntMeterEnergyPollingRule = new schedule.RecurrenceRule();
  greenAntMeterEnergyPollingRule.minute = [14, 29, 44, 59]; // scheduled for every 15 minutes - 1 min each hour
  schedule.scheduleJob(greenAntMeterEnergyPollingRule, () => {
    CronService.pollLiveGreenAntMeterProductionData();
  }).nameOverride = 'GreenAnt Meter Energy Polling';

  /* -------------------------------------------------------------------------- */
  /*                          Expected Generation Jobs                          */
  /* -------------------------------------------------------------------------- */

  const expectedHourSchedule = [
    4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21,
  ];

  const scadaSystemCalculateExpectedGenerationRule =
    new schedule.RecurrenceRule();
  scadaSystemCalculateExpectedGenerationRule.hour = expectedHourSchedule;
  scadaSystemCalculateExpectedGenerationRule.minute = 0;
  scadaSystemCalculateExpectedGenerationRule.tz = 'America/Sao_Paulo';
  schedule.scheduleJob(scadaSystemCalculateExpectedGenerationRule, () => {
    CronService.scadaSystemCalculateExpectedGeneration();
  }).nameOverride = 'SCADA System Calculate Expected Generation';

  const solisSystemCalculateExpectedGenerationRule =
    new schedule.RecurrenceRule();
  solisSystemCalculateExpectedGenerationRule.hour = expectedHourSchedule;
  solisSystemCalculateExpectedGenerationRule.minute = 0;
  solisSystemCalculateExpectedGenerationRule.tz = 'America/Sao_Paulo';
  schedule.scheduleJob(solisSystemCalculateExpectedGenerationRule, () => {
    CronService.solisSystemCalculateExpectedGeneration();
  }).nameOverride = 'Solis System Calculate Expected Generation';

  const sungrowSystemCalculateExpectedGenerationRule =
    new schedule.RecurrenceRule();
  sungrowSystemCalculateExpectedGenerationRule.hour = expectedHourSchedule;
  sungrowSystemCalculateExpectedGenerationRule.minute = 0;
  sungrowSystemCalculateExpectedGenerationRule.tz = 'America/Sao_Paulo';
  schedule.scheduleJob(sungrowSystemCalculateExpectedGenerationRule, () => {
    CronService.sungrowSystemCalculateExpectedGeneration();
  }).nameOverride = 'Sungrow System Calculate Expected Generation';

  const solarEdgeCalculateExpectedGenerationRule =
    new schedule.RecurrenceRule();
  solarEdgeCalculateExpectedGenerationRule.hour = expectedHourSchedule;
  solarEdgeCalculateExpectedGenerationRule.minute = 0;
  solarEdgeCalculateExpectedGenerationRule.tz = 'America/New_York';
  schedule.scheduleJob(solarEdgeCalculateExpectedGenerationRule, () => {
    CronService.solarEdgeCalculateExpectedGeneration();
  }).nameOverride = 'SolarEdge System Calculate Expected Generation';

  const sgdSystemCalculateExpectedGenerationRule =
    new schedule.RecurrenceRule();
  sgdSystemCalculateExpectedGenerationRule.hour = expectedHourSchedule;
  sgdSystemCalculateExpectedGenerationRule.minute = 0;
  sgdSystemCalculateExpectedGenerationRule.tz = 'America/Sao_Paulo';
  schedule.scheduleJob(sgdSystemCalculateExpectedGenerationRule, () => {
    CronService.sgdSystemCalculateExpectedGeneration();
  }).nameOverride = 'SGD System Calculate Expected Generation';

  const sunExchangeSiteCalculateExpectedGenerationRule =
    new schedule.RecurrenceRule();
  sunExchangeSiteCalculateExpectedGenerationRule.hour = expectedHourSchedule;
  sunExchangeSiteCalculateExpectedGenerationRule.minute = 0;
  sunExchangeSiteCalculateExpectedGenerationRule.tz = 'Africa/Johannesburg'; // CapeTown South Africa timezone
  schedule.scheduleJob(sunExchangeSiteCalculateExpectedGenerationRule, () => {
    CronService.sunExchangeSiteCalculateExpectedGeneration();
  }).nameOverride = 'SunExchange Site Calculate Expected Generation';
}

const monitorGenerationDataRule = new schedule.RecurrenceRule();
monitorGenerationDataRule.hour = 12;
monitorGenerationDataRule.minute = 0;
monitorGenerationDataRule.tz = 'America/New_York';
schedule.scheduleJob(monitorGenerationDataRule, () => {
  CronService.monitorGenerationData();
}).nameOverride = 'Monitor generation data';

const monitoringPunchListReportEmailRule = new schedule.RecurrenceRule();
// NOTE: Make sure this runs daily after scadaSystemDailyInverterGenerationRule and solisInverterGenerationPollingRule
monitoringPunchListReportEmailRule.hour = 6;
monitoringPunchListReportEmailRule.minute = 0;
monitoringPunchListReportEmailRule.tz = 'America/New_York';
schedule.scheduleJob(monitoringPunchListReportEmailRule, () => {
  CronService.sendMonitoringPunchlistReport();
}).nameOverride = 'Monitoring punchlist report email';

const omMonthlyReportRule = new schedule.RecurrenceRule();
omMonthlyReportRule.date = 1;
omMonthlyReportRule.hour = 7;
omMonthlyReportRule.minute = 0;
omMonthlyReportRule.tz = 'America/New_York';
schedule.scheduleJob(omMonthlyReportRule, () => {
  CronService.runMonthlyOMReport();
}).nameOverride = 'Monthly O&M Report';

const energeaCorporateWalletBalanceCheckRule = new schedule.RecurrenceRule();
energeaCorporateWalletBalanceCheckRule.hour = 10;
energeaCorporateWalletBalanceCheckRule.minute = 0;
energeaCorporateWalletBalanceCheckRule.tz = 'America/New_York';
schedule.scheduleJob(energeaCorporateWalletBalanceCheckRule, () => {
  CronService.checkEnergeaWalletBalance();
}).nameOverride = 'Energea Corp. Wallet Balance Check';

const fetchLatestExchangeRatesRule = new schedule.RecurrenceRule();
fetchLatestExchangeRatesRule.hour = 0;
fetchLatestExchangeRatesRule.minute = 0;
fetchLatestExchangeRatesRule.tz = 'America/New_York';
schedule.scheduleJob(fetchLatestExchangeRatesRule, () => {
  CronService.fetchExchangeRates();
}).nameOverride = 'Fetch latest exchange rates';

const preferredReceivingAccountHealthCheck = new schedule.RecurrenceRule();
preferredReceivingAccountHealthCheck.date = [1, 10, 20];
preferredReceivingAccountHealthCheck.hour = 2;
preferredReceivingAccountHealthCheck.minute = 0;
preferredReceivingAccountHealthCheck.tz = 'America/New_York';
schedule.scheduleJob(preferredReceivingAccountHealthCheck, () => {
  CronService.preferredReceivingAccountHealthCheck();
}).nameOverride = 'Preferred Receiving Account Health Check';

// const matchPromoMembershipCheck = new schedule.RecurrenceRule();
// matchPromoMembershipCheck.hour = 17; // 5pm EST so that its sending at a good time. This is a marketing email
// matchPromoMembershipCheck.minute = 0;
// matchPromoMembershipCheck.tz = 'America/New_York';
// schedule.scheduleJob(matchPromoMembershipCheck, () => {
//   CronService.createMatchPromosIfEligible();
// }).nameOverride = 'Match Promo Membership Check';

Object.keys(schedule.scheduledJobs).forEach((scheduledJobKey) => {
  console.log(
    `Cron job (${
      schedule.scheduledJobs[String(scheduledJobKey)].nameOverride
    }) set. Next time it will run is ${moment(
      schedule.scheduledJobs[String(scheduledJobKey)].nextInvocation()._date.ts
    ).format('lll')}`
  );
});
