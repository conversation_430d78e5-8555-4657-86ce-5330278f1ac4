import { ApolloError } from 'apollo-server-express';
import { Sequelize } from 'sequelize';
import database from '../models/index';
import { sendIssueEmail } from './mail';
import DwollaService from './dwolla';
import SellOrderService from './sellOrder';

const { Op, fn, col } = Sequelize;

const Investment = database.models.investment;
const Portfolio = database.models.portfolio;
const SellOrder = database.models.sellOrder;
const ShareTransfer = database.models.shareTransfer;
const Transfer = database.models.transfer;
const User = database.models.user;

const naturalSharesPurchased = (investment) => {
  if (investment.cancelledDt) {
    return 0;
  }

  // If shareTransfers are already loaded, use them directly
  if (investment.shareTransfers) {
    const totalResoldShares = investment.shareTransfers.reduce(
      (sum, transfer) => sum + parseFloat(transfer.soldShares || 0),
      0
    );
    return investment.shares - totalResoldShares;
  }

  return investment
    .getShareTransfers({
      attributes: [
        [fn('sum', col('soldShares')), 'totalResoldSharesPurchased'],
      ],
    })
    .then(
      (res) =>
        investment.shares - (res[0].dataValues.totalResoldSharesPurchased || 0)
    );
};

const naturalSharesPurchasedValue = (investment) => {
  if (investment.cancelledDt) {
    return 0;
  }

  // If shareTransfers are already loaded, use them directly
  if (investment.shareTransfers) {
    const totalResoldValue = investment.shareTransfers.reduce(
      (sum, transfer) => sum + parseFloat(transfer.value || 0),
      0
    );
    return investment.value - totalResoldValue;
  }

  return investment
    .getShareTransfers({
      attributes: [
        [fn('sum', col('value')), 'totalResoldSharesPurchasedValue'],
      ],
    })
    .then(
      (res) =>
        investment.value -
        (res[0].dataValues.totalResoldSharesPurchasedValue || 0)
    );
};

const crowdSharesPurchased = (investment) => {
  // This excludes shares that Energea sold
  if (investment.cancelledDt) {
    return 0;
  }
  return investment
    .getShareTransfers({
      attributes: [
        [fn('sum', col('soldShares')), 'totalResoldSharesPurchased'],
      ],
      raw: true,
      include: [
        {
          attributes: [],
          model: SellOrder,
          required: true,
          where: { userId: { [Op.not]: 76 } },
        },
      ],
    })
    .then(
      (res) => investment.shares - (res[0].totalResoldSharesPurchased || 0)
    );
};

const crowdSharesPurchasedValue = (investment) => {
  // This excludes shares that Energea sold
  if (investment.cancelledDt) {
    return 0;
  }
  return investment
    .getShareTransfers({
      attributes: [
        [fn('sum', col('value')), 'totalResoldSharesPurchasedValue'],
      ],
      raw: true,
      include: [
        {
          attributes: [],
          model: SellOrder,
          required: true,
          where: { userId: { [Op.not]: 76 } },
        },
      ],
    })
    .then(
      (res) => investment.value - (res[0].totalResoldSharesPurchasedValue || 0)
    );
};

const cancelInvestment = async (investmentId) => {
  const investmentToCancel = await Investment.findByPk(investmentId, {
    include: [
      {
        model: Transfer,
        required: false,
      },
    ],
  });

  if (!investmentToCancel) {
    throw new ApolloError(`No investment found with id ${investmentId}`);
  }

  // cancel dwolla transfers
  if (
    investmentToCancel.transfer &&
    investmentToCancel.transfer.dwollaTransferIds
  ) {
    for (
      let index2 = 0;
      index2 < investmentToCancel.transfer.dwollaTransferIds.length;
      index2 += 1
    ) {
      const dwollaTransferId =
        investmentToCancel.transfer.dwollaTransferIds[parseInt(index2, 10)];
      // eslint-disable-next-line no-await-in-loop
      await DwollaService.cancelDwollaTransfer(dwollaTransferId).then(
        () => {},
        (error) => {
          sendIssueEmail({
            description:
              'Error cancelling dwolla transfer for failed blend product investment',
            oData: {
              investmentToCancel,
              error,
            },
          });
          throw new ApolloError(
            `Error cancelling dwolla transfer for failed blend product investment ${investmentId}`,
            {
              error,
            }
          );
        }
      );
    }
  }

  // delete share transfers
  await ShareTransfer.destroy({
    where: {
      investmentId: investmentToCancel.id,
    },
  }).then(
    () => {
      console.log(
        `Successfully destroyed share transfers from failed investment ${investmentToCancel.id}`
          .bold.green
      );
    },
    (error) => {
      throw new ApolloError(
        `Error deleting share transfers associated with failed investment ${investmentId}`,
        {
          error,
        }
      );
    }
  );

  // set cancelledDt
  const cancelledInvestment = investmentToCancel;
  cancelledInvestment.cancelledDt = new Date();
  return cancelledInvestment.save().then(
    (res) => res,
    (e) => {
      throw new ApolloError(
        `Error setting cancelledDt on investment with id ${investmentId}`,
        {
          e,
        }
      );
    }
  );
};

const cancelBlendedProductInvestment = async (blendedProductInvestmentId) => {
  const investmentsToCancel = await Investment.findAll({
    where: {
      blendedProductInvestmentId,
      cancelledDt: null,
    },
  });
  console.error(`Cancelling blended product investment...`.bold.red);
  try {
    const cancelPromises = [];
    for (let index = 0; index < investmentsToCancel.length; index += 1) {
      const investmentToCancel = investmentsToCancel[parseInt(index, 10)];
      cancelPromises.push(cancelInvestment(investmentToCancel.id));
    }
    return Promise.all(cancelPromises).then(
      () => {
        console.log(
          `All investments associated with blended product investment ${blendedProductInvestmentId} have been successfully cancelled.`
        );
        return {
          success: true,
          message: 'Investments cancelled',
        };
      },
      (error) => {
        console.error(
          `Error cancelling investments for failed blend product investment ${blendedProductInvestmentId}`
            .bold.red,
          error
        );
        sendIssueEmail({
          description:
            'Error cancelling investments for failed blend product investment',
          oData: {
            blendedProductInvestmentId,
            investmentsToCancel,
            error,
          },
        });
        return {
          success: false,
          message: 'Error cancelling investments',
        };
      }
    );
  } catch (error) {
    sendIssueEmail({
      description:
        'Error cancelling investments for failed blend product investment',
      oData: {
        blendedProductInvestmentId,
        investmentsToCancel,
        error,
      },
    });
    return {
      success: false,
      message: 'Error cancelling investments',
    };
  }
};

const issueInvestmentShareTransferTransfers = async (investment) => {
  const shareTransfers = await ShareTransfer.findAll({
    where: {
      [Sequelize.Op.and]: [
        {
          investmentId: investment.id,
        },
        {
          sellDt: investment.startDt, // NOTE: potential liability as the sellDt and startDt must not be altered.
        },
      ],
    },
    include: [
      {
        model: SellOrder,
        include: [
          {
            model: Portfolio,
          },
          {
            model: User,
          },
        ],
      },
    ],
  });
  if (!shareTransfers || shareTransfers.length === 0) return null;

  const aPromises = [];
  for (let index = 0; index < shareTransfers.length; index++) {
    const shareTransfer = shareTransfers[parseInt(index, 10)];
    aPromises.push(
      SellOrderService.getShareTransferToAccountId(
        shareTransfer.sellOrder
      ).then(async (toAccountId) => {
        const transferResults = await DwollaService.transferFunds({
          amount: parseFloat(shareTransfer.value),
          type: 'shareTransfer',
          userId: shareTransfer.sellOrder.userId,
          shareTransferId: shareTransfer.id,
          toAccountId,
          fromAccountId:
            shareTransfer.sellOrder.portfolio.dwollaFundingSourceId,
        });

        if (transferResults && transferResults.success) {
          return transferResults;
        }
        sendIssueEmail({
          description: 'Issue handling share order transfer',
          oData: {
            transferResults,
            investment,
          },
        });
        return new ApolloError(transferResults, 'APPLICATION', {
          error: 'Error transferring funds for share transfer',
        });
      })
    );
  }
  return Promise.all(aPromises);
};

const getInvestmentFeedQueryConfig = (filter, sort) => {
  const includes = [];
  const filterClauses = {};

  if (sort.field === 'portfolio.name') {
    includes.push({
      model: Portfolio,
      attributes: ['id', 'name'],
    });
  }

  const userFilter = {};

  if (filter.q && filter.q.length > 0) {
    userFilter[Op.or] = [
      {
        firstName: {
          [Op.iLike]: `${filter.q}%`,
        },
      },
      {
        lastName: {
          [Op.iLike]: `${filter.q}%`,
        },
      },
    ];
  }
  if (filter.investorState) {
    userFilter.state = {
      [Op.iLike]: `${filter.investorState}%`,
    };
  }
  if (filter.value !== null && filter.value !== undefined) {
    filterClauses.value = filter.value;
  }

  includes.push({
    model: User,
    attributes: [
      'id',
      'state',
      'email',
      'address1',
      'address2',
      'city',
      'postalCode',
      'oktaId',
      'firstName',
      'lastName',
      'isAccredited',
    ],
    where: userFilter,
  });

  if (filter.portfolio) {
    filterClauses.portfolioId = filter.portfolio.id;
  }
  if (filter.subAccountFilter) {
    if (filter.subAccountFilter === 'mtOnly') {
      filterClauses.subAccountId = {
        [Op.not]: null,
      };
    }
  }
  if (filter.startDtLowerBound && !filter.startDtUpperBound) {
    filterClauses.startDt = {
      [Op.gte]: filter.startDtLowerBound,
    };
  } else if (filter.startDtUpperBound && !filter.startDtLowerBound) {
    filterClauses.startDt = {
      [Op.lte]: filter.startDtUpperBound,
    };
  } else if (filter.startDtUpperBound && filter.startDtLowerBound) {
    filterClauses[Op.and] = [
      {
        startDt: {
          [Op.gte]: filter.startDtLowerBound,
        },
      },
      {
        startDt: {
          [Op.lte]: filter.startDtUpperBound,
        },
      },
    ];
  }

  return {
    includes,
    filterClauses,
  };
};

export default {
  cancelInvestment,
  cancelBlendedProductInvestment,
  getInvestmentFeedQueryConfig,
  issueInvestmentShareTransferTransfers,
  naturalSharesPurchased,
  naturalSharesPurchasedValue,
  crowdSharesPurchased,
  crowdSharesPurchasedValue,
};
