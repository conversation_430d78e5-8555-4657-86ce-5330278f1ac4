import throng from 'throng';
import Queue from 'bull';
import numeral from 'numeral';
import moment from 'moment-timezone';
import { Sequelize } from 'sequelize';
import fs from 'fs';
import xl from 'excel4node';
import ExcelJS from 'exceljs';
import { PassThrough } from 'stream';
import openExchangeRates from 'open-exchange-rates';
import money from 'money';
import jsonfile from 'jsonfile';
import validator from 'validator';
import { merge } from 'merge-pdf-buffers';
// NOTE: The import below is needed to use numeral.locale(...)

import database from '../models/index';
import {
  // sendAutoReinvestDividendTransferCompleted,
  sendIssueEmail,
  sendMonthlyPortfolioReconciliationDocumentsReadyEmail,
  sendMonitoringPunchlistReportEmail,
  // sendMTAccountFeePaymentReportEmail,
  sendMTMonthlyReconciliationReportsEmail,
  sendOMMonthlyReportEmail,
  sendDividendsPaidSummaryEmail,
  sendCommunitySolarOffTakerInvoiceEmail,
  sendMatchPromoCodeGrantedEmail,
  sendEquitySummaryReportEmail,
  sendUtilityBillConversionEmail,
  sendIRAAccountOpenedEmail,
  sendExportedCMSList,
  sendSalesPartnerCustomerDefaultedEmail,
} from './mail';
import createInvestment from './createInvestment';
import DividendService from './dividend';
import DwollaService from './dwolla';
import HubSpotService from './hubSpot';
import PortfolioService from './portfolio';
import SellOrderService from './sellOrder';
import SlackService from './slack';
import UserService from './user';
import SCADAService from './scada';
import SGDService from './sgd';
import ShareTransferService from './shareTransfer';
import TransferService from './transfer';
import PowerFactorService from './powerFactor';
import {
  getAccountCashBalance,
  getMTOfflinePaymentDetailsString,
  handleCheckVerificationStatus,
  unhideFromIRATracker,
} from './millenniumTrust';
import MarketingService from './marketing';

import {
  // eslint-disable-next-line import/named
  createQuarterlyUserLedger,
  omReport,
} from './pdfKit';
import {
  getObjectAsBuffer,
  uploadObjectToS3,
  uploadStreamToS3,
  getSignedUrl,
} from './aws';
import ExporterService from './exporter';
import InvestmentService from './investment';
import IPQSService from './ipQualityScore';
// import FingerprintService from './fingerprint';
import SolarEdgeService from './solarEdge';
import GreenAntService from './greenAnt';
import SunExchangeService from './sunExchange';
import SolisService from './solis';
import SMAService from './sma';
import SolarCloudService from './solarCloud';
import { bullOptions } from './redis';
import PromoService from './promo';
import {
  createProjectPunchDetailTab,
  getModuleTempRef,
  getProjectSensors,
  getProjectInverters,
  getProjectSunrise,
  getProjectSunset,
  getPerformanceRatio,
  getPOAIrradianceFactor,
  projectMonitoringPunchList,
} from './project';

import {
  constants,
  convertBrStateToStateCode,
  englishToPortugueseMonthTranslation,
  formatBoletoBarcode,
  formatBrZipCode,
  formatEnergeaIRAType,
  // formatShares,
  isPromise,
  getOMPreventativeMaintenancePlanPeriodicityData,
  sleep,
  stringifyObject,
  lintAwsObjectKey,
} from '../../utils/global';
import { calculatePlatformNavBasedIRR } from './platform';
import { getProjectTimezone, wait } from '../lib/server-utils';
import {
  getProductionTotalsData,
  getTicketEstimatedImpact,
  saveUpdateDailyInverterGeneration,
} from './monitoring';
import { writeToCell } from './excel';
import {
  fetchEnergisaBillConversion,
  fetchLightBillConversion,
} from './infosimples';
import { sendPushNotificationToAll } from './pushNotification';
import { getAccountDetails } from './millenniumTrustUtils';
import {
  createStripeInvoice,
  getStripeInvoiceDescriptionString,
  retrieveStripeInvoice,
} from './stripe';
import {
  createEnergeaConsortiumStatement,
  getBrBillingCycleAvailableCredits,
  getBrBillingCycleBilledCredits,
  getBrBillingCycleCMSGeneration,
  getBrBillingCycleCreditAdjustmentTotal,
  getBrBillingCycleCurrentRevenue,
  getBrBillingCycleExpectedRevenue,
  getBrBillingCycleNumberOfConsumerUnits,
  getBrBillingCycleTotalGrossConsumption,
  getBrBillingCycleUCBalance,
  getBrConsumerUnitFeedQueryConfig,
  getBrInvoiceFeedQueryConfig,
  getPrecedingBrBillingCycle,
} from './creditManagement';
import {
  createStarkBankBoleto,
  getStarkBankBoletoPdfBuffer,
  getStarkBankBoleto,
} from './stark';
import { getMTCEstimatedInvestableBalance } from './subAccount';
// import { sendInvoiceReadyWhatsAppMessage } from './whatsapp';

const { Op, fn, col } = Sequelize;

const AutoInvestSubscription = database.models.autoInvestSubscription;
const AutoReinvestIndicator = database.models.autoReinvestIndicator;
const BlendedProductInvestment = database.models.blendedProductInvestment;
const BrInvoice = database.models.brInvoice;
const BrConsortium = database.models.brConsortium;
const BrBillingCycle = database.models.brBillingCycle;
const BrContactsBrCustomer = database.models.brContactsBrCustomer;
const BrContact = database.models.brContact;
const BrCustomer = database.models.brCustomer;
const BrConsumerUnit = database.models.brConsumerUnit;
const BrCreditCompensation = database.models.brCreditCompensation;
const BudgetLineItem = database.models.budgetLineItem;
const Country = database.models.country;
const Device = database.models.device;
const Dividend = database.models.dividend;
const Employee = database.models.employee;
const EmployeeType = database.models.employeeType;
const EquipmentItem = database.models.equipmentItem;
const EventNotification = database.models.eventNotification;
const ExchangeRate = database.models.exchangeRate;
const ExpectedProductionPeriod = database.models.expectedProductionPeriod;
const GreenAntMeter = database.models.greenAntMeter;
const HistoricalPerformancePoint = database.models.historicalPerformancePoint;
const HubSpotLeadSource = database.models.hubSpotLeadSource;
const Inverter = database.models.inverter;
const InverterProductionPeriod = database.models.inverterProductionPeriod;
const Investment = database.models.investment;
const InvestorReturn = database.models.investorReturn;
const IpAddress = database.models.ipAddress;
const LeadSourceCategory = database.models.leadSourceCategory;
const MillenniumTrustAuthSession = database.models.millenniumTrustAuthSession;
const MillenniumTrustFundingSession =
  database.models.millenniumTrustFundingSession;
const MonthlyPortfolioFinancialActual =
  database.models.monthlyPortfolioFinancialActual;
const OMMonthlyReport = database.models.omMonthlyReport;
const OMPmpChecklistItem = database.models.omPmpChecklistItem;
const OMPmpPeriodicity = database.models.omPmpPeriodicity;
const OMReport = database.models.omReport;
const OMReportImage = database.models.omReportImage;
const OMReportType = database.models.omReportType;
const OMTicket = database.models.omTicket;
const OMTicketType = database.models.omTicketType;
const OMTruck = database.models.omTruck;
const PendingDwollaDividendPayment =
  database.models.pendingDwollaDividendPayment;
const Portfolio = database.models.portfolio;
const PortfolioSharePrice = database.models.portfolioSharePrice;
const PowerFactorSystem = database.models.powerFactorSystem;
const ProductionPeriod = database.models.productionPeriod;
const Project = database.models.project;
const PromoCode = database.models.promoCode;
const QueryCache = database.models.queryCache;
const QuarterlyUserFinancialStatement =
  database.models.quarterlyUserFinancialStatement;
const SalesforceProject = database.models.salesforceProject;
const ScadaSystem = database.models.scadaSystem;
const SellOrder = database.models.sellOrder;
const Sensor = database.models.sensor;
const SensorDataPeriod = database.models.sensorDataPeriod;
const SgdSystem = database.models.sgdSystem;
const ShareTransfer = database.models.shareTransfer;
const SmaSite = database.models.smaSite;
const SolarEdgeSite = database.models.solarEdgeSite;
const SolisSystem = database.models.solisSystem;
const SubAccount = database.models.subAccount;
const SunExchangeSite = database.models.sunExchangeSite;
const SungrowSystem = database.models.sungrowSystem;
const Transfer = database.models.transfer;
const TUSDInvoice = database.models.tusdInvoice;
const User = database.models.user;
const UserLogin = database.models.userLogin;

const aModelers = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];

// Helper function to check if error is a known API connection failure
// Used to prevent flooding Slack with notifications when external APIs are down
const isAPIConnectionError = (error) => {
  if (!error) return false;
  const errorString = String(error);
  const connectionErrorPatterns = [
    'ECONNREFUSED',
    'ETIMEDOUT',
    'ENOTFOUND',
    'Network Error',
    'status code 5',
    'Request failed',
    'Error fetching auth token',
    'timeout',
    'connect ETIMEDOUT',
  ];
  return connectionErrorPatterns.some((pattern) =>
    errorString.toLowerCase().includes(pattern.toLowerCase())
  );
};

// Connect to a local redis instance locally, and the Heroku-provided URL in production
// Spin up multiple processes to handle jobs to take advantage of more CPU cores
// See: https://devcenter.heroku.com/articles/node-concurrency for more info
const workers = process.env.WEB_CONCURRENCY || 1;

console.log(`Worker throng using WEB_CONCURRENCY of ${workers}.`);

// The maximum number of jobs each worker should process at once. This will need
// to be tuned for your application. If each job is mostly waiting on network
// responses it can be much higher. If each job is CPU-intensive, it might need
// to be much lower.
const maxJobsPerWorker = 30;

const longRunningConfig = (minutes) => {
  const returnOptions = { ...bullOptions };
  returnOptions.settings = {
    lockDuration: minutes * 60 * 1000, // default 30000 (30 secs). The amount of time until a job is marked as stalled and gets rerun. Will allow jobs more time to process when increased
  };
  return returnOptions;
};
const defaultConfig = bullOptions;

// Full list of events here: https://github.com/OptimalBits/bull/blob/f1630220c242ddcee5d2bf93e58d49286b58a5bf/REFERENCE.md#events
const applyListeners = (queue) => {
  queue.on('error', (error) => {
    // An error occurred.
    const msg = `Redis Queue Error : ${queue?.name}`;
    console.error(msg, error);
  });

  // eslint-disable-next-line no-unused-vars
  queue.on('waiting', (jobId) => {
    // A Job is waiting to be processed as soon as a worker is idling.
  });

  // eslint-disable-next-line no-unused-vars
  queue.on('active', (job, jobPromise) => {
    // A job has started. You can use `jobPromise.cancel()`` to abort it.
    console.log(`Redis Queue (${job.queue.name}) - STARTED`);
  });

  queue.on('stalled', (job) => {
    // A job has been marked as stalled. This is useful for debugging job
    // workers that crash or pause the event loop.
    const msg = `Redis Queue (${
      job.queue.name
    }) - STALLED. Job: ${stringifyObject(job)}. Failed reason: ${
      job.failedReason
    }.`;
    SlackService.logToSlack({
      title: msg,
      data: [
        {
          label: 'Attempts made',
          value: job.attemptsMade,
        },
      ],
      type: 'bull-queue',
    });
    console.warn(msg);
  });

  queue.on('lock-extension-failed', (job, err) => {
    // A job failed to extend lock. This will be useful to debug redis
    // connection issues and jobs getting restarted because workers
    // are not able to extend locks.
    const msg = `Redis Queue (${
      job.queue.name
    }) - LOCK-EXTENSION-FAILED. Error: ${stringifyObject(err)}`;
    SlackService.logToSlack({
      title: msg,
      data: [
        {
          label: 'Error',
          value: stringifyObject(err),
        },
      ],
      type: 'bull-queue',
    });
    console.error(msg);
  });

  // eslint-disable-next-line no-unused-vars
  queue.on('progress', (job, progress) => {
    // A job's progress was updated!
  });

  // eslint-disable-next-line no-unused-vars
  queue.on('completed', (job, result) => {
    // A job successfully completed with a `result`.
    const msg = `Redis Queue (${job.queue.name}) - COMPLETED.`;
    console.log(msg);
  });

  queue.on('failed', (job, err) => {
    const criticalProcesses = ['checkForRecurringInvestmentSubscriptions'];
    if (criticalProcesses.includes(job.queue.name)) {
      sendIssueEmail({
        description: `Failed to run ${job.queue.name} queue.`,
        oData: {
          job,
          err,
        },
      });
    }
    // A job failed with reason `err`!
    const msg = `Redis Queue (${job.queue.name}) - FAILED. Job:`;
    SlackService.logToSlack({
      title: msg,
      data: [
        {
          label: 'Error',
          value: stringifyObject(err),
        },
      ],
      type: 'bull-queue',
    });
    console.error(msg, stringifyObject(err));
  });

  queue.on('paused', () => {
    // The queue has been paused.
  });

  // eslint-disable-next-line no-unused-vars
  queue.on('resumed', (job) => {
    // The queue has been resumed.
  });

  // eslint-disable-next-line no-unused-vars
  queue.on('cleaned', (jobs, type) => {
    // Old jobs have been cleaned from the queue. `jobs` is an array of cleaned
    // jobs, and `type` is the type of jobs cleaned.
  });

  queue.on('drained', () => {
    // Emitted every time the queue has processed all the waiting jobs (even if there can be some delayed jobs not yet processed)
    // console.log(`Redis Queue (${queue.name || ''}) - EMPTY.`);
  });

  // eslint-disable-next-line no-unused-vars
  queue.on('removed', (job) => {
    // A job successfully removed.
  });
};

function start() {
  const setMonthlyLtvCacChartDataCache = new Queue(
    'setMonthlyLtvCacChartDataCache',
    longRunningConfig(10)
  );
  applyListeners(setMonthlyLtvCacChartDataCache);

  const sendConsortiumInvoiceEmailQueue = new Queue(
    'sendConsortiumInvoiceEmail',
    defaultConfig
  );
  applyListeners(sendConsortiumInvoiceEmailQueue);

  const checkPotentialPreventativeMaintenanceTaskQueue = new Queue(
    'checkPotentialPreventativeMaintenanceTask',
    defaultConfig
  );
  applyListeners(checkPotentialPreventativeMaintenanceTaskQueue);

  const creditManagementDashboardExportQueue = new Queue(
    'creditManagementDashboardExport',
    longRunningConfig(10)
  );
  applyListeners(creditManagementDashboardExportQueue);

  const createStripeInvoiceQueue = new Queue(
    'createStripeInvoice',
    defaultConfig
  );
  applyListeners(createStripeInvoiceQueue);

  const createStarkBoletoQueue = new Queue('createStarkBoleto', defaultConfig);
  applyListeners(createStarkBoletoQueue);

  const setLtvToCacDataCache = new Queue(
    'setLtvToCacDataCache',
    longRunningConfig(5)
  );
  applyListeners(setLtvToCacDataCache);

  const updateInvestorReturns = new Queue(
    'updateInvestorReturns',
    longRunningConfig(2)
  );
  applyListeners(updateInvestorReturns);

  // Connect to the named work queue
  const recalculateProjectedSharePriceQueue = new Queue(
    'recalculateProjectedSharePrice',
    longRunningConfig(10)
  );
  applyListeners(recalculateProjectedSharePriceQueue);

  const recalculateSharePriceQueue = new Queue(
    'recalculateSharePrice',
    longRunningConfig(10)
  );
  applyListeners(recalculateSharePriceQueue);

  const fetchExchangeRatesQueue = new Queue(
    'fetchExchangeRates',
    defaultConfig
  );
  applyListeners(fetchExchangeRatesQueue);

  const checkForRecurringInvestmentSubscriptionsQueue = new Queue(
    'checkForRecurringInvestmentSubscriptions',
    longRunningConfig(20)
  );
  applyListeners(checkForRecurringInvestmentSubscriptionsQueue);

  const hubSpotUserLeadSourceSyncQueue = new Queue(
    'pullUserLeadSourceFromHubSpot',
    defaultConfig
  );
  applyListeners(hubSpotUserLeadSourceSyncQueue);

  const utilityBillConversionQueue = new Queue(
    'utilityBillConversion',
    defaultConfig
  );
  applyListeners(utilityBillConversionQueue);

  const sellOrderHealthCheckQueue = new Queue(
    'sellOrderHealthCheck',
    defaultConfig
  );
  applyListeners(sellOrderHealthCheckQueue);

  const cashoutSellOrderQueue = new Queue(
    'cashoutSellOrder',
    longRunningConfig(5)
  );
  applyListeners(cashoutSellOrderQueue);

  const pushUserPortfolioInvestmentTotalsToHubSpot = new Queue(
    'pushUserPortfolioInvestmentTotalsToHubSpot',
    longRunningConfig(10)
  );
  applyListeners(pushUserPortfolioInvestmentTotalsToHubSpot);

  const pushInvestorRecapEmailKPIsToHubSpotQueue = new Queue(
    'pushInvestorRecapEmailKPIsToHubSpot',
    longRunningConfig(120)
  );
  applyListeners(pushInvestorRecapEmailKPIsToHubSpotQueue);

  const handleInvestorRecapKPIsBatchQueue = new Queue(
    'handleInvestorRecapKPIsBatch',
    longRunningConfig(120)
  );
  applyListeners(handleInvestorRecapKPIsBatchQueue);

  const emailMonthlyPortfolioReconciliationQueue = new Queue(
    'emailMonthlyPortfolioReconciliation',
    longRunningConfig(30)
  );
  applyListeners(emailMonthlyPortfolioReconciliationQueue);

  const emailEquitySummaryReportQueue = new Queue(
    'emailEquitySummaryReport',
    longRunningConfig(30)
  );
  applyListeners(emailEquitySummaryReportQueue);

  const createQuarterlyStatementsQueue = new Queue(
    'createQuarterlyStatements',
    longRunningConfig(10)
  );
  applyListeners(createQuarterlyStatementsQueue);

  const manageQuarterlyStatementJobsQueue = new Queue(
    'manageQuarterlyStatementJobs',
    longRunningConfig(60)
  );
  applyListeners(manageQuarterlyStatementJobsQueue);

  const reinvestDividendsQueue = new Queue(
    'issueAutoReinvestedDividends',
    longRunningConfig(5)
  );
  applyListeners(reinvestDividendsQueue);

  const sendDividendSummaryEmailQueue = new Queue(
    'sendDividendSummaryEmail',
    defaultConfig
  );
  applyListeners(sendDividendSummaryEmailQueue);

  const syncHubSpotContactsQueue = new Queue(
    'syncHubSpotContacts',
    defaultConfig
  );
  applyListeners(syncHubSpotContactsQueue);

  const syncHubSpotBrContactsQueue = new Queue(
    'syncHubSpotBrContacts',
    defaultConfig
  );
  applyListeners(syncHubSpotBrContactsQueue);

  const syncHubSpotSubAccountsQueue = new Queue(
    'syncHubSpotSubAccounts',
    longRunningConfig(10)
  );
  applyListeners(syncHubSpotSubAccountsQueue);

  const pushInvestmentDataToHubSpotQueue = new Queue(
    'pushInvestmentDataToHubSpot',
    defaultConfig
  );
  applyListeners(pushInvestmentDataToHubSpotQueue);

  const logInvestmentToSlackQueue = new Queue(
    'logInvestmentToSlack',
    defaultConfig
  );
  applyListeners(logInvestmentToSlackQueue);

  const pollRecentSolarEdgeSiteSensorDataQueue = new Queue(
    'pollRecentSolarEdgeSiteSensorData',
    defaultConfig
  );
  applyListeners(pollRecentSolarEdgeSiteSensorDataQueue);

  const pollRecentSolarEdgeSiteProductionDataQueue = new Queue(
    'pollRecentSolarEdgeSiteProductionData',
    defaultConfig
  );
  applyListeners(pollRecentSolarEdgeSiteProductionDataQueue);

  const pollRecentSolarEdgeInverterProductionDataQueue = new Queue(
    'pollRecentSolarEdgeInverterProductionData',
    defaultConfig
  );
  applyListeners(pollRecentSolarEdgeInverterProductionDataQueue);

  const pollRecentGreenAntMeterProductionDataQueue = new Queue(
    'pollRecentGreenAntMeterProductionData',
    defaultConfig
  );
  applyListeners(pollRecentGreenAntMeterProductionDataQueue);

  const pollRecentSunExchangeSiteProductionDataQueue = new Queue(
    'pollRecentSunExchangeSiteProductionData',
    defaultConfig
  );
  applyListeners(pollRecentSunExchangeSiteProductionDataQueue);

  const pollRecentAMMPSensorDataQueue = new Queue(
    'pollRecentAMMPSensorData',
    defaultConfig
  );
  applyListeners(pollRecentAMMPSensorDataQueue);

  const pollRecentAMMPInverterPowerDataQueue = new Queue(
    'pollRecentAMMPInverterPowerData',
    defaultConfig
  );
  applyListeners(pollRecentAMMPInverterPowerDataQueue);

  const pollRecentAMMPInverterGenerationDataQueue = new Queue(
    'pollRecentAMMPInverterGenerationData',
    defaultConfig
  );
  applyListeners(pollRecentAMMPInverterGenerationDataQueue);

  const backfillAMMPSensorDataQueue = new Queue(
    'backfillAMMPSensorData',
    longRunningConfig(10)
  );
  applyListeners(backfillAMMPSensorDataQueue);

  const backfillAMMPInverterPowerQueue = new Queue(
    'backfillAMMPInverterPower',
    longRunningConfig(10)
  );
  applyListeners(backfillAMMPInverterPowerQueue);

  const pollRecentScadaSystemGenerationDataQueue = new Queue(
    'pollRecentScadaSystemGenerationData',
    defaultConfig
  );
  applyListeners(pollRecentScadaSystemGenerationDataQueue);

  const pollRecentScadaSystemInverterPowerDataQueue = new Queue(
    'pollRecentScadaSystemInverterPowerData',
    longRunningConfig(10)
  );
  applyListeners(pollRecentScadaSystemInverterPowerDataQueue);

  const pollRecentScadaSystemSensorDataQueue = new Queue(
    'pollRecentScadaSystemSensorData',
    longRunningConfig(15)
  );
  applyListeners(pollRecentScadaSystemSensorDataQueue);

  const pollScadaSystemAlarmsQueue = new Queue(
    'pollScadaSystemAlarms',
    longRunningConfig(10)
  );
  applyListeners(pollScadaSystemAlarmsQueue);

  const syncScadaSystemAlarmStatusesQueue = new Queue(
    'syncScadaSystemAlarmStatuses',
    defaultConfig
  );
  applyListeners(syncScadaSystemAlarmStatusesQueue);

  const backfillScadaSystemDailyInverterGenerationQueue = new Queue(
    'backfillScadaSystemDailyInverterGeneration',
    defaultConfig
  );
  applyListeners(backfillScadaSystemDailyInverterGenerationQueue);

  const recalculatePlatformIRRQueue = new Queue(
    'recalculatePlatformIRR',
    longRunningConfig(5)
  );
  applyListeners(recalculatePlatformIRRQueue);

  const pollRecentSGDGenerationDataQueue = new Queue(
    'pollRecentSGDGenerationData',
    longRunningConfig(10)
  );
  applyListeners(pollRecentSGDGenerationDataQueue);

  const fetchSGDInverterDataQueue = new Queue(
    'pollRecentSGDInverterData',
    longRunningConfig(10)
  );
  applyListeners(fetchSGDInverterDataQueue);

  const fetchSGDSensorDataQueue = new Queue(
    'pollRecentSGDSensorData',
    longRunningConfig(10)
  );
  applyListeners(fetchSGDSensorDataQueue);

  const pollRecentSMAGenerationDataQueue = new Queue(
    'pollRecentSMAGenerationData',
    longRunningConfig(10)
  );
  applyListeners(pollRecentSMAGenerationDataQueue);

  const fetchSMAInverterDataQueue = new Queue(
    'pollRecentSMAInverterData',
    longRunningConfig(10)
  );
  applyListeners(fetchSMAInverterDataQueue);

  const fetchSMASensorDataQueue = new Queue(
    'pollRecentSMASensorData',
    longRunningConfig(10)
  );
  applyListeners(fetchSMASensorDataQueue);

  const backfillSMAInverterGenerationDataQueue = new Queue(
    'backfillSMAInverterGenerationData',
    longRunningConfig(10)
  );
  applyListeners(backfillSMAInverterGenerationDataQueue);

  const fetchSungrowGenerationDataQueue = new Queue(
    'fetchSungrowGenerationData',
    longRunningConfig(10)
  );
  applyListeners(fetchSungrowGenerationDataQueue);

  const fetchSungrowInverterDataQueue = new Queue(
    'fetchSungrowInverterData',
    longRunningConfig(10)
  );
  applyListeners(fetchSungrowInverterDataQueue);

  const fetchSungrowSensorDataQueue = new Queue(
    'fetchSungrowSensorData',
    longRunningConfig(10)
  );
  applyListeners(fetchSungrowSensorDataQueue);

  const fetchSungrowInverterGenerationDataQueue = new Queue(
    'fetchSungrowInverterGenerationData',
    longRunningConfig(10)
  );
  applyListeners(fetchSungrowInverterGenerationDataQueue);

  const pollRecentSolisGenerationDataQueue = new Queue(
    'pollRecentSolisGenerationData',
    longRunningConfig(10)
  );
  applyListeners(pollRecentSolisGenerationDataQueue);

  const pollRecentSolisInverterSnapshotDataQueue = new Queue(
    'pollRecentSolisInverterSnapshotData',
    longRunningConfig(10)
  );
  applyListeners(pollRecentSolisInverterSnapshotDataQueue);

  const backfillSolisInverterGenerationDataQueue = new Queue(
    'backfillSolisInverterGenerationData',
    longRunningConfig(10)
  );
  applyListeners(backfillSolisInverterGenerationDataQueue);

  const backfillSolarEdgeDailyInverterGenerationDataQueue = new Queue(
    'backfillSolarEdgeDailyInverterGenerationData',
    longRunningConfig(10)
  );
  applyListeners(backfillSolarEdgeDailyInverterGenerationDataQueue);

  const backfillSGDDailyInverterGenerationDataQueue = new Queue(
    'backfillSGDDailyInverterGenerationData',
    longRunningConfig(10)
  );
  applyListeners(backfillSGDDailyInverterGenerationDataQueue);

  const pollRecentSolisSensorDataQueue = new Queue(
    'pollRecentSolisSensorData',
    longRunningConfig(10)
  );
  applyListeners(pollRecentSolisSensorDataQueue);

  const backfillSCADASystemInverterPowerQueue = new Queue(
    'backfillSCADASystemInverterPower',
    longRunningConfig(10)
  );
  applyListeners(backfillSCADASystemInverterPowerQueue);

  const backfillSCADASensorDataQueue = new Queue(
    'backfillSCADASensorData',
    longRunningConfig(10)
  );
  applyListeners(backfillSCADASensorDataQueue);

  const backfillSCADAGenerationQueue = new Queue(
    'backfillSCADAGeneration',
    longRunningConfig(5)
  );
  applyListeners(backfillSCADAGenerationQueue);

  const backfillSGDGenerationQueue = new Queue(
    'backfillSGDGeneration',
    longRunningConfig(5)
  );
  applyListeners(backfillSGDGenerationQueue);

  const backfillSMAGenerationQueue = new Queue(
    'backfillSMAGeneration',
    longRunningConfig(5)
  );
  applyListeners(backfillSMAGenerationQueue);

  const pollRecentSgdInverterGenerationDataQueue = new Queue(
    'pollRecentSGDInverterGenerationData',
    longRunningConfig(10)
  );
  applyListeners(pollRecentSgdInverterGenerationDataQueue);

  const backfillSolisGenerationQueue = new Queue(
    'backfillSolisGeneration',
    longRunningConfig(5)
  );
  applyListeners(backfillSolisGenerationQueue);

  const backfillSolisInverterPowerQueue = new Queue(
    'backfillSolisInverterPower',
    longRunningConfig(10)
  );
  applyListeners(backfillSolisInverterPowerQueue);

  const backfillSolisSensorDataQueue = new Queue(
    'backfillSolisSensorData',
    longRunningConfig(10)
  );
  applyListeners(backfillSolisSensorDataQueue);

  const backfillSolarEdgeInverterPowerQueue = new Queue(
    'backfillSolarEdgeInverterPower',
    defaultConfig
  );
  applyListeners(backfillSolarEdgeInverterPowerQueue);

  const backfillSolarEdgeSensorDataQueue = new Queue(
    'backfillSolarEdgeSensorData',
    defaultConfig
  );
  applyListeners(backfillSolarEdgeSensorDataQueue);

  const backfillSolarEdgeGenerationQueue = new Queue(
    'backfillSolarEdgeGeneration',
    defaultConfig
  );
  applyListeners(backfillSolarEdgeGenerationQueue);

  const backfillProjectExpectedGenerationQueue = new Queue(
    'backfillProjectExpectedGeneration',
    longRunningConfig(10)
  );
  applyListeners(backfillProjectExpectedGenerationQueue);

  const backfillPowerFactorGenerationQueue = new Queue(
    'backfillPowerFactorGeneration',
    defaultConfig
  );
  applyListeners(backfillPowerFactorGenerationQueue);

  const backfillPowerFactorSensorDataQueue = new Queue(
    'backfillPowerFactorSensorData',
    defaultConfig
  );
  applyListeners(backfillPowerFactorSensorDataQueue);

  const backfillPowerFactorInverterDataQueue = new Queue(
    'backfillPowerFactorInverterData',
    defaultConfig
  );
  applyListeners(backfillPowerFactorInverterDataQueue);

  const pollRecentPowerFactorSensorDataQueue = new Queue(
    'pollRecentPowerFactorSensorData',
    defaultConfig
  );
  applyListeners(pollRecentPowerFactorSensorDataQueue);

  const pollRecentPowerFactorProductionDataQueue = new Queue(
    'pollRecentPowerFactorProductionData',
    defaultConfig
  );
  applyListeners(pollRecentPowerFactorProductionDataQueue);

  const pollRecentPowerFactorInverterDataQueue = new Queue(
    'pollRecentPowerFactorInverterData',
    defaultConfig
  );
  applyListeners(pollRecentPowerFactorInverterDataQueue);

  const sendMonitoringPunchlistReportQueue = new Queue(
    'sendMonitoringPunchlistReport',
    longRunningConfig(15)
  );
  applyListeners(sendMonitoringPunchlistReportQueue);

  const runMonthlyOMReportQueue = new Queue(
    'runMonthlyOMReport',
    longRunningConfig(10)
  );
  applyListeners(runMonthlyOMReportQueue);

  const checkMillenniumTrustVerificationStatusesQueue = new Queue(
    'checkMillenniumTrustVerificationStatuses',
    longRunningConfig(10)
  );
  applyListeners(checkMillenniumTrustVerificationStatusesQueue);

  const checkMillenniumTrustAccountCashBalancesQueue = new Queue(
    'checkMillenniumTrustAccountCashBalances',
    longRunningConfig(10)
  );
  applyListeners(checkMillenniumTrustAccountCashBalancesQueue);

  const checkMillenniumTrustAccountStatusesQueue = new Queue(
    'checkMillenniumTrustAccountStatuses',
    longRunningConfig(10)
  );
  applyListeners(checkMillenniumTrustAccountStatusesQueue);

  const sendMTCMonthlyReconciliationReportsQueue = new Queue(
    'sendMTCMonthlyReconciliationReports',
    longRunningConfig(5)
  );
  applyListeners(sendMTCMonthlyReconciliationReportsQueue);

  const handleListEmailExportQueue = new Queue(
    'handleListEmailExport',
    longRunningConfig(10)
  );
  applyListeners(handleListEmailExportQueue);

  const recordUserLoginQueue = new Queue('recordUserLogin', defaultConfig);
  applyListeners(recordUserLoginQueue);

  const checkPreferredReceivingAccountQueue = new Queue(
    'checkPreferredReceivingAccount',
    defaultConfig
  );
  applyListeners(checkPreferredReceivingAccountQueue);

  const createMatchPromosIfEligibleQueue = new Queue(
    'createMatchPromosIfEligible',
    defaultConfig
  );
  applyListeners(createMatchPromosIfEligibleQueue);

  const handlePromoIfApplicableQueue = new Queue(
    'handlePromoIfApplicable',
    defaultConfig
  );
  applyListeners(handlePromoIfApplicableQueue);

  const handleMassPaymentCompleteQueue = new Queue(
    'handleMassPaymentComplete',
    longRunningConfig(10)
  );
  applyListeners(handleMassPaymentCompleteQueue);

  handleMassPaymentCompleteQueue.process(1, async (job, done) => {
    // NOTE: ONLY 1 PROCESS AT A TIME IS IMPORTANT!
    // If two mass payments are processing at the same time and one transfer has dwolla transfers
    // in both mass payments (due to transfer exceeding dwolla transfer amount max), the
    // dwollaTransferIds could be overwritten from the other mass payment and not all dwolla transfer
    // ids will get saved.

    const { body } = job.data;

    const dwollaId = body.resourceId;

    // Step 1) Fetch the mass payment items and all transfers (through dividends) associated with it
    const aPromises = [];
    const maxPerPage = 200;
    const maxPotentialLineItems = 5000;
    let pgCounter = 0;
    const flattenedArray = [];

    while (pgCounter * maxPerPage <= maxPotentialLineItems) {
      const sQuery = `${
        body._links.resource.href
      }/items?limit=${maxPerPage}&offset=${pgCounter * maxPerPage}`;
      aPromises.push(
        DwollaService.fetch(sQuery).then((res) =>
          res.body._embedded.items.forEach((item) => flattenedArray.push(item))
        )
      );
      pgCounter += 1;
    }
    await Promise.all(aPromises);

    const transfers = await Transfer.findAll({
      attributes: ['id', 'dwollaTransferIds', 'userId'], // Need to include userId attribute for sake of creating notification
      include: [
        {
          attributes: [],
          model: Dividend,
          required: true,
          include: [
            {
              attributes: [],
              model: PendingDwollaDividendPayment,
              required: true,
              where: {
                dwollaMassPaymentIds: {
                  [Op.contains]: [dwollaId],
                },
              },
            },
          ],
        },
      ],
    });

    if (flattenedArray.length === 0) {
      sendIssueEmail({
        description: 'No items found within mass payment',
        oData: {
          body,
        },
      });
      done();
      return;
    }

    if (transfers.length === 0) {
      sendIssueEmail({
        description: 'No db transfers found for mass payment',
        oData: {
          body,
        },
      });
      done();
      return;
    }

    // Step 2) Assign all dwollaTransferIds to the transfers using the correlationId
    const transferUpdatePromises = [];

    transfers.forEach((dbTransfer) => {
      // NOTE: We don't want to do a check here to make sure that this dbTransfer doesn't already
      // have dwollaTransferIds because it could have been set already from another mass transfer
      // in the case where it was a dividend that had to be split into two+ transfers and also two mass payments
      flattenedArray
        .filter((item) => item.correlationId === String(dbTransfer.id))
        .forEach((item) => {
          if (item.status === 'success') {
            const dwollaTransferId = item?._links?.transfer?.href
              ?.split('/')
              ?.pop();
            if (!dwollaTransferId) {
              sendIssueEmail({
                description: 'No dwollaTransferId found for mass payment item',
                oData: {
                  item,
                },
              });
              return;
            }
            if (!dbTransfer.dwollaTransferIds) {
              dbTransfer.dwollaTransferIds = [];
            }
            dbTransfer.dwollaTransferIds = [
              ...dbTransfer.dwollaTransferIds,
              dwollaTransferId,
            ];
            TransferService.createTransferNotification(
              dwollaTransferId,
              dbTransfer,
              'initiated'
            );
          } else if (item.status === 'failed') {
            sendIssueEmail({
              description: 'Action Required: An item in a mass payment failed',
              oData: {
                message:
                  "The mass payment was successfully posted to dwolla, but an item within it failed. Other transfers in this mass payment were created but this one was not. The dbTransfer will be deleted. If we want to pay this dividend, diagnose the issue and manually issue the transfer. If we don't then there may be nothing to do.",
                massPayment: body._links.resource.href,
                item: stringifyObject(item),
              },
            });
            Transfer.destroy({
              where: {
                id: parseInt(item.correlationId, 10),
              },
            }).catch((err) => {
              sendIssueEmail({
                description:
                  'Error deleting dbTransfer for associated failed dwolla transfer (mass-payment process)',
                oData: {
                  massPayment: body._links.resource.href,
                  item,
                  err,
                },
              });
            });
          } else {
            sendIssueEmail({
              description: `Unhandled dwolla mass payment item status: ${item.status}`,
              oData: {
                message:
                  "The mass payment was successfully posted to dwolla, but an item within it has an unhandled status (expected 'success' or 'failed'). This shouldn't happen but if it does, figure out how to handle it at that time. If it ends up being successful, we need to save the dwolla transfer id to the dbTransfer. If it isn't, we need to either delete the dbTransfer or manually issue the dwolla transfer and save the id to the dbTransfer.",
                massPayment: body._links.resource.href,
                item,
              },
            });
          }
        });
      transferUpdatePromises.push(dbTransfer.save());
    });
    await Promise.all(transferUpdatePromises);

    // Step 3) Send a mass payment completed slack msg to platform-info
    SlackService.logToSlack({
      title: 'Mass Payment Completed',
      url: `https://dashboard.dwolla.com/mass-payments/${dwollaId}`,
      type: 'platform-info',
      data: [
        {
          label: 'Mass Payment ID',
          value: dwollaId,
        },
        {
          label: 'Total Transfer Amount',
          value: flattenedArray.reduce(
            (acc, curVal) => acc + parseFloat(curVal.amount.value),
            0
          ),
        },
        {
          label: 'Mass Payment Item Count',
          value: flattenedArray.length,
        },
      ],
    });

    done();
  });

  handlePromoIfApplicableQueue.process(maxJobsPerWorker, async (job, done) => {
    try {
      // NOTE: We take investment value as a parameter instead of from the investment
      // record for the sake of blended product investments which individually could be
      // less than the thresholds required.
      const { investmentId, investmentValue } = job.data;
      const investment = await Investment.findByPk(investmentId, {
        attributes: [
          'id',
          'userId',
          'referralId',
          'dividendId',
          'promoRewardCodeId',
        ],
        include: [
          {
            model: User,
            required: true,
            attributes: ['id'],
            include: [
              {
                model: PromoCode,
                attributes: ['id', 'promoType'],
                required: false,
                where: {
                  completedDt: null,
                  expirationDt: {
                    [Op.gt]: new Date(),
                  },
                  promoType: {
                    [Op.in]: [
                      constants.foundersCardMatchPromo.typeName,
                      constants.greenLevelMatchPromo.typeName,
                      constants.goldLevelMatchPromo.typeName,
                      constants.platinumLevelMatchPromo.typeName,
                      constants.holiday2024Promo.typeName,
                    ],
                  },
                },
              },
            ],
          },
        ],
      });
      if (investment?.user?.promoCodes?.length > 0) {
        const foundersCardPromos = investment.user.promoCodes.filter(
          (p) => p.promoType === constants.foundersCardMatchPromo.typeName
        );
        const greenMatchPromos = investment.user.promoCodes.filter(
          (p) => p.promoType === constants.greenLevelMatchPromo.typeName
        );
        const goldMatchPromos = investment.user.promoCodes.filter(
          (p) => p.promoType === constants.goldLevelMatchPromo.typeName
        );
        const platinumMatchPromos = investment.user.promoCodes.filter(
          (p) => p.promoType === constants.platinumLevelMatchPromo.typeName
        );
        const holiday2024Promos = investment.user.promoCodes.filter(
          (p) => p.promoType === constants.holiday2024Promo.typeName
        );
        if (
          foundersCardPromos.length > 0 &&
          investmentValue >= constants.foundersCardMatchPromo.minInvestment
        ) {
          PromoService.handleMatchPromoEligibleInvestment(
            investment,
            foundersCardPromos[0]
          );
        } else if (
          platinumMatchPromos.length > 0 &&
          investmentValue >= constants.platinumLevelMatchPromo.minInvestment
        ) {
          PromoService.handleMatchPromoEligibleInvestment(
            investment,
            platinumMatchPromos[0]
          );
        } else if (
          goldMatchPromos.length > 0 &&
          investmentValue >= constants.goldLevelMatchPromo.minInvestment
        ) {
          PromoService.handleMatchPromoEligibleInvestment(
            investment,
            goldMatchPromos[0]
          );
        } else if (
          greenMatchPromos.length > 0 &&
          investmentValue >= constants.greenLevelMatchPromo.minInvestment
        ) {
          PromoService.handleMatchPromoEligibleInvestment(
            investment,
            greenMatchPromos[0]
          );
        } else if (
          holiday2024Promos.length > 0 &&
          investmentValue >= constants.holiday2024Promo.minInvestment
        ) {
          PromoService.handleMatchPromoEligibleInvestment(
            investment,
            holiday2024Promos[0]
          );
        }
      }
      done();
    } catch (err) {
      const eMsg = 'Error handling promo if applicable job';
      console.error(eMsg, err);
      sendIssueEmail({
        description: eMsg,
        oData: { input: job.data },
      });
      done(err);
    }
  });
  recordUserLoginQueue.process(maxJobsPerWorker, async (job, done) => {
    try {
      const { authId } = job.data;
      if (!authId) {
        done();
        return;
      }
      // Get user from authId
      const [dbUser, brContact] = await Promise.all([
        User.findOne({
          attributes: ['id', 'hubSpotContactId', 'firstName', 'lastName'],
          where: {
            authId,
          },
        }),
        BrContact.findOne({
          attributes: ['id', 'hubSpotContactId', 'firstName', 'lastName'],
          where: {
            authId,
          },
        }),
      ]);

      // check for test user authId before logging
      if (!dbUser && !brContact && authId !== 'auth0|00ublqto59LTBdy8e4x7') {
        SlackService.logToSlack({
          title: `No user found with authId: ${authId}. Failed to record user login.`,
          type: 'platform-error',
          data: [
            {
              label: 'Input',
              value: stringifyObject(job.data),
            },
          ],
        });
        done();
        return;
      }

      // check for test user ipqsRequestId before logging
      if (!dbUser && !brContact) {
        done();
        return;
      }

      const loginDt = new Date();
      if (dbUser?.hubSpotContactId) {
        HubSpotService.updateContact(dbUser.hubSpotContactId, {
          lastLoggedInDt: loginDt,
        }).catch((err) => {
          console.error('Error saving lastLoggedInDt to hubspot contact', err);
        });
      }

      if (job.data.ipqsRequestId) {
        // TODO: Remove all uses of this flow once we confirm the other flow covers the new account creation case
        const { ipqsRequestId, secondaryIP: ipifyIpAddress } = job.data;
        const userLoginObj = await UserLogin.create({
          userId: dbUser?.id,
          brContactId: brContact?.id,
          loginDt,
          ipAddressId: null,
          deviceId: null,
          application: 'Energea', // using auth0's energea.com application name
        });

        const ipqsResponse = await IPQSService.getFingerprint(ipqsRequestId);
        const identificationData = ipqsResponse.data;

        if (identificationData?.success) {
          const {
            device_id: visitorId,
            browser,
            operating_system: os,
            first_seen: firstSeenDt,
            last_seen: lastSeenDt,
            brand,
            model,
            fraud_chance: fraudScore,
          } = identificationData;

          const deviceLabel = `${brand} ${model}`;

          let device = await Device.findOne({
            where: { visitorId },
          });
          if (!device) {
            device = await Device.create({
              firstSeenDt,
              lastSeenDt,
              os,
              browser,
              device: deviceLabel,
              visitorId,
              fraudScore,
              fingerprintSource: 'IPQS',
            }).catch((err) => {
              console.error('Error creating device', err);
            });
          } else {
            const updatedDevice = device;
            updatedDevice.lastSeenDt = lastSeenDt;
            updatedDevice.os = os;
            updatedDevice.browser = browser;
            updatedDevice.device = deviceLabel;
            await updatedDevice.save().catch((err) => {
              console.error('Error updating device', err);
            });
          }
          userLoginObj.deviceId = device?.id;
        }

        const ip = identificationData?.ip_address || ipifyIpAddress;
        if (ip) {
          // Use IP from that response to look up ipAddress record
          let dbIpAddress = await IpAddress.findOne({
            where: { ipAddress: ip },
          });
          if (!dbIpAddress) {
            // Run IPQS to get a fraud score
            const ipqsData = await IPQSService.fetchIPFraudData(ip);
            dbIpAddress = await IpAddress.create({
              ipAddress: ip,
              latitude: ipqsData?.latitude,
              longitude: ipqsData?.longitude,
              city: ipqsData?.city,
              country: ipqsData?.country_code,
              postalCode: ipqsData?.zip_code,
              state: ipqsData?.region,
              fraudScore: ipqsData?.success ? ipqsData.fraud_score : null,
            }).catch((err) => {
              console.error('Error creating ipAddress', err);
            });
          } else if (dbIpAddress.fraudScore === null) {
            const ipqsData = await IPQSService.fetchIPFraudData(ip);
            dbIpAddress.fraudScore = ipqsData?.success
              ? ipqsData.fraud_score
              : null;
            await dbIpAddress.save().catch((err) => {
              console.error('Error updating ipAddress', err);
            });
          }
          userLoginObj.ipAddressId = dbIpAddress?.id;
        } else {
          console.warn(
            'No identification data retrieved from IPQS Fingerprinting and no ipifyIpAddress. Recording login.'
          );
        }
        await userLoginObj.save();
      } else {
        const { ip, userAgent, applicationName, location } = job.data;
        const userLoginObj = await UserLogin.create({
          userId: dbUser?.id,
          brContactId: brContact?.id,
          loginDt,
          ipAddressId: null,
          deviceId: null,
          application: applicationName,
        });
        if (ip) {
          // Use IP from that response to look up ipAddress record
          let dbIpAddress = await IpAddress.findOne({
            where: { ipAddress: ip },
          });
          if (!dbIpAddress) {
            // Run IPQS to get a fraud score
            const ipqsData = await IPQSService.fetchIPFraudData(ip, userAgent);
            dbIpAddress = await IpAddress.create({
              ipAddress: ip,
              latitude: location?.latitude || ipqsData?.latitude,
              longitude: location?.longitude || ipqsData?.longitude,
              city: location?.city || ipqsData?.city,
              country: location?.country || ipqsData?.country_code,
              postalCode: ipqsData?.zip_code,
              state: location?.region || ipqsData?.region,
              fraudScore: ipqsData?.success ? ipqsData.fraud_score : null,
            }).catch((err) => {
              console.error('Error creating ipAddress', err);
            });
          } else if (dbIpAddress.fraudScore === null) {
            const ipqsData = await IPQSService.fetchIPFraudData(ip, userAgent);
            dbIpAddress.fraudScore = ipqsData?.success
              ? ipqsData.fraud_score
              : null;
            await dbIpAddress.save().catch((err) => {
              console.error('Error updating ipAddress', err);
            });
          }
          userLoginObj.ipAddressId = dbIpAddress?.id;
        } else {
          console.warn(
            'No identification data retrieved from IPQS Fingerprinting and no ipifyIpAddress. Recording login.'
          );
        }
        await userLoginObj.save();
      }

      if (dbUser && [76].indexOf(dbUser.id) === -1) {
        UserService.getNAV(dbUser).then(
          (nav) => {
            if (nav >= 500_000) {
              sendPushNotificationToAll({
                content: {
                  title: '🤑🏠 Big roller in the house',
                  body: `${dbUser.firstName} ${dbUser.lastName} (${numeral(
                    nav
                  ).format('$0,0[.]0a')}) just signed in to energea.com.`,
                },
                application: 'Energea KPIs',
              });
            }
          },
          (err) => {
            console.error('Error fetching user NAV for push notification', err);
          }
        );
      }
    } catch (err) {
      console.error(err);
      SlackService.logToSlack({
        type: 'platform-error',
        title: 'Error recording user login data',
        data: [
          { label: 'Error', value: stringifyObject(err) },
          { label: 'Input', value: stringifyObject(job.data) },
        ],
      });
    }
    done();
  });

  sendDividendSummaryEmailQueue.process(maxJobsPerWorker, async (job, done) => {
    try {
      const {
        userId,
        dividendStartDt,
        dividendEndDt,
        monthYearStr,
        previewMode,
        dividendQueryStringMonth,
      } = job.data;
      const [dividendAggregates, reinvestmentTotal, user] = await Promise.all([
        Dividend.findAll({
          attributes: [
            [Sequelize.fn('sum', Sequelize.col('value')), 'totalDividends'],
            'subAccountId',
          ],
          where: {
            userId,
            [Op.and]: [
              {
                date: { [Op.gte]: dividendStartDt },
              },
              {
                date: { [Op.lte]: dividendEndDt },
              },
            ],
          },
          group: ['subAccountId', 'subAccount.id'],
          include: [
            {
              attributes: [
                'id',
                'subAccountTypeId',
                'iraType',
                'millenniumTrustAccountLast4',
                'entrustAccountId',
                'otherAccountId',
              ],
              model: SubAccount,
            },
          ],
        }).catch((err) => {
          console.error(err);
          return null;
        }),

        Investment.findAll({
          attributes: [
            [
              Sequelize.fn('sum', Sequelize.col('investment.value')),
              'totalReinvested',
            ],
          ],
          raw: true,
          where: { cancelledDt: null },
          include: [
            {
              model: Dividend,
              required: true,
              attributes: [],
              where: {
                userId,
                [Op.and]: [
                  {
                    date: { [Op.gte]: dividendStartDt },
                  },
                  {
                    date: { [Op.lte]: dividendEndDt },
                  },
                ],
              },
            },
          ],
        })
          .then((res) => parseFloat(res[0].totalReinvested || 0))
          .catch((err) => {
            console.error(err);
            return null;
          }),

        User.findByPk(userId, {
          attributes: ['firstName', 'lastName', 'email'],
        }).catch((err) => {
          console.error(err);
          return null;
        }),
      ]);

      if (
        user === null ||
        dividendAggregates === null ||
        reinvestmentTotal === null
      ) {
        sendIssueEmail({
          description: 'Error fetching data for dividend summary email',
          oData: {
            input: job.data,
          },
        });
        done();
        return;
      }

      let individualAccount = null;
      const subAccountDividendAggs = [];
      let dividendTotal = 0;
      dividendAggregates.forEach((account) => {
        if (account.subAccountId) {
          subAccountDividendAggs.push(account);
        } else {
          individualAccount = account;
        }
        dividendTotal += parseFloat(account.dataValues.totalDividends || 0);
      });

      const accounts = [];
      if (individualAccount) {
        accounts.push({
          accountName: 'Individual Account',
          value: numeral(
            parseFloat(individualAccount.dataValues.totalDividends || 0)
          ).format('$0,0.00'),
        });
      }
      await Promise.all(
        subAccountDividendAggs.map((subAcctDividendAgg) =>
          subAcctDividendAgg.subAccount.name
            .then((subAcctName) => {
              accounts.push({
                accountName: subAcctName,
                value: numeral(
                  parseFloat(subAcctDividendAgg.dataValues.totalDividends || 0)
                ).format('$0,0.00'),
              });
            })
            .catch((err) => {
              sendIssueEmail({
                description: 'Error getting subaccount name for dividend email',
                oData: {
                  input: job.data,
                  message: "Sending email with sub account name: 'IRA Account'",
                  err,
                },
              });
              accounts.push({
                accountName: 'IRA Account',
                value: parseFloat(
                  subAcctDividendAgg.dataValues.totalDividends || 0
                ),
              });
            })
        )
      );

      let dividendReinvestmentStatus = 'none';
      if (reinvestmentTotal === 0) {
        dividendReinvestmentStatus = 'none';
      } else if (reinvestmentTotal >= dividendTotal) {
        dividendReinvestmentStatus = 'all';
      } else if (reinvestmentTotal > 0 && reinvestmentTotal < dividendTotal) {
        dividendReinvestmentStatus = 'some';
      }

      if (process.env.NODE_ENV === 'production' || previewMode) {
        sendDividendsPaidSummaryEmail({
          email: previewMode ? '<EMAIL>' : user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          monthYear: monthYearStr,
          accounts,
          dividendTotal: numeral(dividendTotal).format('$0,0.00'),
          dividendReinvestmentStatus,
          dividendQueryStringMonth,
          previewMode,
        });
      } else {
        console.log(`send email ${user.firstName} ${user.lastName}`);
      }
    } catch (error) {
      console.error(error);
      sendIssueEmail({
        description: 'Error sending dividend summary email',
        oData: {
          input: job.data,
          error,
        },
      });
    }

    done();
  });

  handleListEmailExportQueue.process(1, async (job, done) => {
    const { filter, sort, list, exportConfig } = job.data;

    if (list === 'user') {
      const filterClauses = UserService.getUserFeedFilter(filter);
      await User.findAndCountAll({
        order: [[sort.field, sort.order]],
        where: filterClauses,
      }).then((records) =>
        ExporterService.emailExport({
          email: exportConfig.email,
          type: 'user',
          records: records.rows,
        })
      );
    } else if (list === 'transfer') {
      const { filterClauses, includes } =
        TransferService.getTransferFeedQueryConfig(filter);
      await Transfer.findAndCountAll({
        order: [[sort.field, sort.order]],
        include: includes,
        where: filterClauses,
      }).then((records) =>
        ExporterService.emailExport({
          email: exportConfig.email,
          type: 'transfer',
          records: records.rows,
        })
      );
    } else if (list === 'investment') {
      const { filterClauses, includes } =
        InvestmentService.getInvestmentFeedQueryConfig(filter, sort);
      const isArthur = aModelers.indexOf(exportConfig.email) > -1;
      filterClauses.cancelledDt = null;
      await Investment.findAndCountAll({
        attributes: [
          'id',
          'completedDt',
          'value',
          'startDt',
          'portfolioId',
          // 'userId',
          'historicalFlg',
          'shares',
          'subAccountId',
          'cancelledDt',
          'dividendId',
          'referralId',
        ],
        order: [[Sequelize.col(sort.field), sort.order]],
        where: filterClauses,
        include: includes,
      }).then((investments) =>
        ExporterService.emailExport({
          email: exportConfig.email,
          type: isArthur ? 'investment-arthur' : 'investment',
          records: investments.rows,
        })
      );
    } else if (list === 'dividend') {
      const { filterClauses, includes } =
        DividendService.getDividendFeedQueryConfig({
          filter,
          sort,
          includeUsers: true,
          includePortfolio: true,
        });
      await Dividend.findAndCountAll({
        attributes: [
          'id',
          'date',
          'value',
          'monthlyPortfolioFinancialActualId',
          'userId',
          // 'subAccountId',
        ],
        order: [[Sequelize.col(sort.field), sort.order]],
        where: filterClauses,
        include: includes,
      }).then((dividends) =>
        ExporterService.emailExport({
          email: exportConfig.email,
          type: 'dividend',
          records: dividends.rows,
        })
      );
    } else if (list === 'shareTransfer') {
      const { filterClauses, includes } =
        ShareTransferService.getShareTransferFeedQueryConfig(filter, sort);
      const isArthur = aModelers.indexOf(exportConfig.email) > -1;
      // if (isArthur) {
      includes.push({
        attributes: ['id'],
        model: Investment,
        required: true,
        where: {
          cancelledDt: null,
        },
      });
      // }
      await ShareTransfer.findAndCountAll({
        order: [[Sequelize.col(sort.field), sort.order]],
        where: filterClauses,
        include: includes,
      }).then((records) =>
        ExporterService.emailExport({
          email: exportConfig.email,
          type: isArthur ? 'shareTransfer-arthur' : 'shareTransfer',
          records: records.rows,
        })
      );
    } else if (list === 'brInvoice') {
      const { filterClauses, includes } = getBrInvoiceFeedQueryConfig(
        filter,
        sort
      );
      await BrInvoice.findAndCountAll({
        // attributes: [],
        order: [[Sequelize.col(sort.field), sort.order]],
        where: filterClauses,
        include: includes,
      }).then((res) =>
        ExporterService.emailExport({
          email: exportConfig.email,
          type: 'brInvoice',
          records: res.rows,
        })
      );
    } else if (list === 'brConsumerUnit') {
      const { filterClauses, includes } = getBrConsumerUnitFeedQueryConfig(
        filter,
        sort
      );
      includes.push({
        model: BrCustomer,
        attributes: ['type', 'cpf', 'cnpj'],
      });
      await BrConsumerUnit.findAndCountAll({
        // attributes: [],
        order: [[Sequelize.col(sort.field), sort.order]],
        where: filterClauses,
        include: includes,
      }).then((res) =>
        ExporterService.emailExport({
          email: exportConfig.email,
          type: 'brConsumerUnit',
          records: res.rows,
        })
      );
    }

    done();
  });

  setMonthlyLtvCacChartDataCache.process(1, async (job, done) => {
    const graphStartDt = constants.ltvCacStartDate;
    const aPromises = [];
    aPromises.push(
      BudgetLineItem.findAll({
        attributes: [
          [fn('sum', col('netTransfer')), 'totalMarketingSpend'],
          [fn('date_trunc', 'month', col('transferDate')), 'month'],
        ],
        where: {
          transferDate: {
            [Op.gte]: graphStartDt,
          },
          name: {
            [Op.not]: MarketingService.excludedMarketingSpendNames,
          },
        },
        group: [fn('date_trunc', 'month', col('transferDate'))],
      })
    );

    aPromises.push(
      Investment.findAll({
        attributes: [
          'id',
          'value',
          'startDt',
          'userId',
          'blendedProductInvestmentId',
        ],
        where: {
          cancelledDt: null,
          userId: {
            [Op.not]: 76,
          },
          startDt: {
            [Op.gte]: constants.ltvCacStartDate,
          },
        },
        order: [['startDt', 'ASC']],
      })
    );

    aPromises.push(
      Investment.findAll({
        attributes: ['startDt', 'value'],
        where: {
          cancelledDt: null,
          referralId: {
            [Op.not]: null,
          },
          startDt: {
            [Op.gte]: constants.ltvCacStartDate,
          },
        },
      })
    );
    const [monthlySpends, investments, referralInvestments] = await Promise.all(
      aPromises
    ).catch((error) => {
      const eMsg = 'Error in the setMonthlyLtvCacChartDataCache promises';
      console.error(eMsg, error);
      sendIssueEmail({
        description: eMsg,
        oData: {
          error,
        },
      });
    });
    const monthMap = {};
    monthlySpends.forEach((monthData) => {
      monthMap[moment.utc(monthData.dataValues.month).format('YYYY-MM')] = {
        marketingSpendTotal: monthData.dataValues.totalMarketingSpend,
        firstInvestmentTotal: 0,
      };
    });
    const investors = {};
    investments.forEach((investment) => {
      if (
        !investors[String(investment.userId)] ||
        (investment.blendedProductInvestmentId &&
          investors[String(investment.userId)] &&
          investors[String(investment.userId)].blendedProductInvestmentId ===
            investment.blendedProductInvestmentId)
      ) {
        const dateStr = moment.utc(investment.startDt).format('YYYY-MM');
        if (monthMap[String(dateStr)]) {
          monthMap[String(dateStr)].firstInvestmentTotal += parseFloat(
            investment.value
          );
        } else {
          monthMap[String(dateStr)] = {
            marketingSpendTotal: 0,
            firstInvestmentTotal: parseFloat(investment.value),
          };
        }
        investors[String(investment.userId)] = {
          id: investment.id,
          blendedProductInvestmentId: investment.blendedProductInvestmentId,
        };
      }
    });

    // Add additional referral program spend
    referralInvestments.forEach((referralInvestment) => {
      const dateStr = moment.utc(referralInvestment.startDt).format('YYYY-MM');
      if (monthMap[String(dateStr)]) {
        monthMap[String(dateStr)].marketingSpendTotal += parseFloat(
          referralInvestment.value
        );
      } else {
        monthMap[String(dateStr)] = {
          marketingSpendTotal: parseFloat(referralInvestment.value),
          firstInvestmentTotal: 0,
        };
      }
    });

    const lfdiLtvPromises = [];
    Object.keys(monthMap).forEach((monthStr) => {
      const endDt = moment(monthStr, 'YYYY-MM').endOf('month');
      lfdiLtvPromises.push(
        MarketingService.calculateAvgLFDI({ endDt }).then((lfdi) => {
          monthMap[String(monthStr)].avgLFDI = lfdi;
        })
      );
      lfdiLtvPromises.push(
        MarketingService.getLTVMultiplier({ endDt }).then((ltvMultiplier) => {
          monthMap[String(monthStr)].ltvMultiplier = ltvMultiplier;
        })
      );
    });

    await Promise.all(lfdiLtvPromises).catch((error) => {
      const eMsg = 'Error in the lfdiLtvPromises functions';
      console.error(eMsg, error);
      sendIssueEmail({
        description: eMsg,
        oData: {
          error,
        },
      });
    });

    const returnObj = Object.keys(monthMap)
      .map((month) => ({
        month,
        marketingSpendTotal: monthMap[String(month)].marketingSpendTotal || 0,
        firstInvestmentTotal: monthMap[String(month)].firstInvestmentTotal || 0,
        avgLFDI: monthMap[String(month)].avgLFDI || 0,
        ltvMultiplier: monthMap[String(month)].ltvMultiplier || 0,
      }))
      .sort((a, b) => (a.month > b.month ? 1 : -1));

    let marketingSpendRunningTotal = 0;
    let firstInvestmentRunningTotal = 0;
    const newData = returnObj.map((month) => {
      marketingSpendRunningTotal += month.marketingSpendTotal;
      firstInvestmentRunningTotal += month.firstInvestmentTotal;
      const updatedMonth = month;
      updatedMonth.marketingSpendRunningTotal = marketingSpendRunningTotal;
      updatedMonth.firstInvestmentRunningTotal = firstInvestmentRunningTotal;
      return updatedMonth;
    });
    const cachedData = await QueryCache.findOne({
      where: { cacheKey: 'getMonthlyLtvCacChartData' },
    });

    if (cachedData) {
      const saveObj = cachedData;
      saveObj.data = newData;
      saveObj.save().catch((err) => {
        console.error('Error saving updated query cache', err);
      });
    } else {
      const newObj = {
        cacheKey: 'getMonthlyLtvCacChartData',
        data: newData,
      };
      await QueryCache.create(newObj).catch((err) => {
        console.error('Error creating new query cache', err);
      });
    }
    done();
  });

  setLtvToCacDataCache.process(4, async (job, done) => {
    const { leadSourceCategoryId } = job.data;
    // const timeKey = `setLtvToCacDataCache-${leadSourceCategoryId}`;
    // console.time(timeKey);
    const leadSourceCategory = await LeadSourceCategory.findByPk(
      leadSourceCategoryId
    );
    if (!leadSourceCategory) {
      console.error(
        'No lead source category found when processing setLtvToCacDataCache job'
      );
      done();
    }
    const aPromises = [];
    aPromises.push(
      leadSourceCategory.getBudgetLineItems({
        attributes: [
          [fn('sum', col('netTransfer')), 'totalMarketingSpend'],
          [fn('date_trunc', 'month', col('transferDate')), 'month'],
        ],
        where: {
          transferDate: {
            [Op.gte]: constants.ltvCacStartDate,
          },
        },
        group: [fn('date_trunc', 'month', col('transferDate'))],
      })
    );

    aPromises.push(
      Investment.findAll({
        attributes: ['startDt', 'value'],
        where: {
          cancelledDt: null,
          referralId: {
            [Op.not]: null,
          },
          startDt: {
            [Op.gte]: constants.ltvCacStartDate,
          },
        },
      })
    );

    aPromises.push(
      leadSourceCategory.getHubSpotLeadSources({
        attributes: ['id'],
        order: [[User, Investment, 'startDt', 'ASC']],
        include: [
          {
            attributes: ['id'],
            required: true,
            model: User,
            where: {
              id: {
                [Op.not]: 76,
              },
            },
            include: [
              {
                attributes: ['startDt', 'value', 'blendedProductInvestmentId'],
                required: true,
                model: Investment,
                where: {
                  cancelledDt: null,
                  startDt: {
                    [Op.gte]: constants.ltvCacStartDate,
                  },
                },
              },
            ],
          },
        ],
      })
    );

    const [monthlySpends, referralInvestments, leadSources] = await Promise.all(
      aPromises
    );
    // console.timeLog(timeKey);

    const monthlySpendMap = {};
    monthlySpends.forEach((monthData) => {
      const monthSpend = parseFloat(
        monthData.dataValues.totalMarketingSpend || 0
      );
      monthlySpendMap[
        moment.utc(monthData.dataValues.month).format('YYYY-MM')
      ] = {
        marketingSpendTotal: monthSpend,
        firstInvestmentTotal: 0,
      };
    });

    leadSources.forEach((leadSource) => {
      leadSource.users.forEach((user) => {
        let userInitialInvestment = null;
        user.investments.forEach((curInvestment) => {
          const curInvestmentVal = parseFloat(curInvestment.value);
          if (
            !userInitialInvestment ||
            (curInvestment.blendedProductInvestmentId &&
              userInitialInvestment &&
              userInitialInvestment.blendedProductInvestmentId ===
                curInvestment.blendedProductInvestmentId)
          ) {
            userInitialInvestment = curInvestment;
            const dateStr = moment.utc(curInvestment.startDt).format('YYYY-MM');
            if (monthlySpendMap[String(dateStr)]) {
              monthlySpendMap[String(dateStr)].firstInvestmentTotal +=
                curInvestmentVal;
            } else {
              monthlySpendMap[String(dateStr)] = {
                marketingSpendTotal: 0,
                firstInvestmentTotal: curInvestmentVal,
              };
            }
          }
        });
      });
    });

    // Add additional referral program spend
    if (leadSourceCategory.id === 8) {
      referralInvestments.forEach((referralInvestment) => {
        const dateStr = moment
          .utc(referralInvestment.startDt)
          .format('YYYY-MM');
        if (monthlySpendMap[String(dateStr)]) {
          monthlySpendMap[String(dateStr)].marketingSpendTotal += parseFloat(
            referralInvestment.value
          );
        } else {
          monthlySpendMap[String(dateStr)] = {
            marketingSpendTotal: parseFloat(referralInvestment.value),
            firstInvestmentTotal: 0,
          };
        }
      });
    }

    const lfdiLtvPromises = [];
    // Combine the LFDI and LTV multiplier calculations into single queries per month
    Object.keys(monthlySpendMap).forEach((monthStr) => {
      const endDt = moment(monthStr, 'YYYY-MM').endOf('month');
      lfdiLtvPromises.push(
        Promise.all([
          MarketingService.calculateAvgLFDI({
            endDt,
            leadSourceCategoryId: leadSourceCategory.id,
          }),
          MarketingService.getLTVMultiplier({
            endDt,
            leadSourceCategoryId: leadSourceCategory.id,
          }),
        ]).then(([lfdi, ltvMult]) => {
          monthlySpendMap[String(monthStr)].avgLFDI = lfdi;
          monthlySpendMap[String(monthStr)].ltvMultiplier = ltvMult;
        })
      );
    });
    // console.timeLog(timeKey);
    await Promise.all(lfdiLtvPromises);
    // console.timeLog(timeKey);

    const monthlyLtvCacChartData = Object.keys(monthlySpendMap)
      .map((month) => ({
        month,
        marketingSpendTotal: monthlySpendMap[String(month)].marketingSpendTotal,
        firstInvestmentTotal:
          monthlySpendMap[String(month)].firstInvestmentTotal,
        avgLFDI: monthlySpendMap[String(month)].avgLFDI,
        ltvMultiplier: monthlySpendMap[String(month)].ltvMultiplier,
      }))
      .sort((a, b) => (a.month > b.month ? 1 : -1));

    let marketingSpendRunningTotal = 0;
    let firstInvestmentRunningTotal = 0;
    const lintedMonthlyLtvCacChartData = monthlyLtvCacChartData.map((month) => {
      marketingSpendRunningTotal += month.marketingSpendTotal;
      firstInvestmentRunningTotal += month.firstInvestmentTotal;
      const updatedMonth = month;
      updatedMonth.marketingSpendRunningTotal = marketingSpendRunningTotal;
      updatedMonth.firstInvestmentRunningTotal = firstInvestmentRunningTotal;
      return updatedMonth;
    });

    // Replace the sequential calls with parallel execution
    let [finalAvgLFDI, finalLtvMultiplier] = await Promise.all([
      MarketingService.calculateAvgLFDI({
        leadSourceCategoryId: leadSourceCategory.id,
      }),
      MarketingService.getLTVMultiplier({
        leadSourceCategoryId: leadSourceCategory.id,
      }),
    ]);
    // console.timeLog(timeKey);

    // Lint 0 denominator values
    if (Number.isNaN(finalAvgLFDI)) {
      finalAvgLFDI = 0;
    }
    if (Number.isNaN(finalLtvMultiplier)) {
      finalLtvMultiplier = 0;
    }

    const ltvToCacData = {
      avgLFDI: finalAvgLFDI,
      ltvMultiplier: finalLtvMultiplier,
      monthlyLtvCacChartData: lintedMonthlyLtvCacChartData,
    };
    const saveObj = leadSourceCategory;
    saveObj.ltvToCacData = ltvToCacData;
    await saveObj.save();
    // console.timeEnd(timeKey);
    done();
  });

  createStripeInvoiceQueue.process(1, async (job, done) => {
    const {
      brCreditCompensationInput,
      billingMonthPortugueseString,
      brBillingCycleId,
    } = job.data;

    const brBillingCycle = await BrBillingCycle.findByPk(brBillingCycleId);

    const brConsumerUnit = await BrConsumerUnit.findOne({
      where: {
        installationCode: brCreditCompensationInput.installationCode,
      },
      include: [
        {
          model: BrCustomer,
          include: [
            {
              model: BrConsortium,
              attributes: ['stripeAccountKey'],
            },
          ],
        },
      ],
    });

    if (!brConsumerUnit) {
      SlackService.logToSlack({
        title: `No consumer unit found with installation code: ${brCreditCompensationInput.installationCode} in createStripeInvoiceQueue`,
        type: 'credit-management',
        data: [
          {
            label: 'Msg',
            value: 'No invoice created',
          },
        ],
      });
      done();
      return;
    }
    const brCreditCompensation = await BrCreditCompensation.create({
      ...brCreditCompensationInput,
      brConsumerUnitId: brConsumerUnit.id,
      brBillingCycleId: brBillingCycle.id,
    }).then(async (res) => {
      const updatedBrConsumerUnit = brConsumerUnit;
      const generationMonthNameShort = moment(
        brBillingCycle.generationMonth,
        'YYYY-MM-DD'
      )
        .format('MMM')
        .toLowerCase();
      updatedBrConsumerUnit[`${generationMonthNameShort}Consumption`] =
        res.grossConsumption;
      await updatedBrConsumerUnit.save();
      return res;
    });
    const brInvoice = await BrInvoice.create({
      ...brCreditCompensationInput.brInvoice,
      brCreditCompensationId: brCreditCompensation.id,
      brBillingCycleId: brBillingCycle.id,
    });

    if (brInvoice.amountDue === 0) {
      SlackService.logToSlack({
        type: 'credit-management',
        url: `${process.env.CMS_HOST}/BrInvoice/${brInvoice.id}`,
        title:
          'Invoice not sent to CU or created in Stripe because amount due = 0.',
        data: [
          {
            label: 'Consumer Unit',
            value: brConsumerUnit.name,
          },
        ],
      });
      done();
      return;
    }

    if (brConsumerUnit.invoiceOfflineFlg) {
      SlackService.logToSlack({
        type: 'credit-management',
        url: `${process.env.CMS_HOST}/BrInvoice/${brInvoice.id}`,
        title:
          'Invoice not sent to CU or created in Stripe. Flagged for offline invoicing.',
        data: [
          {
            label: 'Consumer Unit',
            value: brConsumerUnit.name,
          },
        ],
      });
      done();
      return;
    }

    const eeiValue =
      brCreditCompensation.injectedElectricityPrice *
      brCreditCompensation.injectedElectricity;
    const discountValue =
      brCreditCompensation.injectedElectricityPrice *
      brCreditCompensation.injectedElectricity *
      brCreditCompensation.discountRate;
    const expectedAmountDue = eeiValue - discountValue;

    if (Math.abs(expectedAmountDue - brInvoice.amountDue) >= 0.02) {
      SlackService.logToSlack({
        type: 'credit-management',
        url: `${process.env.CMS_HOST}/BrInvoice/${brInvoice.id}`,
        title: 'Invoice amount due not equal to eeiValue - discount',
        data: [
          {
            label: 'Consumer Unit',
            value: brConsumerUnit.name,
          },
          {
            label: 'Status',
            value: 'Invoice not created in Stripe.',
          },
          {
            label: 'Invoice amount due',
            value: String(brInvoice.amountDue),
          },
          {
            label: 'Expected amount due',
            value: String(eeiValue - discountValue),
          },
        ],
      });
      done();
      return;
    }

    await wait(250); // NOTE: For rate limit reasons, we want to conservatively only process 1 invoice every .25 seconds.

    // NOTE: All uses of this instance of numeral with use portuguese formatting.
    // For example: numeralPortuguese(1.23).format('0.00') === '1,23'
    // NOTE: This must be reset to 'en' once done using, otherwise the dyno will continue to use it.
    numeral.locale('pt-br');
    const invoiceLineItems = [
      {
        description: `Energia Eléctrica Injetada (${numeral(
          brCreditCompensation.injectedElectricity
        ).format('0,0')}kWh)`,
        amount: Math.round(eeiValue * 100),
      },
      {
        description: `Economia (${numeral(
          brCreditCompensation.discountRate
        ).format('0[.]0%')})`,
        amount: -1 * Math.round(discountValue * 100),
      },
    ];
    // NOTE: Must reset numeral locale
    numeral.locale('en');

    const createStripeInvoiceInput = {
      stripeCustomerId: brConsumerUnit.brCustomer.stripeCustomerId,
      description: getStripeInvoiceDescriptionString(
        billingMonthPortugueseString,
        brConsumerUnit.installationCode,
        brCreditCompensation
      ),
      dueDt: moment(brInvoice.dueDt, 'YYYY-MM-DD').toDate().getTime() / 1000,
      invoiceItems: invoiceLineItems,
    };

    // createStripeInvoice includes at least 4-5 API writes
    await createStripeInvoice(
      createStripeInvoiceInput,
      brInvoice,
      brConsumerUnit.brCustomer.brConsortium
    ).then(
      (res) => {},
      (err) => {
        console.error('Error creating stripe invoice', err);
      }
    );

    await createEnergeaConsortiumStatement({
      brBillingCycle,
      brConsumerUnit,
      brCustomer: brConsumerUnit.brCustomer,
      brInvoice,
      brCreditCompensation,
      otherFields: brCreditCompensationInput,
    });

    done();
  });

  createStarkBoletoQueue.process(1, async (job, done) => {
    const { brCreditCompensationInput, brBillingCycleId } = job.data;

    const brBillingCycle = await BrBillingCycle.findByPk(brBillingCycleId);

    const brConsumerUnit = await BrConsumerUnit.findOne({
      where: {
        installationCode: brCreditCompensationInput.installationCode,
      },
      include: [
        {
          model: BrCustomer,
          attributes: ['name', 'cnpj', 'cpf'],
        },
      ],
    });

    if (!brConsumerUnit) {
      SlackService.logToSlack({
        title: `No consumer unit found with installation code: ${brCreditCompensationInput.installationCode} in createStripeInvoiceQueue`,
        type: 'credit-management',
        data: [
          {
            label: 'Msg',
            value: 'No invoice created',
          },
        ],
      });
      done();
      return;
    }
    const brCreditCompensation = await BrCreditCompensation.create({
      ...brCreditCompensationInput,
      brConsumerUnitId: brConsumerUnit.id,
      brBillingCycleId: brBillingCycle.id,
    }).then(async (res) => {
      const updatedBrConsumerUnit = brConsumerUnit;
      const generationMonthNameShort = moment(
        brBillingCycle.generationMonth,
        'YYYY-MM-DD'
      )
        .format('MMM')
        .toLowerCase();
      updatedBrConsumerUnit[`${generationMonthNameShort}Consumption`] =
        res.grossConsumption;
      await updatedBrConsumerUnit.save();
      return res;
    });
    const brInvoice = await BrInvoice.create({
      ...brCreditCompensationInput.brInvoice,
      brCreditCompensationId: brCreditCompensation.id,
      brBillingCycleId: brBillingCycle.id,
    });

    // NOTE: We want to create the statement even if we aren't going to create the boleto.
    await createEnergeaConsortiumStatement({
      brBillingCycle,
      brConsumerUnit,
      brCustomer: brConsumerUnit.brCustomer,
      brInvoice,
      brCreditCompensation,
      otherFields: brCreditCompensationInput,
    }).catch((err) => {
      SlackService.logToSlack({
        type: 'credit-management',
        title: 'Error creating brInvoice bill/statement PDF',
        data: [
          {
            label: 'Error',
            value: stringifyObject(err),
          },
          {
            label: 'Installation #',
            value: brConsumerUnit.installationCode,
          },
        ],
      });
      console.error(err);
    });

    if (brInvoice.amountDue === 0) {
      SlackService.logToSlack({
        type: 'credit-management',
        url: `${process.env.CMS_HOST}/BrInvoice/${brInvoice.id}`,
        title:
          'Invoice not sent to CU or generated by Stark bank because amount due = 0.',
        data: [
          {
            label: 'Consumer Unit',
            value: brConsumerUnit.name,
          },
        ],
      });
      done();
      return;
    }

    if (brConsumerUnit.invoiceOfflineFlg) {
      SlackService.logToSlack({
        type: 'credit-management',
        url: `${process.env.CMS_HOST}/BrInvoice/${brInvoice.id}`,
        title:
          'Invoice not sent to CU or generated by Stark bank. Flagged for offline invoicing.',
        data: [
          {
            label: 'Consumer Unit',
            value: brConsumerUnit.name,
          },
        ],
      });
      done();
      return;
    }

    const eeiValue =
      brCreditCompensation.injectedElectricityPrice *
      brCreditCompensation.injectedElectricity;
    const discountValue =
      brCreditCompensation.injectedElectricityPrice *
      brCreditCompensation.injectedElectricity *
      brCreditCompensation.discountRate;
    const expectedAmountDue = eeiValue - discountValue;

    if (Math.abs(expectedAmountDue - brInvoice.amountDue) >= 0.02) {
      SlackService.logToSlack({
        type: 'credit-management',
        url: `${process.env.CMS_HOST}/BrInvoice/${brInvoice.id}`,
        title: 'Invoice amount due not equal to eeiValue - discount',
        data: [
          {
            label: 'Consumer Unit',
            value: brConsumerUnit.name,
          },
          {
            label: 'Status',
            value: 'Boleto not generated by Stark bank.',
          },
          {
            label: 'Invoice amount due',
            value: String(brInvoice.amountDue),
          },
          {
            label: 'Expected amount due',
            value: String(eeiValue - discountValue),
          },
        ],
      });
      done();
      return;
    }

    await wait(250); // NOTE: For rate limit reasons, we want to conservatively only process 1 invoice every .25 seconds.

    const createStarkBoletoInput = {
      amount: Math.round(brInvoice.amountDue * 100),
      due: moment(brInvoice.dueDt).format('YYYY-MM-DD'),
      fine: 2,
      interest: 1,
      name: brConsumerUnit.brCustomer.name,
      taxId:
        brConsumerUnit?.brCustomer?.cnpj || brConsumerUnit?.brCustomer?.cpf,
      streetLine1: brConsumerUnit.address1,
      streetLine2: brConsumerUnit.address2,
      district: brConsumerUnit.district,
      city: brConsumerUnit.city,
      stateCode: convertBrStateToStateCode(brConsumerUnit.state),
      zipCode: formatBrZipCode(brConsumerUnit.postalCode),
    };

    // createStripeInvoice includes at least 4-5 API writes
    await createStarkBankBoleto(createStarkBoletoInput, brInvoice.id).catch(
      (err) => {
        console.error('Error creating stark bank boleto', err);
      }
    );

    done();
  });

  creditManagementDashboardExportQueue.process(
    maxJobsPerWorker,
    async (job, done) => {
      const { email } = job.data;
      const aPromises = [];

      const includeAdditionalColumns =
        ['<EMAIL>', '<EMAIL>'].indexOf(
          email
        ) > -1;

      const columns = [
        includeAdditionalColumns
          ? {
              label: 'Billing Reference Month',
              writeData: (worksheet, row, column, billingCycle) => {
                worksheet
                  .cell(row, column)
                  .string(billingCycle.billingMonth || '');
              },
            }
          : null,
        {
          label: 'Last Generation Reference Month (Measurement Month)',
          writeData: (worksheet, row, column, billingCycle) => {
            worksheet
              .cell(row, column)
              .string(billingCycle.generationMonth || '');
          },
        },
        includeAdditionalColumns
          ? {
              label: 'Energy Generated (kWh) (CMS)',
              writeData: (worksheet, row, column, billingCycle) => {
                aPromises.push(
                  getBrBillingCycleCMSGeneration(billingCycle.id).then(
                    (res) => {
                      worksheet.cell(row, column).number(res || 0);
                    }
                  )
                );
              },
            }
          : null,
        {
          label: 'Gross Generation (Energia Geracao TUSD kWh)',
          writeData: (worksheet, row, column, billingCycle) => {
            if (billingCycle.tusdInvoices?.length) {
              let sum = 0;
              billingCycle.tusdInvoices.forEach((tusdInvoice) => {
                sum += tusdInvoice.totalAdjustedGeneratedEnergy;
              });
              worksheet.cell(row, column).number(sum);
            }
          },
        },
        {
          label: 'Consumption (Energia Consumo TUSD kWh)',
          writeData: (worksheet, row, column, billingCycle) => {
            if (billingCycle.tusdInvoices?.length) {
              let sum = 0;
              billingCycle.tusdInvoices.forEach((tusdInvoice) => {
                sum += tusdInvoice.totalAdjustedConsumedEnergy;
              });
              worksheet.cell(row, column).number(sum);
            }
          },
        },
        {
          label: 'Net Generation (Energia Injetada TUSD kWh)',
          writeData: (worksheet, row, column, billingCycle) => {
            if (billingCycle.tusdInvoices?.length) {
              let sum = 0;
              billingCycle.tusdInvoices.forEach((tusdInvoice) => {
                const {
                  peakInjectedEnergy,
                  offPeakInjectedEnergy,
                  peakOffPeakFactor,
                } = tusdInvoice;
                sum +=
                  (peakInjectedEnergy || 0) * peakOffPeakFactor +
                  (offPeakInjectedEnergy || 0);
              });
              worksheet.cell(row, column).number(sum);
            }
          },
        },
        {
          label: 'Consumption (Energia Fornecida/Consumida kWh)',
          writeData: (worksheet, row, column, billingCycle) => {
            aPromises.push(
              getBrBillingCycleTotalGrossConsumption(billingCycle.id).then(
                (res) => {
                  if (res || res === 0) {
                    worksheet.cell(row, column).number(res);
                  }
                }
              )
            );
          },
        },
        {
          label: 'Demand Charge (TUSD R$)',
          writeData: (worksheet, row, column, billingCycle) => {
            if (billingCycle.tusdInvoices?.length) {
              let sum = 0;
              billingCycle.tusdInvoices.forEach((tusdInvoice) => {
                sum += tusdInvoice.invoicedAmount || 0;
              });
              worksheet.cell(row, column).number(sum);
            }
          },
        },
        {
          label: 'Credits Created (Injected Energy kWh)',
          writeData: (worksheet, row, column, billingCycle) => {
            if (billingCycle.tusdInvoices?.length) {
              let sum = 0;
              billingCycle.tusdInvoices.forEach((tusdInvoice) => {
                const {
                  peakInjectedEnergy,
                  offPeakInjectedEnergy,
                  peakOffPeakFactor,
                } = tusdInvoice;
                sum +=
                  (peakInjectedEnergy || 0) * peakOffPeakFactor +
                  (offPeakInjectedEnergy || 0);
              });
              worksheet.cell(row, column).number(sum);
            }
          },
        },
        {
          label: 'Credits Redeemed (Energia Compensada kWh)',
          writeData: (worksheet, row, column, billingCycle) => {
            aPromises.push(
              getBrBillingCycleBilledCredits(billingCycle.id).then((res) => {
                if (res || res === 0) {
                  worksheet.cell(row, column).number(res);
                }
              })
            );
          },
        },
        {
          label: 'Weighted Contracted Rate (R$/MWh)',
          writeData: (worksheet, row, column, billingCycle) => {
            aPromises.push(
              Promise.all([
                getBrBillingCycleBilledCredits(billingCycle.id),
                getBrBillingCycleExpectedRevenue(billingCycle.id),
              ]).then(([billedCredits, expectedRevenue]) => {
                if (
                  billedCredits &&
                  (expectedRevenue || expectedRevenue === 0)
                ) {
                  worksheet
                    .cell(row, column)
                    .number(expectedRevenue / (billedCredits / 1000));
                }
              })
            );
          },
        },
        {
          label: 'Number of Consumer Units',
          writeData: (worksheet, row, column, billingCycle) => {
            aPromises.push(
              getBrBillingCycleNumberOfConsumerUnits(billingCycle.id).then(
                (res) => {
                  if (res || res === 0) {
                    worksheet.cell(row, column).number(res);
                  }
                }
              )
            );
          },
        },
        {
          label: 'Project',
          writeData: (worksheet, row, column, billingCycle) => {
            worksheet
              .cell(row, column)
              .string(billingCycle.salesforceProject.name);
          },
        },
        {
          label: 'Start of Month UC Balance (kWh)',
          writeData: (worksheet, row, column, billingCycle) => {
            aPromises.push(
              getPrecedingBrBillingCycle(billingCycle).then(
                (precedingBrBillingCycle) => {
                  if (precedingBrBillingCycle) {
                    getBrBillingCycleUCBalance(precedingBrBillingCycle.id).then(
                      (ucBalance) => {
                        if (ucBalance || ucBalance === 0) {
                          worksheet.cell(row, column).number(ucBalance);
                        }
                      }
                    );
                  }
                }
              )
            );
          },
        },
        {
          label: 'UC Balance (kWh)',
          writeData: (worksheet, row, column, billingCycle) => {
            aPromises.push(
              Promise.all([
                getBrBillingCycleAvailableCredits(billingCycle),
                getBrBillingCycleBilledCredits(billingCycle.id),
              ]).then(([availableCredits, billedCredits]) => {
                if (
                  (availableCredits || availableCredits === 0) &&
                  (billedCredits || billedCredits === 0)
                ) {
                  worksheet
                    .cell(row, column)
                    .number(availableCredits - billedCredits);
                }
              })
            );
          },
        },
        {
          label: 'Adjusted UC Balance (kWh)',
          writeData: (worksheet, row, column, billingCycle) => {
            aPromises.push(
              Promise.all([
                getBrBillingCycleAvailableCredits(billingCycle),
                getBrBillingCycleBilledCredits(billingCycle.id),
                getBrBillingCycleCreditAdjustmentTotal(billingCycle.id),
              ]).then(
                ([availableCredits, billedCredits, creditAdjustmentTotal]) => {
                  if (
                    (availableCredits || availableCredits === 0) &&
                    (billedCredits || billedCredits === 0) &&
                    (creditAdjustmentTotal || creditAdjustmentTotal === 0)
                  ) {
                    worksheet
                      .cell(row, column)
                      .number(
                        availableCredits - billedCredits + creditAdjustmentTotal
                      );
                  }
                }
              )
            );
          },
        },
        includeAdditionalColumns
          ? {
              label: 'Realized Revenue (R$)',
              writeData: (worksheet, row, column, billingCycle) => {
                aPromises.push(
                  getBrBillingCycleCurrentRevenue(billingCycle.id).then(
                    (res) => {
                      if (res || res === 0) {
                        worksheet.cell(row, column).number(res);
                      }
                    }
                  )
                );
              },
            }
          : null,
      ].filter((x) => !!x);

      const brBillingCycles = await BrBillingCycle.findAll({
        include: [
          {
            model: SalesforceProject,
            attributes: ['name'],
            required: true,
          },
          {
            model: TUSDInvoice,
            attributes: [
              'peakGeneratedEnergy',
              'offPeakGeneratedEnergy',
              'peakConsumedEnergy',
              'offPeakConsumedEnergy',
              'peakInjectedEnergy',
              'offPeakInjectedEnergy',
              'peakOffPeakFactor',
              'invoicedAmount',
            ],
          },
        ],
        order: [
          // [models.project, 'portfolioId', 'asc'],
          // [models.project, 'id', 'asc'],
          ['billingMonth', 'desc'],
        ],
      });

      const wb = new xl.Workbook();
      const ws = wb.addWorksheet();

      const boldStyle = {
        font: {
          bold: true,
        },
      };

      columns.forEach((column, index) => {
        ws.cell(1, index + 1)
          .string(column.label)
          .style(boldStyle);
      });
      brBillingCycles.forEach((brBillingCycle, rowIndex) => {
        columns.forEach((column, colIndex) => {
          column.writeData(ws, rowIndex + 2, colIndex + 1, brBillingCycle);
        });
      });

      await Promise.all(aPromises);

      const fileName = `CreditMgmtDownload.xlsx`;
      const filePath = `${__dirname}/${fileName}`;

      const buffer = await wb.writeToBuffer();
      fs.writeFile(filePath, buffer, (err) => {
        if (err) {
          SlackService.logToSlack({
            title: 'Error writing file to disk',
            type: 'platform-error',
            data: [
              {
                label: 'File',
                value: filePath,
              },
              {
                label: 'Error',
                value: stringifyObject(err),
              },
            ],
          });
          return;
        }
        sendExportedCMSList({
          email,
          fileName,
          filePath,
        }).then(() => {
          fs.unlink(filePath, (err1) => {
            if (err1) {
              console.error(
                'Error deleting list export after email sent',
                err1
              );
            }
          });
        });
      });

      done();
    }
  );

  checkPotentialPreventativeMaintenanceTaskQueue.process(
    maxJobsPerWorker,
    async (job, done) => {
      const { omPmpChecklistItemId } = job.data;
      const omPmpChecklistItem = await OMPmpChecklistItem.findByPk(
        omPmpChecklistItemId,
        {
          include: [
            {
              model: OMPmpPeriodicity,
              required: true,
            },
          ],
        }
      );
      const { startDt, omPmpPeriodicity } = omPmpChecklistItem;
      const periodicityData = getOMPreventativeMaintenancePlanPeriodicityData(
        omPmpPeriodicity.name
      );

      const isSaturday = (mmt) => moment(mmt).day() === 6;
      const isSunday = (mmt) => moment(mmt).day() === 0;

      const thisSunday = moment().clone().day(7).endOf('day');
      const nextSunday = moment(thisSunday).add(1, 'week').endOf('day');
      const currentDt = moment(startDt);
      while (currentDt.isSameOrBefore(nextSunday)) {
        if (currentDt.isBetween(thisSunday, nextSunday)) {
          // Fetch info and make ticket
          const ticketStartDt = currentDt.format('YYYY-MM-DD HH:mm:ss');
          // If new ticket is to be scheduled on a weekend, push to next business day
          const lintedTicketStartDt = moment(ticketStartDt);
          if (isSaturday(lintedTicketStartDt)) {
            lintedTicketStartDt.add(2, 'day');
          } else if (isSunday(lintedTicketStartDt)) {
            lintedTicketStartDt.add(1, 'day');
          }
          // eslint-disable-next-line no-await-in-loop
          await Promise.all([
            omPmpChecklistItem.getOmChecklistItem({
              attributes: ['omTicketTypeId', 'defaultTicketDescription'],
            }),
            omPmpChecklistItem.label,
            omPmpChecklistItem.getOmPmp({
              attributes: ['projectId'],
              include: [{ model: Project, attributes: ['name'] }],
            }),
            OMTicket.findOne({
              attributes: ['id'],
              where: {
                startDt: lintedTicketStartDt.format('YYYY-MM-DD HH:mm:ss'),
                omPmpChecklistItemId,
              },
            }),
          ]).then(([omChecklistItem, ticketTitle, omPmp, existingTicket]) => {
            if (existingTicket) {
              console.warn(
                `OMTicket already existing for this omPmpChecklistItem with this start date. Skipping. Ticket ID: ${existingTicket.id}`
              );
              return null;
            }

            return OMTicket.create({
              projectId: omPmp.projectId,
              omTicketTypeId: omChecklistItem.omTicketTypeId,
              startDt: lintedTicketStartDt.format('YYYY-MM-DD HH:mm:ss'),
              title: ticketTitle,
              deviceName: omPmpChecklistItem.name,
              notes: omChecklistItem.defaultTicketDescription || '',
              equipmentItemId: omPmpChecklistItem.defaultEquipmentItemId,
              internalOnlyFlg: false,
              clientNotificationRequiredFlg: false,
              omPmpChecklistItemId,
            });
            // .then((newTicket) => {
            //   SlackService.logToSlack({
            //     title: `New PMP ticket created (#${newTicket.ticketNumber})`,
            //     type: 'om-pmp',
            //     data: [
            //       {
            //         label: 'Project',
            //         value: omPmp.project.name,
            //       },
            //       {
            //         label: 'Ticket Name',
            //         value: newTicket.title,
            //       },
            //     ],
            //   });
            // });
          });
          break;
        } else {
          currentDt.add(periodicityData.amount, periodicityData.unit);
        }
      }
      done();
    }
  );

  sendConsortiumInvoiceEmailQueue.process(1, async (job, done) => {
    const {
      brInvoiceId,
      automaticReminderEmailFlg,
      daysOverdue,
      manualResend,
    } = job.data;
    const brInvoice = await BrInvoice.findByPk(brInvoiceId, {
      attributes: [
        'stripeInvoiceId',
        'id',
        'brBillingCycleId',
        'dueDt',
        'amountDue',
        'invoiceSentDt',
        'energeaStatementAwsObjectKey',
        'starkBankBoletoId',
      ],
      include: [
        {
          attributes: ['id', 'billingMonth'],
          model: BrBillingCycle,
          include: [
            {
              attributes: ['id'],
              model: SalesforceProject,
              include: [
                {
                  model: BrConsortium,
                },
              ],
            },
          ],
        },
        {
          model: BrCreditCompensation,
          attributes: ['id', 'brConsumerUnitId'],
        },
      ],
    });

    if (!brInvoice) {
      SlackService.logToSlack({
        title: `No brInvoice found in sendConsortiumInvoiceEmailQueue for brInvoice id: ${brInvoiceId}`,
        type: 'platform-error',
        data: [
          {
            label: 'Msg',
            value: 'No email sent',
          },
        ],
      });
      done();
      return;
    }

    if (
      brInvoice.invoiceSentDt &&
      !automaticReminderEmailFlg &&
      !manualResend
    ) {
      SlackService.logToSlack({
        title: `BrInvoice id: ${brInvoiceId} already flagged as sent in in sendConsortiumInvoiceEmailQueue`,
        type: 'platform-error',
        data: [
          {
            label: 'Msg',
            value: 'No email sent',
          },
        ],
      });
      done();
      return;
    }

    const consumerUnit =
      await brInvoice.brCreditCompensation?.getBrConsumerUnit({
        attributes: [
          'installationCode',
          'name',
          'salesPersonBrContactId',
          'brSalesPersonId',
        ],
      });

    // Send alert to sales partner/sales person when invoice 15 days overdue as part of automatic email reminder flow
    if (
      daysOverdue === 15 &&
      automaticReminderEmailFlg &&
      consumerUnit.brSalesPersonId
    ) {
      // NOTE: send to linked sales person if applicable. Otherwise, send to admin contact if one is assigned
      let salesPerson = null;
      if (consumerUnit.salesPersonBrContactId) {
        salesPerson = await consumerUnit.getSalesPersonBrContact({
          attributes: ['id', 'firstName', 'lastName', 'email'],
        });
      } else if (consumerUnit.brSalesPersonId) {
        const salesPartner = await consumerUnit.getSalesPerson({
          attributes: ['id', 'adminBrContactId'],
        });
        if (salesPartner?.adminBrContactId) {
          salesPerson = await BrContact.findByPk(
            salesPartner.adminBrContactId,
            {
              attributes: ['id', 'firstName', 'lastName', 'email'],
            }
          );
        }
      }
      if (salesPerson?.email) {
        // NOTE: This must be reset to 'en' once done using, otherwise the dyno will continue to use it.
        numeral.locale('pt-br');
        await sendSalesPartnerCustomerDefaultedEmail({
          firstName: salesPerson.firstName,
          lastName: salesPerson.lastName,
          email: salesPerson.email,
          consumerUnitName: consumerUnit.name,
          invoiceDueDt: moment(brInvoice.dueDt).format('DD/MM/YYYY'),
          amountDue: numeral(brInvoice.amountDue).format('$0,0.00'),
        });
        // NOTE: Must reset numeral locale
        numeral.locale('en');
      }
    }

    if (!brInvoice.brBillingCycle) {
      SlackService.logToSlack({
        title: `No brBillingCycle found in sendConsortiumInvoiceEmailQueue for brInvoice id: ${brInvoiceId}`,
        type: 'platform-error',
        data: [
          {
            label: 'Msg',
            value: 'No email sent',
          },
        ],
      });
      done();
      return;
    }

    if (!brInvoice.brBillingCycle.salesforceProject) {
      SlackService.logToSlack({
        title: `No salesforceProject found in sendConsortiumInvoiceEmailQueue for brInvoice id: ${brInvoiceId}`,
        type: 'platform-error',
        data: [
          {
            label: 'Msg',
            value: 'No email sent',
          },
        ],
      });
      done();
      return;
    }

    if (!brInvoice.brBillingCycle.salesforceProject.brConsortium) {
      SlackService.logToSlack({
        title: `No brConsortium found in sendConsortiumInvoiceEmailQueue for brInvoice id: ${brInvoiceId}`,
        type: 'platform-error',
        data: [
          {
            label: 'Msg',
            value: 'No email sent',
          },
        ],
      });
      done();
      return;
    }

    const customer = await BrCustomer.findOne({
      attributes: ['id', 'stripeCustomerId', 'name'],
      include: [
        {
          attributes: [],
          required: true,
          model: BrConsumerUnit,
          include: [
            {
              attributes: [],
              required: true,
              model: BrCreditCompensation,
              include: [
                {
                  attributes: [],
                  required: true,
                  model: BrInvoice,
                  where: {
                    id: brInvoice.id,
                  },
                },
              ],
            },
          ],
        },
      ],
    });

    if (!customer) {
      SlackService.logToSlack({
        title: `No customer found for stripe invoice ${brInvoice.stripeInvoiceId} in sendConsortiumInvoiceEmailQueue`,
        type: 'platform-error',
        data: [
          {
            label: 'Msg',
            value: 'No email sent',
          },
        ],
      });
      done();
      return;
    }

    const allContactsWithEmails = await BrContact.findAll({
      attributes: ['email', 'id'],
      where: {
        email: {
          [Op.not]: null,
        },
      },
      order: [['id', 'asc']],
      include: [
        {
          model: BrContactsBrCustomer,
          attributes: [],
          where: {
            sendInvoiceFlg: true,
          },
          required: true,
          include: [
            {
              attributes: [],
              required: true,
              model: BrCustomer,
              where: {
                id: customer.id,
              },
            },
          ],
        },
      ],
    });

    const validEmailAddresses = [
      ...new Set(
        allContactsWithEmails
          .map((c) => c.email)
          .filter((e) => validator.isEmail(e))
      ),
    ];
    const toEmail = validEmailAddresses?.[0];
    const ccEmails = validEmailAddresses?.slice(1);

    if (!toEmail) {
      SlackService.logToSlack({
        title: `No billing contact found for customer ${customer.id} with valid email in sendStripeInvoiceEmailsQueue`,
        type: 'platform-error',
        data: [
          {
            label: 'Msg',
            value: 'No email sent',
          },
        ],
      });
      done();
      return;
    }

    const emailData = {
      email:
        process.env.STRIPE_CREDIT_MGMT_FEATURE_TEST === 'true'
          ? '<EMAIL>'
          : toEmail,
      ccEmails:
        process.env.STRIPE_CREDIT_MGMT_FEATURE_TEST === 'true' ? [] : ccEmails,
      referenceMonthStr:
        englishToPortugueseMonthTranslation[
          String(
            moment(brInvoice.brBillingCycle.billingMonth, 'YYYY-MM-DD').format(
              'MMMM'
            )
          )
        ],
      referenceMonthYearStr: moment(
        brInvoice.brBillingCycle.billingMonth,
        'YYYY-MM-DD'
      ).format('MM/YYYY'),
      dueDateStr: `${moment(
        brInvoice.dueDt,
        'YYYY-MM-DD'
      ).date()} de ${englishToPortugueseMonthTranslation[
        String(moment(brInvoice.dueDt, 'YYYY-MM-DD').format('MMMM'))
      ].toLowerCase()}`,
      clientName: customer.name,
      installationNumber: consumerUnit.installationCode,
      attachments: [],
    };

    if (brInvoice.stripeInvoiceId) {
      const consortium =
        brInvoice.brBillingCycle.salesforceProject.brConsortium;
      const stripeInvoice = await retrieveStripeInvoice(
        brInvoice.stripeInvoiceId,
        consortium
      );

      if (!stripeInvoice) {
        SlackService.logToSlack({
          title: `No stripe invoice found for brInvoice ${brInvoice.id} in sendConsortiumInvoiceEmailQueue with stripeInvoiceId`,
          type: 'platform-error',
          data: [
            {
              label: 'Msg',
              value: 'No email sent',
            },
          ],
        });
        done();
        return;
      }

      emailData.showStripeInvoiceButton = !!stripeInvoice.hosted_invoice_url;
      emailData.invoiceLink = stripeInvoice.hosted_invoice_url;

      if (brInvoice.energeaStatementAwsObjectKey) {
        const statementBuffer = await getObjectAsBuffer(
          brInvoice.energeaStatementAwsObjectKey,
          process.env.S3_BUCKET_CREDIT_MGMT
        );
        emailData.attachments.push({
          content: statementBuffer.toString('base64'),
          filename: `fatura-${brInvoice.id}.pdf`,
          type: 'text/pdf',
          disposition: 'attachment',
        });
      }
    } else if (brInvoice.starkBankBoletoId) {
      const boleto = await getStarkBankBoleto(brInvoice.id);
      emailData.includeBoletoBarcode = !!boleto.line;
      emailData.barcode = boleto.line;

      const boletoBuffer = await getStarkBankBoletoPdfBuffer(brInvoice.id);

      let finalPdfBuffer = null;
      if (brInvoice.energeaStatementAwsObjectKey) {
        const statementBuffer = await getObjectAsBuffer(
          brInvoice.energeaStatementAwsObjectKey,
          process.env.S3_BUCKET_CREDIT_MGMT
        );
        // NOTE: await is needed on the next line
        finalPdfBuffer = await merge([statementBuffer, boletoBuffer]);
      } else {
        finalPdfBuffer = boletoBuffer;
      }

      emailData.attachments.push({
        content: finalPdfBuffer.toString('base64'),
        filename: `fatura-${brInvoice.id}.pdf`,
        type: 'text/pdf',
        disposition: 'attachment',
      });
    } else if (brInvoice.energeaStatementAwsObjectKey) {
      const statementBuffer = await getObjectAsBuffer(
        brInvoice.energeaStatementAwsObjectKey,
        process.env.S3_BUCKET_CREDIT_MGMT
      );
      emailData.attachments.push({
        content: statementBuffer.toString('base64'),
        filename: `fatura-${brInvoice.id}.pdf`,
        type: 'text/pdf',
        disposition: 'attachment',
      });
    }

    // const whatsAppData = {
    //   toPhoneNumber: '4134784672',
    //   clientName: emailData.clientName,
    //   installationNumber: emailData.installationNumber,
    //   referenceMonthYearStr: emailData.referenceMonthYearStr,
    //   dueDateStr: emailData.dueDateStr,
    //   downloadUrl: `${process.env.ENERGEA_BR_HOST}?download=1&fatura=${brInvoice.id}&key=${brInvoice.hash}`,
    // };
    // sendInvoiceReadyWhatsAppMessage(whatsAppData);

    if (daysOverdue === 0) {
      emailData.emailType = 'dueToday';
      await sendCommunitySolarOffTakerInvoiceEmail(emailData);
    } else if (daysOverdue === 1) {
      emailData.emailType = 'overdueWarning1';
      await sendCommunitySolarOffTakerInvoiceEmail(emailData);
    } else if (daysOverdue === 7) {
      emailData.emailType = 'overdueWarning2';
      await sendCommunitySolarOffTakerInvoiceEmail(emailData);
    } else if (daysOverdue === 15) {
      emailData.emailType = 'overdueWarning3';
      await sendCommunitySolarOffTakerInvoiceEmail(emailData);
    } else {
      emailData.emailType = 'invoiceCreated';
      await sendCommunitySolarOffTakerInvoiceEmail(emailData);
    }

    if (!automaticReminderEmailFlg && !manualResend) {
      const updatedBrInvoice = brInvoice;
      updatedBrInvoice.invoiceSentDt = new Date();
      await updatedBrInvoice.save();
    }

    done();
  });

  updateInvestorReturns.process(2, async (job, done) => {
    const investors = await User.findAll({
      attributes: ['id'],
      include: [
        {
          attributes: [],
          required: true,
          model: Investment,
          where: {
            cancelledDt: null,
          },
        },
      ],
    });
    const aCreateObjects = [];
    const aPromises = investors.map((investor) => {
      const aPromises2 = [
        UserService.firstInvestmentDt(investor),
        UserService.navBasedIRR({ user: investor, grossOfPenaltiesFlg: true }),
      ];
      return Promise.all(aPromises2).then((resp) => {
        const [firstInvestmentDt, irr] = resp;
        const createObject = { firstInvestmentDt, irr, userId: investor.id };
        aCreateObjects.push(createObject);
      });
    });
    await Promise.all(aPromises);
    await InvestorReturn.destroy({
      where: {},
      truncate: true,
    });
    await InvestorReturn.bulkCreate(aCreateObjects);
    done();
  });
  recalculateSharePriceQueue.process(2, async (job, done) => {
    const { portfolioId } = job.data;
    try {
      const portfolio = await Portfolio.findByPk(portfolioId, {
        attributes: ['id', 'subtitle'],
      });
      // eslint-disable-next-line no-await-in-loop
      const latestActual = await portfolio
        .getMonthlyPortfolioFinancialActuals({
          attributes: ['id', 'portfolioId', 'sharePrice'],
          order: [['effectiveDt', 'DESC']],
        })
        .then((actuals) => actuals[0]);
      if (!latestActual) {
        console.log(`Skipped ${portfolio.name} because there are no actuals`);
      } else {
        const oldSharePrice = parseFloat(latestActual.sharePrice);
        // eslint-disable-next-line no-await-in-loop
        const sharePriceRecalculation =
          await PortfolioService.recalculateSharePrice({
            monthlyPortfolioFinancialActualId: latestActual.dataValues.id,
          });
        if (
          sharePriceRecalculation &&
          typeof sharePriceRecalculation === 'number' &&
          sharePriceRecalculation !== oldSharePrice
        ) {
          const saveObj = latestActual;
          saveObj.sharePrice = sharePriceRecalculation;
          // eslint-disable-next-line no-await-in-loop
          await saveObj.save().then(
            async (returnObj) => {
              const portfolioSharePriceObj = {
                sharePrice: sharePriceRecalculation,
                date: returnObj.dataValues.updatedAt,
                portfolioId: portfolio.id,
              };
              await PortfolioSharePrice.create(portfolioSharePriceObj).catch(
                (e) => {
                  SlackService.logToSlack({
                    title:
                      'Error saving the new share price to the portfolioSharePrices table.',
                    data: [
                      {
                        label: 'Errors',
                        value: e,
                      },
                    ],
                    type: 'platform-error',
                  });
                }
              );
              const msg = `Share price for ${
                portfolio.subtitle
              } has successfully updated from ${numeral(oldSharePrice).format(
                '$0,0.000000'
              )} to ${numeral(sharePriceRecalculation).format('$0,0.000000')}`;
              console.log(msg);
              SlackService.logToSlack({
                title: msg,
                data: [
                  {
                    label: 'Price Delta',
                    value: sharePriceRecalculation - oldSharePrice,
                  },
                  {
                    label: 'Percentage Delta',
                    value: numeral(
                      (sharePriceRecalculation - oldSharePrice) / oldSharePrice
                    ).format('%00.0000'),
                  },
                ],
                type: 'platform-info',
              });
              await PortfolioService.recalculateProjectedSharePrices(
                portfolio.id
              ).then(
                () =>
                  SlackService.logToSlack({
                    title: `Successfully updated projected share prices for ${portfolio.subtitle}`,
                    data: [],
                    type: 'platform-info',
                  }),
                (e) =>
                  SlackService.logToSlack({
                    title: 'Error updating projected share prices',
                    data: [
                      {
                        label: 'Error',
                        value: e,
                      },
                    ],
                    type: 'platform-error',
                  })
              );
            },
            (e) => {
              const msg = `Error updating share price for ${portfolio.subtitle}`;
              console.error(msg, e);
              SlackService.logToSlack({
                title: msg,
                data: [
                  {
                    label: 'Errors',
                    value: e,
                  },
                ],
                type: 'platform-error',
              });
            }
          );
        }
      }
    } catch (err) {
      done();
      SlackService.logToSlack({
        title: 'Error recalculating portfolio share price',
        type: 'platform-error',
        data: [
          { label: 'Portfolio ID', value: portfolioId },
          {
            label: 'Error',
            value: stringifyObject(err),
          },
        ],
      });
      return;
    }
    done();
  });

  recalculateProjectedSharePriceQueue.process(5, async (job, done) => {
    // This is an example job that just slowly reports on progress
    // while doing no work. Replace this with your own job logic.
    console.log(
      `Processing Projected Share Price Recalculation for ${job.data.subtitle}`
    );
    await PortfolioService.recalculateProjectedSharePrices(
      job.data.portfolioId
    ).then(
      () =>
        SlackService.logToSlack({
          title: `Successfully updated projected share prices for ${job.data.subtitle}`,
          data: [],
          type: 'platform-info',
        }),
      (e) =>
        SlackService.logToSlack({
          title: 'Error updating projected share prices',
          data: [
            {
              label: 'Error',
              value: stringifyObject(e),
            },
          ],
          type: 'platform-error',
        })
    );

    done();
  });

  recalculatePlatformIRRQueue.process(1, async (job, done) => {
    const aPromises = [];
    // calculate platform irr
    aPromises.push(
      calculatePlatformNavBasedIRR().then((irr) => {
        HistoricalPerformancePoint.create({
          portfolioId: null,
          navBasedIRR: irr,
          date: new Date(),
        });
      })
    );

    // calculate portfolio IRRs
    [1, 5, 7, 9].forEach((portfolioId) => {
      aPromises.push(
        PortfolioService.getCurrentIRR({
          portfolioId,
          grossOfPenaltiesFlg: true,
        }).then((irr) => {
          HistoricalPerformancePoint.create({
            portfolioId,
            navBasedIRR: irr,
            date: new Date(),
          });
        })
      );
    });

    await Promise.all(aPromises);

    done();
  });

  backfillSolarEdgeGenerationQueue.process(1, async (job, done) => {
    const { solarEdgeSiteId, startDt, endDt } = job.data;

    try {
      const solarEdgeSite = await SolarEdgeSite.findByPk(solarEdgeSiteId);
      await SolarEdgeService.fetchProductionDataRange(
        solarEdgeSite,
        startDt,
        endDt
      ).then(async (historicalRecords) => {
        // Save each record synchronously because we don't want duplicate records so need to know what already exists
        for (let index = 0; index < historicalRecords.length; index += 1) {
          const record = historicalRecords[parseInt(index, 10)];
          // eslint-disable-next-line no-await-in-loop
          await SolarEdgeService.saveProductionPeriod(record, solarEdgeSiteId);
        }
      });
    } catch (err) {
      SlackService.logToSlack({
        title: `Error backfilling SolarEdge generation data`,
        type: 'monitoring',
        data: [
          {
            label: 'Details',
            value: stringifyObject({
              solarEdgeSiteId,
              startDt,
              endDt,
            }),
          },
          {
            label: 'Error',
            value: stringifyObject(err),
          },
        ],
      });
      done(
        new Error(`Error backfilling SolarEdge generation data. Err: ${err}`)
      );
      return null;
    }

    SlackService.logToSlack({
      title: `Completed SolarEdge generation backfill`,
      type: 'monitoring',
      data: [
        {
          label: 'Details',
          value: stringifyObject({
            solarEdgeSiteId,
            startDt,
            endDt,
          }),
        },
      ],
    });
    done();
    return null;
  });

  backfillSolarEdgeInverterPowerQueue.process(1, async (job, done) => {
    const { solarEdgeSiteId, inverterId, startDt, endDt } = job.data;

    try {
      const [inverter, solarEdgeSite] = await Promise.all([
        Inverter.findByPk(inverterId),
        SolarEdgeSite.findByPk(solarEdgeSiteId),
      ]);

      await SolarEdgeService.fetchInverterPowerDataRange(
        solarEdgeSite,
        inverter.dataKey,
        startDt,
        endDt
      ).then((historicalRecords) =>
        historicalRecords.map((record) => {
          const newRecord = record;
          newRecord.inverterId = inverterId;
          return InverterProductionPeriod.findOne({
            where: {
              [Op.and]: [
                {
                  inverterId,
                },
                {
                  periodStartDt: newRecord.periodStartDt,
                },
              ],
            },
          }).then((existingProdPeriod) => {
            if (existingProdPeriod) {
              const updatedProdPeriod = existingProdPeriod;
              updatedProdPeriod.production = newRecord.production;
              updatedProdPeriod.unit = newRecord.unit;
              updatedProdPeriod.timeUnit = newRecord.timeUnit;
              return updatedProdPeriod.save();
            }
            return InverterProductionPeriod.create(newRecord);
          });
        })
      );
    } catch (err) {
      SlackService.logToSlack({
        title: `Error backfilling SolarEdge inverter data`,
        type: 'monitoring',
        data: [
          {
            label: 'Details',
            value: stringifyObject({
              solarEdgeSiteId,
              inverterId,
              startDt,
              endDt,
            }),
          },
          {
            label: 'Error',
            value: stringifyObject(err),
          },
        ],
      });
      done(new Error(`Error backfilling SolarEdge inverter data. Err: ${err}`));
      return null;
    }

    SlackService.logToSlack({
      title: `Completed SolarEdge inverter backfill`,
      type: 'monitoring',
      data: [
        {
          label: 'Details',
          value: stringifyObject({
            solarEdgeSiteId,
            inverterId,
            startDt,
            endDt,
          }),
        },
      ],
    });
    done();
    return null;
  });

  backfillSolarEdgeSensorDataQueue.process(1, async (job, done) => {
    const { solarEdgeSiteId, startDt, endDt } = job.data;

    try {
      const solarEdgeSite = await SolarEdgeSite.findByPk(solarEdgeSiteId, {
        include: [
          {
            model: Sensor,
          },
        ],
      });
      await SolarEdgeService.fetchSensorData(
        solarEdgeSite,
        solarEdgeSite.sensors,
        startDt,
        endDt
      ).then(async (historicalRecords) => {
        const records = [];
        // Save each record synchronously because we don't want duplicate records so need to know what already exists
        for (let index = 0; index < historicalRecords.length; index += 1) {
          const record = historicalRecords[parseInt(index, 10)];
          // eslint-disable-next-line no-await-in-loop
          records.push(
            // eslint-disable-next-line no-await-in-loop
            await SolarEdgeService.saveSensorDataPeriod(record, solarEdgeSiteId)
          );
        }
        return records;
      });
    } catch (err) {
      SlackService.logToSlack({
        title: `Error backfilling SolarEdge sensor data`,
        type: 'monitoring',
        data: [
          {
            label: 'Details',
            value: stringifyObject({
              solarEdgeSiteId,
              startDt,
              endDt,
            }),
          },
          {
            label: 'Error',
            value: stringifyObject(err),
          },
        ],
      });
      done(new Error(`Error backfilling SolarEdge sensor data. Err: ${err}`));
      return null;
    }

    SlackService.logToSlack({
      title: `Completed SolarEdge sensor backfill`,
      type: 'monitoring',
      data: [
        {
          label: 'Details',
          value: stringifyObject({
            solarEdgeSiteId,
            startDt,
            endDt,
          }),
        },
      ],
    });
    done();
    return null;
  });

  backfillPowerFactorGenerationQueue.process(1, async (job, done) => {
    const { powerFactorSystemId, startDt, endDt } = job.data;

    try {
      await PowerFactorService.fetchProductionDataRange(
        powerFactorSystemId,
        startDt,
        endDt
      ).then(async (historicalRecords) => {
        // Save each record synchronously because we don't want duplicate records so need to know what already exists
        for (let index = 0; index < historicalRecords.length; index += 1) {
          const record = historicalRecords[parseInt(index, 10)];
          // eslint-disable-next-line no-await-in-loop
          await PowerFactorService.saveProductionPeriod(
            record,
            powerFactorSystemId
          );
        }
      });
    } catch (err) {
      SlackService.logToSlack({
        title: `Error backfilling PowerFactor generation data`,
        type: 'monitoring',
        data: [
          {
            label: 'Details',
            value: stringifyObject({
              powerFactorSystemId,
              startDt,
              endDt,
            }),
          },
          {
            label: 'Error',
            value: stringifyObject(err),
          },
        ],
      });
      done(
        new Error(`Error backfilling PowerFactor generation data. Err: ${err}`)
      );
      return null;
    }

    SlackService.logToSlack({
      title: `Completed PowerFactor generation backfill`,
      type: 'monitoring',
      data: [
        {
          label: 'Details',
          value: stringifyObject({
            powerFactorSystemId,
            startDt,
            endDt,
          }),
        },
      ],
    });
    done();
    return null;
  });

  backfillPowerFactorSensorDataQueue.process(1, async (job, done) => {
    const { powerFactorSystemId, sensorId, startDt, endDt } = job.data;

    try {
      await PowerFactorService.fetchSensorData(
        powerFactorSystemId,
        sensorId,
        startDt,
        endDt
      ).then(async (historicalRecords) => {
        // eslint-disable-next-line no-await-in-loop
        await SensorDataPeriod.bulkCreate(historicalRecords, {
          // updateOnDuplicate: ['value', 'timeUnit'],
          ignoreDuplicates: true,
        });
      });
    } catch (err) {
      SlackService.logToSlack({
        title: `Error backfilling PowerFactor sensor data`,
        type: 'monitoring',
        data: [
          {
            label: 'Details',
            value: stringifyObject({
              powerFactorSystemId,
              sensorId,
              startDt,
              endDt,
            }),
          },
          {
            label: 'Error',
            value: stringifyObject(err),
          },
        ],
      });
      done(new Error(`Error backfilling PowerFactor sensor data. Err: ${err}`));
      return null;
    }

    SlackService.logToSlack({
      title: `Completed PowerFactor sensor backfill`,
      type: 'monitoring',
      data: [
        {
          label: 'Details',
          value: stringifyObject({
            powerFactorSystemId,
            sensorId,
            startDt,
            endDt,
          }),
        },
      ],
    });
    done();
    return null;
  });

  backfillPowerFactorInverterDataQueue.process(
    maxJobsPerWorker,
    async (job, done) => {
      const { powerFactorSystemId, inverterId, startDt, endDt } = job.data;
      try {
        await PowerFactorService.fetchInverterPowerData(
          inverterId,
          startDt,
          endDt
        ).then(async (historicalRecords) => {
          // eslint-disable-next-line no-await-in-loop
          await InverterProductionPeriod.bulkCreate(historicalRecords, {
            // updateOnDuplicate: ['value', 'timeUnit'],
            ignoreDuplicates: true,
          });
        });
      } catch (err) {
        SlackService.logToSlack({
          title: `Error backfilling PowerFactor inverter data`,
          type: 'monitoring',
          data: [
            {
              label: 'Details',
              value: stringifyObject({
                powerFactorSystemId,
                startDt,
                endDt,
              }),
            },
            {
              label: 'Error',
              value: stringifyObject(err),
            },
          ],
        });
        done(
          new Error(`Error backfilling PowerFactor inverter data. Err: ${err}`)
        );
        return null;
      }

      SlackService.logToSlack({
        title: `Completed PowerFactor inverter backfill`,
        type: 'monitoring',
        data: [
          {
            label: 'Details',
            value: stringifyObject({
              powerFactorSystemId,
              startDt,
              endDt,
            }),
          },
        ],
      });
      done();
      return null;
    }
  );

  backfillProjectExpectedGenerationQueue.process(2, async (job, done) => {
    const { projectId, endDt, logToSlack } = job.data;
    let { startDt } = job.data;
    const project = await Project.findByPk(projectId, {
      include: [
        {
          model: ExpectedProductionPeriod,
          required: false,
          where: {
            periodStartDt: {
              [Op.and]: [{ [Op.gte]: startDt }, { [Op.lte]: endDt }],
            },
          },
        },
      ],
    });
    const statusData = {};
    try {
      if (!project) {
        throw new Error(
          `Error backfilling expected generation. No project found with id : ${projectId}.`
        );
      }
      // NOTE: limit startDt to a minimum of actualCOD
      if (
        project.actualCOD &&
        new Date(startDt) < new Date(project.actualCOD)
      ) {
        startDt = `${project.actualCOD} 00:00:00`;
      }

      const timezone = getProjectTimezone(project);
      // Step 1 : retrieve sensor data for timePeriod
      const aPromises1 = [];
      aPromises1.push(
        getProjectSensors(project, null, {
          sensorTypeId: {
            [Op.in]:
              project.expectedIrradianceSensorTypeIds?.length > 0
                ? project.expectedIrradianceSensorTypeIds
                : [2], // Filter to the project's specified irradiance type (default to inclined irradiance)
          },
        })
      );

      aPromises1.push(
        getProjectSensors(project, null, {
          sensorTypeId: {
            [Op.in]: [5], // NOTE: Filter to module temperature sensor
          },
        })
      );

      const [irradianceSensors, moduleTemperatureSensors] = await Promise.all(
        aPromises1
      );

      if (!irradianceSensors?.[0]) {
        console.warn(
          `No irradiance sensors found for project #${project.id}. Continuing expected generation calculations for the sake of inverter availability.`
        );
      }

      if (!moduleTemperatureSensors?.[0]) {
        console.warn(
          `No module temp sensors found for project #${project.id}. Ignoring module temperature in these calculations.`
        );
      }
      const map = {};
      const inverterProdPeriodPromise = getProjectInverters(
        project,
        ['id'],
        null,
        [
          {
            required: false,
            model: InverterProductionPeriod,
            attributes: ['id', 'periodStartDt', 'production', 'timeUnit'],
            where: {
              periodStartDt: {
                // Expand range to make sure we cover all data points
                [Op.gte]: startDt,
                [Op.lt]: endDt,
              },
              production: { [Op.gt]: 0 },
            },
            order: [['periodStartDt', 'ASC']],
          },
        ]
      ).catch((e) =>
        console.error('Error setting inverterProdPeriodPromise', e)
      );

      // TODO: iterate through these
      const sensorTypeId = irradianceSensors?.[0]?.sensorTypeId;
      const irradianceSensorPromise =
        irradianceSensors?.[0]?.getSensorDataPeriods({
          attributes: ['id', 'periodStartDt', 'value', 'timeUnit'],
          where: {
            periodStartDt: {
              // Expand range to make sure we cover all data points
              [Op.gte]: startDt,
              [Op.lt]: endDt,
            },
          },
          order: [['periodStartDt', 'ASC']],
        });
      const moduleTemperaturePromise =
        moduleTemperatureSensors?.[0]?.getSensorDataPeriods({
          attributes: ['id', 'periodStartDt', 'value'],
          where: {
            periodStartDt: {
              // Expand range to make sure we cover all data points
              [Op.gte]: startDt,
              [Op.lt]: endDt,
            },
          },
          order: [['periodStartDt', 'ASC']],
        });

      const fetchPromises = [
        inverterProdPeriodPromise,
        irradianceSensorPromise,
        moduleTemperaturePromise,
      ];
      const [inverterProdPeriodData, irradianceData, moduleTemperatureData] =
        await Promise.all(fetchPromises);
      irradianceData?.forEach((point) => {
        const sDate = moment(point.periodStartDt).format('YYYY-MM-DD');
        const projectPOAIrradianceFactor =
          sensorTypeId === 3 &&
          getPOAIrradianceFactor(project, new Date(sDate));
        const lintedPoint = {
          periodStartDt: point.periodStartDt,
          value:
            sensorTypeId === 3 && projectPOAIrradianceFactor
              ? point.value * projectPOAIrradianceFactor
              : point.value,
        };
        if (map[String(sDate)]) {
          const prevPoint =
            map[String(sDate)].points[map[String(sDate)].points.length - 1];
          const { sunrise, sunset } = map[String(sDate)];
          // NOTE: default to make the max time-span between points be 15 minutes. We should use the "timeUnit" data point for this in the future
          const defaultTime = 0.25;
          const timeUnit = parseInt(point.timeUnit, 10) / 60;
          const maxTime =
            timeUnit > 0 && timeUnit <= 1 ? timeUnit : defaultTime;
          const hours = Math.min(
            maxTime,
            moment(point.periodStartDt).diff(
              moment(prevPoint.periodStartDt),
              'hours',
              true
            ) || 0
          );

          const lintedStartDt = moment.tz(
            point.periodStartDt,
            'YYYY-MM-DD HH:mm:ss',
            timezone
          );
          const lintedPrevPointStartDt = moment.tz(
            prevPoint.periodStartDt,
            'YYYY-MM-DD HH:mm:ss',
            timezone
          );
          if (lintedStartDt > sunrise && lintedPrevPointStartDt < sunset) {
            // NOTE: There seem to be some nighttime values ~0-10 for some sites so limit to sunrise/sunset times
            map[String(sDate)].irradianceCoverageMinutes += hours * 60;
            map[String(sDate)].totalIrradiance +=
              hours * Math.max(prevPoint?.value || 0, 0);
            map[String(sDate)].points.push(lintedPoint);
          }
        } else {
          map[String(sDate)] = {
            totalIrradiance: 0,
            irradianceCoverageMinutes: 0,
            sunrise: getProjectSunrise(
              project,
              moment.tz(sDate, timezone).toDate()
            ),
            sunset: getProjectSunset(
              project,
              moment.tz(sDate, timezone).toDate()
            ),
            points: [lintedPoint],
            moduleTempPoints: [],
          };
        }
      });

      const sgdSystemId = await project
        .getSgdSystem({ attributes: ['id'] })
        .then(
          (resp) => resp?.id,
          (e) => {
            console.error('Error retrieving SGD system', e);
            return false;
          }
        );

      moduleTemperatureData?.forEach((point) => {
        const sunriseBuffer = 30; // NOTE: set this in case you want to ignore the first and last moments of sunlight
        const sunsetBuffer = 30; // NOTE: set this in case you want to ignore the first and last moments of sunlight

        const sDate = moment(point.periodStartDt).format('YYYY-MM-DD');
        const lintedPeriodStartDt = moment.tz(
          point.periodStartDt,
          'YYYY-MM-DD HH:mm:ss',
          timezone
        );

        const lintedPoint = {
          periodStartDt: point.periodStartDt,
          value: point.value,
        };
        if (map[String(sDate)]) {
          const { sunrise, sunset } = map[String(sDate)];
          if (
            lintedPeriodStartDt >
              moment(sunrise).add(sunriseBuffer, 'minute') &&
            lintedPeriodStartDt <
              moment(sunset).subtract(sunsetBuffer, 'minute')
          ) {
            if (lintedPoint.value < -40 || lintedPoint.value > 85) {
              console.warn(
                `Module temperature out of range for  ${project.name}: ${lintedPoint.value}°C`
              );
            } else {
              map[String(sDate)].moduleTempPoints.push(lintedPoint);
            }
          }
        } else {
          const newSunrise = getProjectSunrise(
            project,
            moment.tz(sDate, timezone).toDate()
          );
          const newSunset = getProjectSunset(
            project,
            moment.tz(sDate, timezone).toDate()
          );
          map[String(sDate)] = {
            totalIrradiance: 0,
            irradianceCoverageMinutes: 0,
            sunrise: newSunrise,
            sunset: newSunset,
            points: [],
            moduleTempPoints: [],
          };
          if (
            lintedPeriodStartDt > newSunrise &&
            lintedPeriodStartDt < newSunset
          ) {
            map[String(sDate)].moduleTempPoints.push(lintedPoint);
          }
        }
      });

      const getInverterAvailability = (sunrise, sunset) => {
        const sunriseBuffer = 15; // NOTE: set this in case you want to ignore the first and last moments of sunlight
        const sunsetBuffer = 15; // NOTE: set this in case you want to ignore the first and last moments of sunlight

        let totalReadings = 0;
        let dataIntervals;

        inverterProdPeriodData?.forEach((inverter) => {
          const filteredInverterReadings =
            inverter.inverterProductionPeriods?.filter((el) => {
              if (!dataIntervals) {
                dataIntervals = parseInt(el.timeUnit, 10);
              }
              const lintedPeriodStartDt = moment.tz(el.periodStartDt, timezone);
              return (
                lintedPeriodStartDt >= sunrise && lintedPeriodStartDt <= sunset
              );
            });
          totalReadings += filteredInverterReadings.length;
        });
        let totalMinutes =
          moment(sunset).diff(moment(sunrise), 'minutes') -
          (sunriseBuffer + sunsetBuffer);

        totalMinutes -= totalMinutes % (dataIntervals || 5);
        const expectedDataPoints =
          (totalMinutes * inverterProdPeriodData?.length || 0) /
          (dataIntervals || 5);
        return !expectedDataPoints
          ? 0
          : (totalReadings / expectedDataPoints) * 100;
      };

      const aPromises = [];

      // NOTE: filling missing days within range with 0 values
      let startDtCounter = new Date(startDt);
      const dEndDt = new Date(endDt);
      const aDates = [];
      while (startDtCounter < dEndDt) {
        aDates.push(moment(startDtCounter).format('YYYY-MM-DD'));
        const newDate = startDtCounter.setDate(startDtCounter.getDate() + 1);
        startDtCounter = new Date(newDate);
      }
      aDates.forEach((sDate) => {
        if (!map[String(sDate)]) {
          map[String(sDate)] = {
            totalIrradiance: 0,
            irradianceCoverageMinutes: 0,
            sunrise: getProjectSunrise(
              project,
              moment.tz(sDate, timezone).toDate()
            ),
            sunset: getProjectSunset(
              project,
              moment.tz(sDate, timezone).toDate()
            ),
            points: [],
            moduleTempPoints: [],
          };
        }
      });

      /* -------------------------------------------------------------------------- */
      /*          Iterate through the days and calculate PR-related values          */
      /* -------------------------------------------------------------------------- */

      const getIrradianceOverride = async (
        // sgdSystemId,
        periodStartDt,
        curData
      ) => {
        if (!sgdSystemId || !curData.points || curData.points.length === 0)
          return;
        const sgdIrradianceOverride =
          await SGDService.fetchDailyIrradianceDataRange(
            sgdSystemId,
            periodStartDt
          );
        curData.totalIrradiance = sgdIrradianceOverride || 0;
      };

      const getTotalDaylightMinutes = (sunrise, sunset) =>
        moment(sunset).diff(moment(sunrise), 'minutes');

      const getExistingDataPoints = (periodStartDt) =>
        project.expectedProductionPeriods?.filter(
          (el) =>
            moment(el.periodStartDt).format('YYYYMMDD') ===
            moment(periodStartDt).format('YYYYMMDD')
        ) || [];

      const findLatestIrradiancePt = (dt, curData) => {
        const aIrradiance = curData.points || [];
        let returnVal = 0;
        for (const element of aIrradiance) {
          if (element.periodStartDt > dt) return returnVal;
          returnVal = element.value;
        }
        return returnVal;
      };

      const getLintedModuleTempPoints = (curData, adjustModuleTempFlg) =>
        curData.moduleTempPoints
          ?.map((moduleTempPoint) => {
            const latestIrradiancePt = findLatestIrradiancePt(
              moduleTempPoint.periodStartDt,
              curData
            );
            if (latestIrradiancePt < 200) return null;
            const changeObj = { ...moduleTempPoint };
            const adjustedVal = adjustModuleTempFlg
              ? moduleTempPoint.value + (latestIrradiancePt / 1000) * 3
              : moduleTempPoint.value;
            changeObj.value = adjustedVal;
            return changeObj;
          })
          .filter((el) => !!el) || [];

      const calculateExpectedProduction = (
        curData,
        projectPR,
        pNomTotal,
        moduleTempCorrectionRatio
      ) =>
        curData.totalIrradiance *
        projectPR *
        pNomTotal *
        moduleTempCorrectionRatio;

      const updateExistingDataPoints = (
        existingDataPoints,
        expectedProduction,
        curData,
        sgdSystemIrradianceOverride,
        totalDaylightMinutes,
        inverterAvailability,
        avgModuleTemperature,
        periodStartDt
      ) => {
        if (existingDataPoints.length > 0) {
          statusData.existingDataPoints = statusData.existingDataPoints
            ? statusData.existingDataPoints + 1
            : 1;
          const updateObj = existingDataPoints[0];
          updateObj.production = expectedProduction;
          updateObj.globEffIrradiance = Math.max(curData.totalIrradiance, 0);
          updateObj.irradianceDataCoverage = sgdSystemIrradianceOverride
            ? 1
            : (curData.irradianceCoverageMinutes + 0) / totalDaylightMinutes;
          updateObj.inverterAvailability = inverterAvailability;
          updateObj.avgModuleTemperature = avgModuleTemperature;
          aPromises.push(updateObj.save());
        } else {
          statusData.newDataPoints = statusData.newDataPoints
            ? statusData.newDataPoints + 1
            : 1;
          const newObj = {
            periodStartDt: periodStartDt.format('YYYY-MM-DD HH:mm:ss'),
            production: expectedProduction,
            projectId: project.id,
            globEffIrradiance: Math.max(curData.totalIrradiance, 0),
            irradianceDataCoverage: sgdSystemIrradianceOverride
              ? 1
              : curData.irradianceCoverageMinutes / totalDaylightMinutes,
            inverterAvailability,
            avgModuleTemperature,
          };
          aPromises.push(ExpectedProductionPeriod.create(newObj));
        }
      };

      Object.keys(map).forEach(async (key) => {
        const curData = map[String(key)];
        const periodStartDt = moment(key);
        const { sunrise, sunset } = curData;
        const sgdSystemIrradianceOverride =
          sgdSystemId && curData.points && curData.points.length > 0;

        await getIrradianceOverride(sgdSystemId, periodStartDt, curData);

        const totalDaylightMinutes = getTotalDaylightMinutes(sunrise, sunset);

        const existingDataPoints = getExistingDataPoints(periodStartDt);

        const projectPR = getPerformanceRatio(project, new Date(periodStartDt));
        const moduleTempRef = getModuleTempRef(
          project,
          new Date(periodStartDt)
        );

        const pNomTotal = (project.systemSizeDC || 0) * 1000;

        const adjustModuleTempProjectIds = [136, 173, 174, 191, 190]; // Nova Cruz, Redencao, Itaporanga, Taquaritinga, Itabaiana
        const adjustModuleTempFlg =
          adjustModuleTempProjectIds.indexOf(project.id) > -1;

        const lintedModuleTempPoints = getLintedModuleTempPoints(
          curData,
          adjustModuleTempFlg
        );

        const avgModuleTemperature =
          lintedModuleTempPoints.reduce((a, b) => a + b.value, 0) /
            lintedModuleTempPoints.length || null;

        const projectsWFaultyModuleTemps = [98]; // Waltham

        const testConditionTemperature = 25;
        const moduleTempCorrectionRatio =
          !avgModuleTemperature ||
          !moduleTempRef ||
          projectsWFaultyModuleTemps.includes(project.id)
            ? 1
            : (1 +
                Math.abs(project.tempCoeffPmax || 0) *
                  (testConditionTemperature - avgModuleTemperature)) /
              (1 +
                Math.abs(project.tempCoeffPmax || 0) *
                  (testConditionTemperature - moduleTempRef));

        const expectedProduction = calculateExpectedProduction(
          curData,
          projectPR,
          pNomTotal,
          moduleTempCorrectionRatio
        );

        const inverterAvailability = getInverterAvailability(sunrise, sunset);
        updateExistingDataPoints(
          existingDataPoints,
          expectedProduction,
          curData,
          sgdSystemIrradianceOverride,
          totalDaylightMinutes,
          inverterAvailability,
          avgModuleTemperature,
          periodStartDt
        );
      });

      await Promise.all(aPromises);
    } catch (err) {
      console.error(err);
      const eMsg = `Error backfilling expected generation data with projectId : ${projectId}`;
      SlackService.logToSlack({
        title: eMsg,
        type: 'monitoring',
        data: [
          {
            label: 'Details',
            value: stringifyObject({
              projectId,
              startDt,
              endDt,
            }),
          },
          {
            label: 'Error',
            value: stringifyObject(err),
          },
        ],
      });
      done(new Error(eMsg));
      return null;
    }
    if (logToSlack) {
      SlackService.logToSlack({
        title: `Completed project expected generation backfill for ${
          project.name
        }. Updated ${numeral(statusData.existingDataPoints || 0).format(
          '0,0'
        )} records and created ${numeral(statusData.newDataPoints || 0).format(
          '0,0'
        )} new records.`,
        type: 'monitoring',
        data: [
          {
            label: 'Details',
            value: stringifyObject({
              projectId,
              startDt,
              endDt,
            }),
          },
        ],
      });
    }
    done();
    return null;
  });

  backfillSCADAGenerationQueue.process(1, async (job, done) => {
    const { scadaSystemId, startDt, endDt } = job.data;

    const scadaSystem = await ScadaSystem.findByPk(scadaSystemId, {
      include: [{ model: Project, attributes: ['id', 'name'] }],
    });
    // Track record counts for the Slack message
    let newRecords = 0;
    let duplicateRecords = 0;
    let updatedRecords = 0;

    try {
      const historicalRecords = await SCADAService.fetchGenerationDataRange(
        scadaSystem,
        startDt,
        endDt
      );

      // Save each record synchronously because we don't want duplicate records so need to know what already exists
      for (let index = 0; index < historicalRecords.length; index += 1) {
        const record = historicalRecords[parseInt(index, 10)];
        // eslint-disable-next-line no-await-in-loop
        const result = await SCADAService.saveProductionPeriod(
          record,
          scadaSystemId
        );

        // Track record status if the saveProductionPeriod method returns status information
        if (result && result.status) {
          if (result.status === 'created') {
            newRecords += 1;
          } else if (result.status === 'updated') {
            updatedRecords += 1;
          } else if (result.status === 'duplicate') {
            duplicateRecords += 1;
          }
        } else {
          // If no status is returned, count it as a new record
          newRecords += 1;
        }
      }
    } catch (err) {
      SlackService.logToSlack({
        title: `Error backfilling SCADA System generation data for ${scadaSystem?.project?.name}`,
        type: 'monitoring',
        data: [
          {
            label: 'Details',
            value: stringifyObject({
              scadaSystemId,
              startDt,
              endDt,
            }),
          },
          {
            label: 'Error',
            value: stringifyObject(err),
          },
        ],
      });
      done(
        new Error(`Error backfilling SCADA System generation data. Err: ${err}`)
      );
      return null;
    }

    SlackService.logToSlack({
      title: `Completed SCADA generation backfill for ${scadaSystem?.project?.name}`,
      type: 'monitoring',
      data: [
        {
          label: 'Details',
          value: stringifyObject({
            scadaSystemId,
            startDt,
            endDt,
          }),
        },
        {
          label: 'New Records',
          value: newRecords,
        },
        {
          label: 'Updated Records',
          value: updatedRecords,
        },
        {
          label: 'Duplicate Records',
          value: duplicateRecords,
        },
      ],
    });
    done();
    return null;
  });

  backfillSCADASystemInverterPowerQueue.process(1, async (job, done) => {
    const { scadaSystemId, inverterId, startDt, endDt } = job.data;

    const [inverter, scadaSystem] = await Promise.all([
      Inverter.findByPk(inverterId, { attributes: ['id', 'name', 'dataKey'] }),
      ScadaSystem.findByPk(scadaSystemId, {
        include: [{ model: Project, attributes: ['id', 'name'] }],
      }),
    ]);

    const historicalRecords = await SCADAService.fetchInverterPowerDataRange(
      scadaSystem,
      inverter,
      startDt,
      endDt
    ).catch((error) => {
      SlackService.logToSlack({
        title: `Error backfilling SCADA System inverter power for ${scadaSystem?.project?.name}`,
        type: 'monitoring',
        data: [
          {
            label: 'Details',
            value: stringifyObject({
              scadaSystemId,
              inverterId,
              startDt,
              endDt,
            }),
          },
          {
            label: 'Error',
            value: stringifyObject(error),
          },
        ],
      });
      done(
        new Error(
          `Error backfilling SCADA System inverter power for ${scadaSystem?.project?.name}. Err: ${error}`
        )
      );
      return null;
    });
    if (!historicalRecords) {
      done();
      return null;
    }
    let newRecordCounter = 0;
    let duplicateRecordCounter = 0;
    let updatedRecordCounter = 0;
    const createRecords = historicalRecords.map((historicalRecord) => {
      const createObj = { ...historicalRecord };
      return InverterProductionPeriod.findOne({
        attributes: ['id', 'production'],
        where: {
          inverterId: createObj.inverterId,
          periodStartDt: createObj.periodStartDt,
        },
      }).then((resp) => {
        if (resp) {
          if (resp.production === createObj.production) {
            duplicateRecordCounter += 1;
            return null;
          }
          updatedRecordCounter += 1;
          const updateObject = resp;
          Object.keys(createObj).forEach((key) => {
            const val = createObj[String(key)];
            updateObject[String(key)] = val;
          });
          return updateObject.save();
        }
        newRecordCounter += 1;
        return InverterProductionPeriod.create(createObj);
      });
    });

    const results = await Promise.all(createRecords);

    SlackService.logToSlack({
      title: `Completed SCADA inverter backfill for ${scadaSystem?.project?.name}.`,
      type: 'monitoring',
      data: [
        {
          label: 'Inverter',
          value: `${inverter.name} (#${inverter.id})`,
        },
        {
          label: 'Start',
          value: String(startDt),
        },
        {
          label: 'End',
          value: String(endDt),
        },
        {
          label: 'Updated Records',
          value: updatedRecordCounter,
        },
        {
          label: 'New Records',
          value: newRecordCounter,
        },
        {
          label: 'Duplicate Records',
          value: duplicateRecordCounter,
        },
      ],
    });
    if (results instanceof Error) {
      done(results);
    } else {
      done();
    }
    return null;
  });

  backfillSCADASensorDataQueue.process(1, async (job, done) => {
    const { scadaSystemId, sensorId, startDt, endDt } = job.data;
    const [sensor, scadaSystem] = await Promise.all([
      Sensor.findByPk(sensorId),
      ScadaSystem.findByPk(scadaSystemId, {
        include: [{ model: Project, attributes: ['id', 'name'] }],
      }),
    ]);

    const historicalRecords = await SCADAService.fetchSensorDataRange(
      scadaSystem,
      sensor,
      startDt,
      endDt
    ).catch((error) => {
      SlackService.logToSlack({
        title: `Error backfilling SCADA System sensor data for ${scadaSystem?.project?.name}`,
        type: 'monitoring',
        data: [
          {
            label: 'Details',
            value: stringifyObject({
              scadaSystemId,
              sensorId,
              startDt,
              endDt,
            }),
          },
          {
            label: 'Error',
            value: stringifyObject(error),
          },
        ],
      });
      done(
        new Error(
          `Error backfilling SCADA System sensor data (${sensor.name}) for ${scadaSystem?.project?.name}. Err: ${error}`
        )
      );
      return null;
    });
    if (!historicalRecords) {
      done();
      return null;
    }

    const existingSensorDataPeriods = await sensor
      .getSensorDataPeriods({
        attributes: ['id', 'periodStartDt', 'value'],
        where: {
          periodStartDt: {
            // Expand range to make sure we cover all data points
            [Op.gte]: moment(startDt).add(-1, 'day').toDate(),
            [Op.lt]: moment(endDt).add(1, 'day').toDate(),
          },
        },
      })
      .then((points) => {
        const map = {};
        points.forEach((point) => {
          map[String(point.periodStartDt)] = point;
        });
        return map;
      });
    let createdCounter = 0;
    let updatedCounter = 0;
    let duplicateDataCounter = 0;
    for (let index2 = 0; index2 < historicalRecords.length; index2 += 1) {
      const historicalRecord = historicalRecords[parseInt(index2, 10)];
      // eslint-disable-next-line no-await-in-loop
      await SCADAService.saveSensorDataPeriod(
        historicalRecord,
        existingSensorDataPeriods
      ).then((resp) => {
        const { duplicateData, updated, created } = resp;
        createdCounter += created;
        duplicateDataCounter += duplicateData;
        updatedCounter += updated;
      });
    }

    SlackService.logToSlack({
      title: `Completed SCADA sensor backfill for ${scadaSystem?.project?.name}`,
      type: 'monitoring',
      data: [
        { label: 'Sensor', value: `${sensor.name} (#${sensor.id})` },
        { label: 'Start', value: String(startDt) },
        { label: 'End', value: String(endDt) },
        { label: 'Updated Records', value: updatedCounter },
        { label: 'New Records', value: createdCounter },
        { label: 'Duplicate Records', value: duplicateDataCounter },
      ],
    });

    done();
    return null;
  });

  backfillAMMPSensorDataQueue.process(1, async (job, done) => {
    const { sunExchangeSiteId, startDt, endDt } = job.data;

    // Track record counts for the Slack message
    let newRecords = 0;
    let duplicateRecords = 0;
    let totalRecordsFetched = 0;

    try {
      const site = await SunExchangeSite.findByPk(sunExchangeSiteId);
      const currDt = moment.utc(startDt);
      const stopDt = moment.utc(endDt);
      let nextDt = moment(startDt).add(1, 'day');
      // NOTE: If you fetch too large of a date range from AMMP, they return no data. Break it down into individual days
      while (currDt <= stopDt) {
        if (nextDt > stopDt) {
          nextDt = moment(stopDt);
        }
        // eslint-disable-next-line no-await-in-loop
        const records = await SunExchangeService.fetchEnvironmentalData(
          site,
          currDt,
          nextDt
        ).catch((error) => {
          console.error(`Error backfilling AMMP sensor data. Err: ${error}`);
          SlackService.logToSlack({
            title: `Error backfilling AMMP sensor data`,
            type: 'monitoring',
            data: [
              {
                label: 'Details',
                value: stringifyObject({
                  sunExchangeSiteId,
                  startDt,
                  endDt,
                }),
              },
              {
                label: 'Error',
                value: stringifyObject(error),
              },
            ],
          });
          done(new Error(`Error backfilling AMMP sensor data. Err: ${error}`));
          return null;
        });

        if (records && records.length > 0) {
          totalRecordsFetched += records.length;

          // Check for existing records to properly track duplicates
          // eslint-disable-next-line no-await-in-loop
          const existingRecords = await SensorDataPeriod.findAll({
            attributes: ['periodStartDt', 'sensorId'],
            where: {
              periodStartDt: {
                [Op.in]: records.map((r) => r.periodStartDt),
              },
              sensorId: {
                [Op.in]: records.map((r) => r.sensorId),
              },
            },
          });

          // Create a set of existing record keys for fast lookup
          const existingKeys = new Set(
            existingRecords.map((r) => `${r.sensorId}-${r.periodStartDt}`)
          );

          // Filter out records that already exist
          const newRecordsToCreate = records.filter(
            (r) => !existingKeys.has(`${r.sensorId}-${r.periodStartDt}`)
          );

          // Track counts
          const currentDuplicates = records.length - newRecordsToCreate.length;
          duplicateRecords += currentDuplicates;

          if (newRecordsToCreate.length > 0) {
            // eslint-disable-next-line no-await-in-loop
            const result = await SensorDataPeriod.bulkCreate(
              newRecordsToCreate,
              {
                ignoreDuplicates: true, // Keep this as a safety net
              }
            );
            newRecords += result.length;
          }
        }

        currDt.add(1, 'day');
        nextDt.add(1, 'day');
      }

      SlackService.logToSlack({
        title: `Completed AMMP sensor backfill`,
        type: 'monitoring',
        data: [
          {
            label: 'Details',
            value: stringifyObject({
              sunExchangeSiteId,
              startDt,
              endDt,
            }),
          },
          {
            label: 'New Records',
            value: newRecords,
          },
          {
            label: 'Duplicate Records',
            value: duplicateRecords,
          },
          {
            label: 'Total Records Fetched',
            value: totalRecordsFetched,
          },
        ],
      });

      done();
    } catch (err) {
      SlackService.logToSlack({
        title: `Failed to backfill AMMP sensor data`,
        type: 'monitoring',
        data: [
          {
            label: 'Error',
            value: stringifyObject(err),
          },
        ],
      });
      done(new Error(`Error backfilling AMMP sensor data. Err: ${err}`));
    }
  });

  backfillAMMPInverterPowerQueue.process(1, async (job, done) => {
    const { sunExchangeSiteId, inverterId, startDt, endDt } = job.data;

    try {
      const [inverter, site] = await Promise.all([
        Inverter.findByPk(inverterId),
        SunExchangeSite.findByPk(sunExchangeSiteId),
      ]);
      const currDt = moment.utc(startDt);
      const stopDt = moment.utc(endDt);
      let nextDt = moment(startDt).add(1, 'day');
      // NOTE: If you fetch too large of a date range from AMMP, they return no data. Break it down into individual days
      while (currDt <= stopDt) {
        if (nextDt.isAfter(stopDt)) {
          nextDt = moment(stopDt);
        }
        // eslint-disable-next-line no-await-in-loop
        const records = await SunExchangeService.fetchInverterPowerData(
          site,
          inverter,
          currDt,
          nextDt
        ).catch((error) => {
          console.error(
            `Error backfilling AMMP inverter power data. Err: ${error}`
          );
          SlackService.logToSlack({
            title: `Error backfilling AMMP inverter power data`,
            type: 'monitoring',
            data: [
              {
                label: 'Details',
                value: stringifyObject({
                  sunExchangeSiteId,
                  inverterId,
                  startDt,
                  endDt,
                }),
              },
              {
                label: 'Error',
                value: stringifyObject(error),
              },
            ],
          });
          done(
            new Error(
              `Error backfilling AMMP inverter power data. Err: ${error}`
            )
          );
          return null;
        });
        // eslint-disable-next-line no-await-in-loop
        await InverterProductionPeriod.bulkCreate(records, {
          // updateOnDuplicate: ['value', 'timeUnit'],
          ignoreDuplicates: true,
        });
        currDt.add(1, 'day');
        nextDt.add(1, 'day');
      }
    } catch (err) {
      SlackService.logToSlack({
        title: `Error backfilling SunExchange inverter power data`,
        type: 'monitoring',
        data: [
          {
            label: 'Details',
            value: stringifyObject({
              sunExchangeSiteId,
              inverterId,
              startDt,
              endDt,
            }),
          },
          {
            label: 'Error',
            value: stringifyObject(err),
          },
        ],
      });
      done(
        new Error(`Error backfilling SunExchange inverter data. Err: ${err}`)
      );
      return null;
    }

    SlackService.logToSlack({
      title: `Completed SunExchange inverter power backfill`,
      type: 'monitoring',
      data: [
        {
          label: 'Details',
          value: stringifyObject({
            sunExchangeSiteId,
            inverterId,
            startDt,
            endDt,
          }),
        },
      ],
    });
    done();
    return null;
  });

  backfillSGDGenerationQueue.process(1, async (job, done) => {
    const { sgdSystemId, startDt, endDt } = job.data;

    const system = await SgdSystem.findByPk(sgdSystemId, {
      include: [{ model: Project, attributes: ['id', 'name'] }],
    });

    // Track record counts for the Slack message
    let newRecords = 0;
    let duplicateRecords = 0;
    let updatedRecords = 0;

    try {
      await SGDService.fetchGenerationDataRange(
        sgdSystemId,
        startDt,
        endDt
      ).then(async (historicalRecords) => {
        // Save each record synchronously because we don't want duplicate records so need to know what already exists
        for (let index = 0; index < historicalRecords.length; index += 1) {
          const record = historicalRecords[parseInt(index, 10)];

          // Check if record already exists to track status
          // eslint-disable-next-line no-await-in-loop
          const existingRecord = await ProductionPeriod.findOne({
            where: {
              sgdSystemId,
              periodStartDt: record.periodStartDt,
              timeUnit: record.timeUnit,
              unit: record.unit,
            },
          });

          // eslint-disable-next-line no-await-in-loop
          const result = await SGDService.saveProductionPeriod(
            record,
            sgdSystemId
          );

          // Track the operation result
          if (result) {
            if (!existingRecord) {
              newRecords += 1;
            } else if (existingRecord.freezeDataFlg) {
              duplicateRecords += 1;
            } else if (record.production > existingRecord.production) {
              updatedRecords += 1;
            } else {
              duplicateRecords += 1;
            }
          }
        }
      });
    } catch (err) {
      SlackService.logToSlack({
        title: `Error backfilling SGD System generation data for ${system?.project?.name}`,
        type: 'monitoring',
        data: [
          {
            label: 'Details',
            value: stringifyObject({
              sgdSystemId,
              startDt,
              endDt,
            }),
          },
          {
            label: 'Error',
            value: stringifyObject(err),
          },
        ],
      });
      done(
        new Error(`Error backfilling SGD System generation data. Err: ${err}`)
      );
      return null;
    }

    SlackService.logToSlack({
      title: `Completed SGD generation backfill for ${system?.project?.name}`,
      type: 'monitoring',
      data: [
        {
          label: 'Details',
          value: stringifyObject({
            sgdSystemId,
            startDt,
            endDt,
          }),
        },
        {
          label: 'New Records',
          value: newRecords,
        },
        {
          label: 'Updated Records',
          value: updatedRecords,
        },
        {
          label: 'Duplicate Records',
          value: duplicateRecords,
        },
      ],
    });
    done();
    return null;
  });

  backfillSMAGenerationQueue.process(1, async (job, done) => {
    const { smaSiteId, startDt, endDt } = job.data;

    const system = await SmaSite.findByPk(smaSiteId, {
      include: [{ model: Project, attributes: ['id', 'name'] }],
    });
    try {
      await SMAService.fetchGenerationData(smaSiteId, startDt, endDt).then(
        async (historicalRecords) => {
          // Save each record synchronously because we don't want duplicate records so need to know what already exists
          for (let index = 0; index < historicalRecords.length; index += 1) {
            const record = historicalRecords[parseInt(index, 10)];
            // eslint-disable-next-line no-await-in-loop
            await SMAService.saveProductionPeriod(record);
          }
        }
      );
    } catch (err) {
      SlackService.logToSlack({
        title: `Error backfilling SMA site generation data for ${system?.project?.name}`,
        type: 'monitoring',
        data: [
          {
            label: 'Details',
            value: stringifyObject({
              smaSiteId,
              startDt,
              endDt,
            }),
          },
          {
            label: 'Error',
            value: stringifyObject(err),
          },
        ],
      });
      done(
        new Error(`Error backfilling SMA site generation data. Err: ${err}`)
      );
      return null;
    }

    SlackService.logToSlack({
      title: `Completed SMA generation backfill`,
      type: 'monitoring',
      data: [
        {
          label: 'Details',
          value: stringifyObject({
            smaSiteId,
            startDt,
            endDt,
          }),
        },
      ],
    });
    done();
    return null;
  });

  pollRecentSgdInverterGenerationDataQueue.process(1, async (job, done) => {
    try {
      const { inverterId, timezone } = job.data;
      const record = await SGDService.fetchInverterDailyGeneration(
        inverterId,
        timezone
      );
      if (record) {
        await saveUpdateDailyInverterGeneration(record, inverterId);
      }
    } catch (error) {
      // Only log to Slack if it's not a known API connection error
      if (!isAPIConnectionError(error)) {
        SlackService.logToSlack({
          title: `Failed to fetch recent SGD inverter generation data`,
          type: 'monitoring',
          data: [
            {
              label: 'Error',
              value: stringifyObject(error),
            },
            {
              label: 'Job Data',
              value: stringifyObject(job.data),
            },
          ],
        });
      } else {
        console.warn(
          'SGD API connection error (not logging to Slack):',
          String(error).substring(0, 200)
        );
      }
      done(
        new Error(`Error polling recent SGD inverter generation data: ${error}`)
      );
      return null;
    }

    done();
    return null;
  });

  backfillSolisGenerationQueue.process(1, async (job, done) => {
    const { solisSystemId, startDt, endDt } = job.data;

    const solisSystem = await SolisSystem.findByPk(solisSystemId, {
      include: [{ model: Project, attributes: ['id', 'name'] }],
    });
    try {
      await SolisService.fetchGenerationDataRange(
        solisSystem,
        startDt,
        endDt
      ).then(async (historicalRecords) => {
        // Save each record synchronously because we don't want duplicate records so need to know what already exists
        for (let index = 0; index < historicalRecords.length; index += 1) {
          const record = historicalRecords[parseInt(index, 10)];
          // eslint-disable-next-line no-await-in-loop
          await SolisService.saveProductionPeriod(record, solisSystemId);
        }
      });
    } catch (err) {
      SlackService.logToSlack({
        title: `Error backfilling Solis System generation data for ${solisSystem?.project?.name}`,
        type: 'monitoring',
        data: [
          {
            label: 'Details',
            value: stringifyObject({
              solisSystemId,
              startDt,
              endDt,
            }),
          },
          {
            label: 'Error',
            value: stringifyObject(err),
          },
        ],
      });
      done(
        new Error(`Error backfilling Solis System generation data. Err: ${err}`)
      );
      return null;
    }

    SlackService.logToSlack({
      title: `Completed Solis generation backfill`,
      type: 'monitoring',
      data: [
        {
          label: 'Details',
          value: stringifyObject({
            solisSystemId,
            startDt,
            endDt,
          }),
        },
      ],
    });
    done();
    return null;
  });

  backfillSolisInverterPowerQueue.process(1, async (job, done) => {
    const { solisSystemId, inverterId, startDt, endDt } = job.data;

    try {
      const records = await SolisService.fetchInverterDataRange(
        solisSystemId,
        inverterId,
        startDt,
        endDt
      ).catch((error) => {
        console.error(
          `Error backfilling Solis inverter power data. Err: ${error}`
        );
        SlackService.logToSlack({
          title: `Error backfilling Solis inverter power data`,
          type: 'monitoring',
          data: [
            {
              label: 'Details',
              value: stringifyObject({
                solisSystemId,
                inverterId,
                startDt,
                endDt,
              }),
            },
            {
              label: 'Error',
              value: stringifyObject(error),
            },
          ],
        });
        done(
          new Error(
            `Error backfilling Solis inverter power data. Err: ${error}`
          )
        );
        return null;
      });
      // eslint-disable-next-line no-await-in-loop
      await InverterProductionPeriod.bulkCreate(records, {
        // updateOnDuplicate: ['value', 'timeUnit'],
        ignoreDuplicates: true,
      });
    } catch (err) {
      SlackService.logToSlack({
        title: `Error backfilling Solis inverter power data`,
        type: 'monitoring',
        data: [
          {
            label: 'Details',
            value: stringifyObject({
              solisSystemId,
              inverterId,
              startDt,
              endDt,
            }),
          },
          {
            label: 'Error',
            value: stringifyObject(err),
          },
        ],
      });
      done(new Error(`Error backfilling Solis inverter data. Err: ${err}`));
      return null;
    }

    SlackService.logToSlack({
      title: `Completed Solis inverter power backfill`,
      type: 'monitoring',
      data: [
        {
          label: 'Details',
          value: stringifyObject({
            solisSystemId,
            inverterId,
            startDt,
            endDt,
          }),
        },
      ],
    });
    done();
    return null;
  });

  backfillSolisSensorDataQueue.process(1, async (job, done) => {
    const { solisSystemId, startDt, endDt } = job.data;

    try {
      const records = await SolisService.fetchSensorDataRange(
        solisSystemId,
        startDt,
        endDt
      ).catch((error) => {
        console.error(`Error backfilling Solis sensor data. Err: ${error}`);
        SlackService.logToSlack({
          title: `Error backfilling Solis solis data`,
          type: 'monitoring',
          data: [
            {
              label: 'Details',
              value: stringifyObject({
                solisSystemId,
                startDt,
                endDt,
              }),
            },
            {
              label: 'Error',
              value: stringifyObject(error),
            },
          ],
        });
        done(new Error(`Error backfilling Solis sensor data. Err: ${error}`));
        return null;
      });
      // eslint-disable-next-line no-await-in-loop
      await SensorDataPeriod.bulkCreate(records, {
        // updateOnDuplicate: ['value', 'timeUnit'],
        ignoreDuplicates: true,
      });
    } catch (err) {
      SlackService.logToSlack({
        title: `Error backfilling Solis sensor data`,
        type: 'monitoring',
        data: [
          {
            label: 'Details',
            value: stringifyObject({
              solisSystemId,
              startDt,
              endDt,
            }),
          },
          {
            label: 'Error',
            value: stringifyObject(err),
          },
        ],
      });
      done(new Error(`Error backfilling Solis sensor data. Err: ${err}`));
      return null;
    }

    SlackService.logToSlack({
      title: `Completed Solis sensor backfill`,
      type: 'monitoring',
      data: [
        {
          label: 'Details',
          value: stringifyObject({
            solisSystemId,
            startDt,
            endDt,
          }),
        },
      ],
    });
    done();
    return null;
  });

  pollRecentSGDGenerationDataQueue.process(2, async (job, done) => {
    const { sgdSystemId } = job.data;
    const sgdSystem = await SgdSystem.findByPk(sgdSystemId, {
      include: [
        { attributes: ['id', 'name'], required: false, model: Project },
      ],
    });
    try {
      const historicalRecords = await SGDService.fetchMostRecentGenerationData(
        sgdSystem
      );
      // Save each record synchronously because we don't want duplicate records so need to know what already exists
      for (let index = 0; index < historicalRecords.length; index += 1) {
        const record = historicalRecords[parseInt(index, 10)];
        // eslint-disable-next-line no-await-in-loop
        await SGDService.saveProductionPeriod(record, sgdSystemId);
      }
    } catch (err) {
      done(
        new Error(
          `Error polling recent SGD System generation data. Err: ${err}`
        )
      );
      return null;
    }

    done();
    return null;
  });

  fetchSGDInverterDataQueue.process(2, async (job, done) => {
    try {
      const { sgdSystemId, inverterId, startDt, endDt } = job.data;
      let records = null;
      if (!startDt || !endDt) {
        records = await SGDService.fetchMostRecentInverterPowerData(
          sgdSystemId,
          inverterId
        );
      } else {
        records = await SGDService.fetchInverterPowerData(
          sgdSystemId,
          inverterId,
          startDt,
          endDt
        );
      }

      if (records?.length) {
        await InverterProductionPeriod.bulkCreate(records, {
          ignoreDuplicates: true,
        }).catch((err) => {
          SlackService.logToSlack({
            title: `Failed to save recent SGD inverter power data`,
            type: 'monitoring',
            data: [
              {
                label: 'Error',
                value: stringifyObject(err),
              },
              {
                label: 'Job Data',
                value: stringifyObject(job.data),
              },
            ],
          });
          done(
            new Error(`Error saving recent SGD inverter power data: ${err}`)
          );
          return null;
        });
      }
    } catch (error) {
      // Only log to Slack if it's not a known API connection error
      if (!isAPIConnectionError(error)) {
        SlackService.logToSlack({
          title: `Failed to fetch recent SGD inverter power data`,
          type: 'monitoring',
          data: [
            {
              label: 'Error',
              value: stringifyObject(error),
            },
            {
              label: 'Job Data',
              value: stringifyObject(job.data),
            },
          ],
        });
      } else {
        console.warn(
          'SGD API connection error (not logging to Slack):',
          String(error).substring(0, 200)
        );
      }
      done(new Error(`Error polling recent SGD inverter power data: ${error}`));
      return null;
    }

    done();
    return null;
  });

  fetchSGDSensorDataQueue.process(2, async (job, done) => {
    try {
      const { sgdSystemId, sensorId, startDt, endDt } = job.data;
      let records = null;
      if (!startDt || !endDt) {
        records = await SGDService.fetchMostRecentSensorData(
          sgdSystemId,
          sensorId
        );
      } else {
        records = await SGDService.fetchSensorData(
          sgdSystemId,
          sensorId,
          startDt,
          endDt
        );
      }
      if (records?.length) {
        await SensorDataPeriod.bulkCreate(records, {
          ignoreDuplicates: true,
        }).catch((err) => {
          SlackService.logToSlack({
            title: `Failed to save recent SGD sensor data`,
            type: 'monitoring',
            data: [
              {
                label: 'Error',
                value: stringifyObject(err),
              },
              {
                label: 'Job Data',
                value: stringifyObject(job.data),
              },
            ],
          });
          done(new Error(`Error saving recent SGD sensor data: ${err}`));
          return null;
        });
      }
    } catch (error) {
      // Only log to Slack if it's not a known API connection error
      if (!isAPIConnectionError(error)) {
        SlackService.logToSlack({
          title: `Failed to fetch recent SGD sensor data`,
          type: 'monitoring',
          data: [
            {
              label: 'Error',
              value: stringifyObject(error),
            },
            {
              label: 'Job Data',
              value: stringifyObject(job.data),
            },
          ],
        });
      } else {
        console.warn(
          'SGD API connection error (not logging to Slack):',
          String(error).substring(0, 200)
        );
      }
      done(new Error(`Error polling recent SGD sensor data: ${error}`));
      return null;
    }

    done();
    return null;
  });

  pollRecentSMAGenerationDataQueue.process(2, async (job, done) => {
    const { smaSiteId } = job.data;
    const smaSite = await SmaSite.findByPk(smaSiteId, {
      include: [
        { attributes: ['id', 'name'], required: false, model: Project },
      ],
    });
    try {
      const historicalRecords = await SMAService.fetchMostRecentGenerationData(
        smaSite
      );
      // Save each record synchronously because we don't want duplicate records so need to know what already exists
      for (let index = 0; index < historicalRecords.length; index += 1) {
        const record = historicalRecords[parseInt(index, 10)];
        // eslint-disable-next-line no-await-in-loop
        await SMAService.saveProductionPeriod(record, smaSiteId);
      }
    } catch (err) {
      done(
        new Error(`Error polling recent SMA Site generation data. Err: ${err}`)
      );
      return null;
    }

    done();
    return null;
  });

  fetchSMAInverterDataQueue.process(2, async (job, done) => {
    try {
      const { inverterId, startDt, endDt } = job.data;
      let records = null;
      if (!startDt || !endDt) {
        records = await SMAService.fetchMostRecentInverterPowerData(inverterId);
      } else {
        records = await SMAService.fetchInverterPowerData(
          inverterId,
          startDt,
          endDt
        );
      }

      if (records?.length) {
        await InverterProductionPeriod.bulkCreate(records, {
          ignoreDuplicates: true,
        }).catch((err) => {
          SlackService.logToSlack({
            title: `Failed to save recent SMA inverter power data`,
            type: 'monitoring',
            data: [
              {
                label: 'Error',
                value: stringifyObject(err),
              },
              {
                label: 'Job Data',
                value: stringifyObject(job.data),
              },
            ],
          });
          done(
            new Error(`Error saving recent SMA inverter power data: ${err}`)
          );
          return null;
        });
      }
    } catch (error) {
      SlackService.logToSlack({
        title: `Failed to fetch recent SMA inverter power data`,
        type: 'monitoring',
        data: [
          {
            label: 'Error',
            value: stringifyObject(error),
          },
          {
            label: 'Job Data',
            value: stringifyObject(job.data),
          },
        ],
      });
      done(new Error(`Error polling recent SMA inverter power data: ${error}`));
      return null;
    }

    done();
    return null;
  });

  fetchSMASensorDataQueue.process(2, async (job, done) => {
    try {
      const { smaSiteId, sensorId, startDt, endDt } = job.data;
      let records = null;
      if (!startDt || !endDt) {
        records = await SMAService.fetchMostRecentSensorData(
          smaSiteId,
          sensorId
        );
      } else {
        records = await SMAService.fetchSensorData(
          smaSiteId,
          sensorId,
          startDt,
          endDt
        );
      }
      if (records?.length) {
        await SensorDataPeriod.bulkCreate(records, {
          ignoreDuplicates: true,
        }).catch((err) => {
          SlackService.logToSlack({
            title: `Failed to save recent SMA sensor data`,
            type: 'monitoring',
            data: [
              {
                label: 'Error',
                value: stringifyObject(err),
              },
              {
                label: 'Job Data',
                value: stringifyObject(job.data),
              },
            ],
          });
          done(new Error(`Error saving recent SMA sensor data: ${err}`));
          return null;
        });
      }
    } catch (error) {
      SlackService.logToSlack({
        title: `Failed to fetch recent SMA sensor data`,
        type: 'monitoring',
        data: [
          {
            label: 'Error',
            value: stringifyObject(error),
          },
          {
            label: 'Job Data',
            value: stringifyObject(job.data),
          },
        ],
      });
      done(new Error(`Error polling recent SMA sensor data: ${error}`));
      return null;
    }

    done();
    return null;
  });

  backfillSMAInverterGenerationDataQueue.process(1, async (job, done) => {
    try {
      const { inverterId, startDt, endDt } = job.data;
      const records = await SMAService.fetchInverterDailyGeneration(
        inverterId,
        startDt,
        endDt
      );
      for (let index = 0; index < records.length; index += 1) {
        const record = records[parseInt(index, 10)];
        // eslint-disable-next-line no-await-in-loop
        await saveUpdateDailyInverterGeneration(record, inverterId);
      }
    } catch (error) {
      SlackService.logToSlack({
        title: `Failed to backfill SMA inverter generation data`,
        type: 'monitoring',
        data: [
          {
            label: 'Error',
            value: stringifyObject(error),
          },
          {
            label: 'Job Data',
            value: stringifyObject(job.data),
          },
        ],
      });
      done(
        new Error(`Error backfilling SMA inverter generation data: ${error}`)
      );
      return null;
    }

    done();
    return null;
  });

  fetchSungrowGenerationDataQueue.process(2, async (job, done) => {
    const { sungrowSystemId, startDt, endDt } = job.data;
    const site = await SungrowSystem.findByPk(sungrowSystemId, {
      include: [
        { attributes: ['id', 'name'], required: false, model: Project },
      ],
    });
    try {
      const historicalRecords =
        !startDt || !endDt
          ? await SolarCloudService.fetchMostRecentGenerationData(site)
          : await SolarCloudService.fetchGenerationData(
              site.id,
              startDt,
              endDt
            );
      // Save each record synchronously because we don't want duplicate records so need to know what already exists
      for (let index = 0; index < historicalRecords.length; index += 1) {
        const record = historicalRecords[parseInt(index, 10)];
        // eslint-disable-next-line no-await-in-loop
        await SolarCloudService.saveProductionPeriod(record);
      }
    } catch (err) {
      done(
        new Error(
          `Error polling recent Sungrow Site generation data. Err: ${err}`
        )
      );
      return null;
    }

    done();
    return null;
  });

  fetchSungrowInverterDataQueue.process(2, async (job, done) => {
    try {
      const { inverterId, startDt, endDt } = job.data;
      const records =
        !startDt || !endDt
          ? await SolarCloudService.fetchMostRecentInverterPowerData(inverterId)
          : await SolarCloudService.fetchInverterPowerData(
              inverterId,
              startDt,
              endDt
            );
      if (records?.length) {
        await InverterProductionPeriod.bulkCreate(records, {
          ignoreDuplicates: true,
        }).catch((err) => {
          SlackService.logToSlack({
            title: `Failed to save recent Sungrow inverter power data`,
            type: 'monitoring',
            data: [
              {
                label: 'Error',
                value: stringifyObject(err),
              },
              {
                label: 'Job Data',
                value: stringifyObject(job.data),
              },
            ],
          });
          done(
            new Error(`Error saving recent Sungrow inverter power data: ${err}`)
          );
          return null;
        });
      }
    } catch (error) {
      SlackService.logToSlack({
        title: `Failed to fetch recent Sungrow inverter power data`,
        type: 'monitoring',
        data: [
          {
            label: 'Error',
            value: stringifyObject(error),
          },
          {
            label: 'Job Data',
            value: stringifyObject(job.data),
          },
        ],
      });
      done(
        new Error(`Error polling recent Sungrow inverter power data: ${error}`)
      );
      return null;
    }

    done();
    return null;
  });

  fetchSungrowSensorDataQueue.process(2, async (job, done) => {
    try {
      const { sensorId, startDt, endDt } = job.data;
      let records = null;
      if (!startDt || !endDt) {
        records = await SolarCloudService.fetchMostRecentSensorData(sensorId);
      } else {
        records = await SolarCloudService.fetchSensorData(
          sensorId,
          startDt,
          endDt
        );
      }
      if (records?.length) {
        await SensorDataPeriod.bulkCreate(records, {
          ignoreDuplicates: true,
        }).catch((err) => {
          SlackService.logToSlack({
            title: `Failed to save recent Sungrow sensor data`,
            type: 'monitoring',
            data: [
              {
                label: 'Error',
                value: stringifyObject(err),
              },
              {
                label: 'Job Data',
                value: stringifyObject(job.data),
              },
            ],
          });
          done(new Error(`Error saving recent Sungrow sensor data: ${err}`));
          return null;
        });
      }
    } catch (error) {
      SlackService.logToSlack({
        title: `Failed to fetch recent Sungrow sensor data`,
        type: 'monitoring',
        data: [
          {
            label: 'Error',
            value: stringifyObject(error),
          },
          {
            label: 'Job Data',
            value: stringifyObject(job.data),
          },
        ],
      });
      done(new Error(`Error polling recent Sungrow sensor data: ${error}`));
      return null;
    }

    done();
    return null;
  });

  fetchSungrowInverterGenerationDataQueue.process(1, async (job, done) => {
    try {
      const { inverterId, startDt, endDt } = job.data;
      const records = await SolarCloudService.fetchInverterDailyGeneration(
        inverterId,
        startDt,
        endDt
      );
      for (let index = 0; index < records.length; index += 1) {
        const record = records[parseInt(index, 10)];
        // eslint-disable-next-line no-await-in-loop
        await saveUpdateDailyInverterGeneration(record, inverterId);
      }
    } catch (error) {
      SlackService.logToSlack({
        title: `Failed to backfill Sungrow inverter generation data`,
        type: 'monitoring',
        data: [
          {
            label: 'Error',
            value: stringifyObject(error),
          },
          {
            label: 'Job Data',
            value: stringifyObject(job.data),
          },
        ],
      });
      done(
        new Error(
          `Error backfilling Sungrow inverter generation data: ${error}`
        )
      );
      return null;
    }

    done();
    return null;
  });

  // NOTE: Only let the following queue process 1 job at a time for now.
  // The Solis API has a rate limit per endpoint and once we have multiple
  // projects polling at the same time we will pass this limit. Once we have
  // multiple projects, we can set it up to fetch multiple sites in 1
  // API fetch.
  pollRecentSolisGenerationDataQueue.process(1, async (job, done) => {
    const { solisSystemId } = job.data;
    const system = await SolisSystem.findByPk(solisSystemId, {
      include: [
        { attributes: ['id', 'name'], required: false, model: Project },
      ],
    });
    try {
      const historicalRecords =
        await SolisService.fetchMostRecentGenerationData(system);
      // Save each record synchronously because we don't want duplicate records so need to know what already exists
      for (let index = 0; index < historicalRecords.length; index += 1) {
        const record = historicalRecords[parseInt(index, 10)];
        // eslint-disable-next-line no-await-in-loop
        await SolisService.saveProductionPeriod(record, solisSystemId);
      }
    } catch (err) {
      done(
        new Error(
          `Error polling recent Solis System generation data. Err: ${err}`
        )
      );
      return null;
    }

    done();
    return null;
  });

  // NOTE: Only let the following queue process 1 job at a time for now.
  // The Solis API has a rate limit per endpoint since we have multiple
  // inverters polling at the same time we will pass this limit. Its
  // possible we can fetch multiple inverter data in one api request but
  // for now limit it to 1 at a time
  pollRecentSolisInverterSnapshotDataQueue.process(1, async (job, done) => {
    try {
      const { solisSystemId, inverterId } = job.data;
      const records = await SolisService.fetchMostRecentInverterSnapshotData(
        solisSystemId,
        inverterId
      );
      if (records?.length) {
        await InverterProductionPeriod.bulkCreate(records, {
          ignoreDuplicates: true,
        }).catch((err) => {
          SlackService.logToSlack({
            title: `Failed to save recent Solis inverter power data`,
            type: 'monitoring',
            data: [
              {
                label: 'Error',
                value: stringifyObject(err),
              },
              {
                label: 'Job Data',
                value: stringifyObject(job.data),
              },
            ],
          });
          done(
            new Error(`Error saving recent Solis inverter power data: ${err}`)
          );
          return null;
        });
      }
    } catch (error) {
      SlackService.logToSlack({
        title: `Failed to fetch recent Solis inverter power data`,
        type: 'monitoring',
        data: [
          {
            label: 'Error',
            value: stringifyObject(error),
          },
          {
            label: 'Job Data',
            value: stringifyObject(job.data),
          },
        ],
      });
      done(
        new Error(`Error polling recent Solis inverter power data: ${error}`)
      );
      return null;
    }

    done();
    return null;
  });

  // NOTE: Only let the following queue process 1 job at a time for now.
  // The Solis API has a rate limit per endpoint since we have multiple
  // inverters polling at the same time we will pass this limit. Its
  // possible we can fetch multiple inverter data in one api request but
  // for now limit it to 1 at a time
  backfillSolisInverterGenerationDataQueue.process(1, async (job, done) => {
    try {
      const { inverterId, startDt, endDt } = job.data;
      const records = await SolisService.fetchInverterDailyGenerationDataRange(
        inverterId,
        startDt,
        endDt
      );
      for (let index = 0; index < records.length; index += 1) {
        const record = records[parseInt(index, 10)];
        // eslint-disable-next-line no-await-in-loop
        await saveUpdateDailyInverterGeneration(record, inverterId);
      }
    } catch (error) {
      SlackService.logToSlack({
        title: `Failed to fetch recent Solis inverter generation data`,
        type: 'monitoring',
        data: [
          {
            label: 'Error',
            value: stringifyObject(error),
          },
          {
            label: 'Job Data',
            value: stringifyObject(job.data),
          },
        ],
      });
      done(
        new Error(
          `Error polling recent Solis inverter generation data: ${error}`
        )
      );
      return null;
    }

    done();
    return null;
  });

  // NOTE: Only let the following queue process 1 job at a time for now.
  // The Solis API has a rate limit per endpoint since we have multiple
  // sensors polling at the same time we will pass this limit. Its
  // possible we can fetch multiple sensors data in one api request but
  // for now limit it to 1 at a time
  pollRecentSolisSensorDataQueue.process(1, async (job, done) => {
    try {
      const { solisSystemId } = job.data;
      const records = await SolisService.fetchMostRecentSensorData(
        solisSystemId
      );
      if (records?.length) {
        await SensorDataPeriod.bulkCreate(records, {
          ignoreDuplicates: true,
        }).catch((err) => {
          SlackService.logToSlack({
            title: `Failed to save recent Solis sensor data`,
            type: 'monitoring',
            data: [
              {
                label: 'Error',
                value: stringifyObject(err),
              },
              {
                label: 'Job Data',
                value: stringifyObject(job.data),
              },
            ],
          });
          done(new Error(`Error saving recent Solis sensor data: ${err}`));
          return null;
        });
      }
    } catch (error) {
      SlackService.logToSlack({
        title: `Failed to fetch recent Solis sensor data`,
        type: 'monitoring',
        data: [
          {
            label: 'Error',
            value: stringifyObject(error),
          },
          {
            label: 'Job Data',
            value: stringifyObject(job.data),
          },
        ],
      });
      done(new Error(`Error polling recent Solis sensor data: ${error}`));
      return null;
    }

    done();
    return null;
  });

  syncHubSpotContactsQueue.process(maxJobsPerWorker, async (job, done) => {
    await HubSpotService.updateAllContactsFromDB();
    done();
  });

  syncHubSpotBrContactsQueue.process(maxJobsPerWorker, async (job, done) => {
    await BrContact.findAll({
      where: {
        hubSpotContactId: null,
        email: {
          [Op.not]: null,
        },
      },
    }).then(async (contacts) => {
      const aPromises = [];
      for (let index = 0; index < contacts.length; index += 1) {
        const contact = contacts[parseInt(index, 10)];
        if (index % 10 === 0) {
          // eslint-disable-next-line no-await-in-loop
          await wait(2000);
        }
        aPromises.push(
          HubSpotService.createContactFromDBUserCreditMgmt(contact)
        );
      }
      return Promise.all(aPromises);
    });
    await HubSpotService.updateAllContactsFromCreditMgmt();
    await BrConsumerUnit.findAll({
      attributes: ['id'],
      where: {
        hubSpotDealId: null,
      },
    }).then(async (brConsumerUnits) => {
      const aPromises = [];
      for (let index = 0; index < brConsumerUnits.length; index += 1) {
        const brConsumerUnit = brConsumerUnits[parseInt(index, 10)];
        if (index % 10 === 0) {
          // eslint-disable-next-line no-await-in-loop
          await wait(5000);
        }
        aPromises.push(
          HubSpotService.createDealFromBrConsumerUnitId(brConsumerUnit.id)
        );
      }
      return Promise.all(aPromises);
    });
    await HubSpotService.updateAllDealsFromCreditMgmt();
    await HubSpotService.updateAllContactDealAssociationsFromCreditMgmt();
    done();
  });

  syncHubSpotSubAccountsQueue.process(maxJobsPerWorker, async (job, done) => {
    await HubSpotService.updateAllSubAccountsFromDB();
    done();
  });

  pollRecentSolarEdgeSiteSensorDataQueue.process(2, async (job, done) => {
    try {
      const { solarEdgeSiteId } = job.data;
      const solarEdgeSite = await SolarEdgeSite.findByPk(solarEdgeSiteId);
      const records = await SolarEdgeService.fetchMostRecentSensorData(
        solarEdgeSite
      );
      // Save each record synchronously because we don't want duplicate records so need to know what already exists
      for (let index = 0; index < records.length; index += 1) {
        const record = records[parseInt(index, 10)];
        // eslint-disable-next-line no-await-in-loop
        await SolarEdgeService.saveSensorDataPeriod(record, solarEdgeSite.id);
      }
    } catch (err) {
      SlackService.logToSlack({
        title: `Failed to poll recent SolarEdge sensor data`,
        type: 'monitoring',
        data: [
          {
            label: 'Error',
            value: stringifyObject(err),
          },
        ],
      });
      done(
        new Error(`Error polling recent SolarEdge sensor data. Err: ${err}`)
      );
      return null;
    }

    done();
    return null;
  });

  pollRecentPowerFactorSensorDataQueue.process(
    maxJobsPerWorker,
    async (job, done) => {
      try {
        const { powerFactorSystemId, sensorId } = job.data;
        const records = await PowerFactorService.fetchMostRecentSensorData(
          powerFactorSystemId,
          sensorId
        );
        if (records?.length) {
          await SensorDataPeriod.bulkCreate(records, {
            ignoreDuplicates: true,
          });
        }
      } catch (err) {
        SlackService.logToSlack({
          title: `Failed to poll recent PowerFactor sensor data`,
          type: 'monitoring',
          data: [
            { label: 'Input', value: stringifyObject(job.data) },
            {
              label: 'Error',
              value: stringifyObject(err),
            },
          ],
        });
        done(
          new Error(`Error polling recent PowerFactor sensor data. Err: ${err}`)
        );
        return null;
      }

      done();
      return null;
    }
  );

  pollRecentSunExchangeSiteProductionDataQueue.process(2, async (job, done) => {
    try {
      const { sunExchangeSiteId } = job.data;
      const sunExchangeSite = await SunExchangeSite.findByPk(sunExchangeSiteId);
      const records = await SunExchangeService.fetchMostRecentProductionData(
        sunExchangeSite
      );
      // Save each record synchronously because we don't want duplicate records so need to know what already exists
      if (records?.length) {
        for (let index = 0; index < records.length; index += 1) {
          const record = records[parseInt(index, 10)];
          // eslint-disable-next-line no-await-in-loop
          await SunExchangeService.saveProductionPeriod(
            record,
            sunExchangeSite.id
          );
        }
      }
    } catch (error) {
      SlackService.logToSlack({
        title: `Failed to fetch recent SunExchange production data`,
        type: 'monitoring',
        data: [
          {
            label: 'Error',
            value: stringifyObject(error),
          },
        ],
      });
      done(
        new Error(`Error polling recent SunExchange production data: ${error}`)
      );
      return null;
    }

    done();
    return null;
  });

  pollRecentAMMPSensorDataQueue.process(2, async (job, done) => {
    try {
      const { sunExchangeSiteId } = job.data;
      const sunExchangeSite = await SunExchangeSite.findByPk(sunExchangeSiteId);
      const records = await SunExchangeService.fetchMostRecentSensorData(
        sunExchangeSite
      );
      if (records?.length) {
        await SensorDataPeriod.bulkCreate(records, { ignoreDuplicates: true });
      }
    } catch (error) {
      console.error(
        'Error polling recent AMMP sensor data',
        `sunExchangeSiteId: ${job?.data?.sunExchangeSiteId}`,
        error
      );
      // SlackService.logToSlack({
      //   title: `Failed to fetch recent AMMP sensor data`,
      //   type: 'monitoring',
      //   data: [
      //     {
      //       label: 'Error',
      //       value: stringifyObject(error),
      //     },
      //   ],
      // });
      done(new Error(`Error polling recent AMMP sensor data: ${error}`));
      return null;
    }

    done();
    return null;
  });

  pollRecentAMMPInverterPowerDataQueue.process(2, async (job, done) => {
    try {
      const { sunExchangeSiteId, inverterId } = job.data;
      const sunExchangeSite = await SunExchangeSite.findByPk(sunExchangeSiteId);
      const inverter = await Inverter.findByPk(inverterId);
      const records = await SunExchangeService.fetchMostRecentInverterPowerData(
        sunExchangeSite,
        inverter
      );
      if (records?.length) {
        await InverterProductionPeriod.bulkCreate(records, {
          ignoreDuplicates: true,
        }).catch((err) => {
          console.error(
            'Failed to save recent AMMP inverter power data.',
            `sunExchangeSiteId: ${sunExchangeSiteId}, inverterId: ${inverterId}`,
            err
          );
          // SlackService.logToSlack(
          //   title: `Failed to save recent AMMP inverter power data.`,
          //   type: 'monitoring',
          //   data: [
          //     {
          //       label: 'Error',
          //       value: stringifyObject(err),
          //     },
          //     {
          //       label: 'Job Data',
          //       value: stringifyObject(job.data),
          //     },
          //   ],
          // });
          done(
            new Error(`Error saving recent AMMP inverter power data: ${err}`)
          );
          return null;
        });
      }
    } catch (error) {
      console.error('Error polling recent AMMP inverter power data');
      // SlackService.logToSlack({
      //   title: `Failed to fetch recent AMMP inverter power data`,
      //   type: 'monitoring',
      //   data: [
      //     {
      //       label: 'Error',
      //       value: stringifyObject(error),
      //     },
      //     {
      //       label: 'Job Data',
      //       value: stringifyObject(job.data),
      //     },
      //   ],
      // });
      done(
        new Error(`Error polling recent AMMP inverter power data: ${error}`)
      );
      return null;
    }

    done();
    return null;
  });

  pollRecentAMMPInverterGenerationDataQueue.process(2, async (job, done) => {
    try {
      const { sunExchangeSiteId, inverterId, startDt, endDt } = job.data;
      const sunExchangeSite = await SunExchangeSite.findByPk(sunExchangeSiteId);
      const inverter = await Inverter.findByPk(inverterId);
      const records = await SunExchangeService.fetchDailyInverterGenerationData(
        sunExchangeSite,
        inverter,
        startDt,
        endDt
      );
      for (let index = 0; index < records.length; index += 1) {
        const record = records[parseInt(index, 10)];
        // eslint-disable-next-line no-await-in-loop
        await saveUpdateDailyInverterGeneration(record, inverterId);
      }
    } catch (error) {
      SlackService.logToSlack({
        title: `Failed to fetch recent AMMP inverter power data`,
        type: 'monitoring',
        data: [
          {
            label: 'Error',
            value: stringifyObject(error),
          },
          {
            label: 'Job Data',
            value: stringifyObject(job.data),
          },
        ],
      });
      done(
        new Error(`Error polling recent AMMP inverter power data: ${error}`)
      );
      return null;
    }

    done();
    return null;
  });

  pollRecentScadaSystemGenerationDataQueue.process(2, async (job, done) => {
    const { scadaSystemId } = job.data;
    const scadaSystem = await ScadaSystem.findByPk(scadaSystemId, {
      include: [{ model: Project, attributes: ['id', 'name'] }],
    });
    try {
      const records = await SCADAService.fetchMostRecentGenerationData(
        scadaSystem
      );
      // Save each record synchronously because we don't want duplicate records so need to know what already exists
      for (let index = 0; index < records.length; index += 1) {
        const record = records[parseInt(index, 10)];
        // eslint-disable-next-line no-await-in-loop
        await SCADAService.saveProductionPeriod(record, scadaSystem.id);
      }
    } catch (error) {
      SlackService.logToSlack({
        title: `Failed to fetch recent SCADA System site generation data for ${scadaSystem?.project?.name}`,
        type: 'monitoring',
        data: [
          {
            label: 'Error',
            value: stringifyObject(error),
          },
        ],
      });
    }

    done();
  });

  pollRecentScadaSystemInverterPowerDataQueue.process(2, async (job, done) => {
    const { scadaSystemId, inverterId } = job.data;
    const [scadaSystem, inverter] = await Promise.all([
      ScadaSystem.findByPk(scadaSystemId, {
        include: [{ model: Project, attributes: ['id', 'name'] }],
      }),
      Inverter.findByPk(inverterId),
    ]);
    try {
      const records = await SCADAService.fetchMostRecentInverterPowerData(
        scadaSystem,
        inverter
      ).catch((e) => {
        const msg = `Failed to fetch recent SCADA inverter records and existingProdPeriods for ${scadaSystem?.project?.name}. (scadaSystem id: ${scadaSystem?.id})`;
        console.warn(msg, e);
        throw new Error(msg);
      });
      if (records.length > 0) {
        await InverterProductionPeriod.bulkCreate(records, {
          // updateOnDuplicate: ['production', 'timeUnit', 'unit'],
          ignoreDuplicates: true,
        });
      }
      // for (let index1 = 0; index1 < records.length; index1 += 1) {
      //   const newRecord = records[parseInt(index1, 10)];
      //   // eslint-disable-next-line no-await-in-loop
      //   await SCADAService.saveInverterProductionPeriod(
      //     newRecord,
      //     existingProdPeriods
      //   ).catch((e) => {
      //     console.error('Error saving inverter production period', e);
      //   });
      // }
    } catch (err) {
      try {
        if (
          err &&
          !String(err).includes('Error connecting to SCADA MySQL database')
        ) {
          SlackService.logToSlack({
            title: `Error polling recent SCADA inverter power data for ${scadaSystem?.project?.name}`,
            type: 'monitoring',
            data: [
              {
                label: 'Error',
                value: stringifyObject(err),
              },
            ],
          });
          done(err);
        }
        console.error('HIT UNKNOWN INVERTER POLLING ERROR', err);
      } catch (err2) {
        const msg = `Error handling SCADA inverter power data error for ${scadaSystem?.project?.name}`;
        SlackService.logToSlack({
          title: msg,
          type: 'monitoring',
          data: [
            {
              label: 'Error',
              value: stringifyObject(err),
            },
            {
              label: 'Error2',
              value: stringifyObject(err2),
            },
          ],
        });
        console.error(msg, err2);
        done(err2);
      }
    }

    done();
  });

  pollRecentScadaSystemSensorDataQueue.process(2, async (job, done) => {
    const { scadaSystemId, sensorId } = job.data;
    const [scadaSystem, sensor] = await Promise.all([
      ScadaSystem.findByPk(scadaSystemId, {
        include: [{ model: Project, attributes: ['id', 'name'] }],
      }),
      Sensor.findByPk(sensorId),
    ]);

    try {
      const records = await SCADAService.fetchMostRecentSensorData(
        scadaSystem,
        sensor
      );
      // for (let index1 = 0; index1 < records.length; index1 += 1) {
      //   const newRecord = records[parseInt(index1, 10)];
      //   // eslint-disable-next-line no-await-in-loop
      //   await SCADAService.saveSensorDataPeriod(
      //     newRecord,
      //     existingSensorDataPeriods
      //   );
      // }
      if (records.length > 0) {
        await SensorDataPeriod.bulkCreate(records, { ignoreDuplicates: true });
      }
    } catch (err) {
      try {
        if (
          err &&
          !String(err).includes('Error connecting to SCADA MySQL database')
        ) {
          SlackService.logToSlack({
            title: `Error polling recent SCADA sensor data`,
            type: 'monitoring',
            data: [
              { label: 'Project', value: scadaSystem?.project?.name },
              {
                label: 'Error',
                value: stringifyObject(err),
              },
            ],
          });
          done(err);
        } else {
          console.error('Unknown error handling SCADA sensor data', err);
        }
      } catch (err2) {
        SlackService.logToSlack({
          title: `Error handling SCADA sensor data error`,
          type: 'monitoring',
          data: [
            {
              label: 'Error',
              value: stringifyObject(err),
            },
            {
              label: 'Error2',
              value: stringifyObject(err2),
            },
          ],
        });
        done(err2);
      }
    }

    done();
  });

  pollScadaSystemAlarmsQueue.process(2, async (job, done) => {
    const { scadaSystemId } = job.data;
    const scadaSystem = await ScadaSystem.findByPk(scadaSystemId, {
      include: [{ model: Project, attributes: ['id', 'name'] }],
    });
    try {
      const records = await SCADAService.fetchAlarms(scadaSystem);
      // Save each record synchronously because we don't want duplicate records so need to know what already exists
      for (let index = 0; index < records.length; index += 1) {
        const record = records[parseInt(index, 10)];
        // eslint-disable-next-line no-await-in-loop
        await SCADAService.saveAlarmRecord(record, scadaSystem);
      }
    } catch (error) {
      SlackService.logToSlack({
        title: `Error polling SCADA System alarms for project : ${scadaSystem?.project?.name}`,
        type: 'monitoring',
        data: [
          {
            label: 'Error',
            value: stringifyObject(error),
          },
        ],
      });
      done(new Error(`Error polling SCADA System alarms. Err: ${error}`));
      return null;
    }

    done();
    return null;
  });

  syncScadaSystemAlarmStatusesQueue.process(1, async (job, done) => {
    const { projectId } = job.data;
    const project = await Project.findByPk(projectId, {
      include: [
        {
          model: ScadaSystem,
          required: true,
          where: {
            isLivePollingEnabled: true,
          },
        },
      ],
    });
    try {
      if (project && project.scadaSystem) {
        await SCADAService.syncAlarmDates(project, project.scadaSystem);
      }
    } catch (error) {
      SlackService.logToSlack({
        title: `Error syncing SCADA System alarms for ${project?.name}`,
        type: 'monitoring',
        data: [
          {
            label: 'Error',
            value: stringifyObject(error),
          },
        ],
      });
      done(
        new Error(
          `Error syncing SCADA System alarms for ${project?.name}. Err: ${error}`
        )
      );
      return null;
    }

    done();
    return null;
  });

  backfillScadaSystemDailyInverterGenerationQueue.process(
    1,
    async (job, done) => {
      let updatedRecordCounter = 0;
      let newRecordCounter = 0;
      let duplicateRecordCounter = 0;

      try {
        const { scadaSystemId, inverterId, dateOverride } = job.data;
        const [scadaSystem, inverter] = await Promise.all([
          ScadaSystem.findByPk(scadaSystemId, {
            include: [{ model: Project, attributes: ['id', 'name'] }],
          }),
          Inverter.findByPk(inverterId),
        ]);

        const startDt = moment(dateOverride || new Date())
          .tz(scadaSystem.timezone)
          .add(-1, 'day')
          .startOf('day');
        const endDt = moment(dateOverride || new Date())
          .tz(scadaSystem.timezone)
          .add(-1, 'day')
          .endOf('day');
        const targetDtStr = moment(dateOverride || new Date())
          .tz(scadaSystem.timezone)
          .add(-1, 'day')
          .format('YYYY-MM-DD');
        const inverterGenerationDailyTotals =
          await SCADAService.fetchGenerationDataRangeForInverter(
            scadaSystem,
            inverter,
            startDt,
            endDt
          );
        const inverterGenerationFiltered = inverterGenerationDailyTotals.filter(
          (pt) => pt.date === targetDtStr
        );
        if (inverterGenerationFiltered.length !== 1) {
          console.error(
            `Length of daily inverter totals for inverter id ${inverterId} != 1 (${inverterGenerationFiltered.length}) - ${targetDtStr}`
          );
        } else {
          const { production, date } = inverterGenerationFiltered[0];
          const result = await saveUpdateDailyInverterGeneration(
            {
              production,
              date,
              inverterId,
            },
            inverterId
          );

          // Track record status based on the result
          if (result && result.status) {
            if (result.status === 'updated') {
              updatedRecordCounter++;
            } else if (result.status === 'created') {
              newRecordCounter++;
            } else if (result.status === 'duplicate') {
              duplicateRecordCounter++;
            }
          }
        }

        // Log to Slack on successful completion
        SlackService.logToSlack({
          title: `Completed SCADA daily inverter generation backfill for ${scadaSystem?.project?.name}`,
          type: 'monitoring',
          data: [
            {
              label: 'Inverter',
              value: `${inverter.name} (#${inverter.id})`,
            },
            {
              label: 'Date',
              value: String(targetDtStr),
            },
            {
              label: 'Updated Records',
              value: updatedRecordCounter,
            },
            {
              label: 'New Records',
              value: newRecordCounter,
            },
            {
              label: 'Duplicate Records',
              value: duplicateRecordCounter,
            },
          ],
        });
      } catch (error) {
        console.error(
          `Error fetching SCADA System daily inverter generation`,
          `scadaSystemId: ${job?.data?.scadaSystemId}`,
          error
        );
        SlackService.logToSlack({
          title: `Error fetching SCADA System daily inverter generation`,
          type: 'monitoring',
          data: [
            {
              label: 'Error',
              value: stringifyObject(error),
            },
            {
              label: 'Job Data',
              value: stringifyObject(job.data),
            },
          ],
        });
        done(
          new Error(
            `Error fetching SCADA System daily inverter generation. Err: ${error}`
          )
        );
        return null;
      }
      done();
      return null;
    }
  );

  pollRecentSolarEdgeSiteProductionDataQueue.process(2, async (job, done) => {
    try {
      const { solarEdgeSiteId } = job.data;
      const solarEdgeSite = await SolarEdgeSite.findByPk(solarEdgeSiteId);
      const records = await SolarEdgeService.fetchMostRecentProductionData(
        solarEdgeSite
      );
      // Save each record synchronously because we don't want duplicate records so need to know what already exists
      for (let index = 0; index < records.length; index += 1) {
        const record = records[parseInt(index, 10)];
        // eslint-disable-next-line no-await-in-loop
        await SolarEdgeService.saveProductionPeriod(record, solarEdgeSite.id);
      }
    } catch (err) {
      SlackService.logToSlack({
        title: `Failed to poll recent SolarEdge production data`,
        type: 'monitoring',
        data: [
          {
            label: 'Error',
            value: stringifyObject(err),
          },
        ],
      });
      done(new Error(err));
      return null;
    }

    done();
    return null;
  });

  pollRecentPowerFactorProductionDataQueue.process(
    maxJobsPerWorker,
    async (job, done) => {
      try {
        const { powerFactorSystemId } = job.data;
        const site = await PowerFactorSystem.findByPk(powerFactorSystemId);
        const records = await PowerFactorService.fetchMostRecentProductionData(
          site
        );
        // Save each record synchronously because we don't want duplicate records so need to know what already exists
        for (let index = 0; index < records.length; index += 1) {
          const record = records[parseInt(index, 10)];
          // eslint-disable-next-line no-await-in-loop
          await PowerFactorService.saveProductionPeriod(record, site.id);
        }
      } catch (err) {
        SlackService.logToSlack({
          title: `Failed to poll recent PowerFactor production data`,
          type: 'monitoring',
          data: [
            {
              label: 'Error',
              value: stringifyObject(err),
            },
          ],
        });
        done(new Error(err));
        return null;
      }

      done();
      return null;
    }
  );

  pollRecentPowerFactorInverterDataQueue.process(
    maxJobsPerWorker,
    async (job, done) => {
      try {
        const { inverterId } = job.data;
        const records =
          await PowerFactorService.fetchMostRecentInverterPowerData(inverterId);
        await InverterProductionPeriod.bulkCreate(records, {
          // updateOnDuplicate: ['value', 'timeUnit'],
          ignoreDuplicates: true,
        });
      } catch (err) {
        SlackService.logToSlack({
          title: `Failed to poll recent PowerFactor inverter data`,
          type: 'monitoring',
          data: [
            { label: 'Input', value: stringifyObject(job.data) },
            {
              label: 'Error',
              value: stringifyObject(err),
            },
          ],
        });
        done(new Error(err));
        return null;
      }

      done();
      return null;
    }
  );

  pollRecentSolarEdgeInverterProductionDataQueue.process(
    2,
    async (job, done) => {
      try {
        const { solarEdgeSiteId, inverterId } = job.data;
        const [solarEdgeSite, inverter] = await Promise.all([
          SolarEdgeSite.findByPk(solarEdgeSiteId),
          Inverter.findByPk(inverterId),
        ]);
        const records =
          await SolarEdgeService.fetchMostRecentInverterProductionData(
            solarEdgeSite,
            inverter.dataKey
          );
        records.forEach(async (record) => {
          const newRecord = record;
          newRecord.inverterId = inverter.id;
          await InverterProductionPeriod.findOne({
            where: {
              [Op.and]: [
                {
                  inverterId: inverter.id,
                },
                {
                  periodStartDt: newRecord.periodStartDt,
                },
              ],
            },
          }).then((res) => {
            if (res) {
              res.production = newRecord.production;
              res.unit = newRecord.unit;
              res.timeUnit = newRecord.timeUnit;
              return res.save().catch((error) => {
                SlackService.logToSlack({
                  title: `Error saving SolarEdge inverter production record`,
                  type: 'monitoring',
                  data: [
                    {
                      label: 'Error',
                      value: stringifyObject(error),
                    },
                  ],
                });
              });
            }
            return InverterProductionPeriod.create(newRecord).catch((error) => {
              SlackService.logToSlack({
                title: `Error creating SolarEdge inverter production period record`,
                type: 'monitoring',
                data: [
                  {
                    label: 'Error',
                    value: stringifyObject(error),
                  },
                ],
              });
            });
          });
        });
        // eslint-disable-next-line no-await-in-loop
        await sleep(500);
      } catch (err) {
        SlackService.logToSlack({
          title: `Failed to fetch recent SolarEdge inverter production data`,
          type: 'monitoring',
          data: [
            {
              label: 'Error',
              value: stringifyObject(err),
            },
          ],
        });
        done(
          new Error(
            `Error fetching recent SolarEdge inverter production data: ${err}`
          )
        );
        return null;
      }

      done();
      return null;
    }
  );

  backfillSolarEdgeDailyInverterGenerationDataQueue.process(
    2,
    async (job, done) => {
      try {
        const { solarEdgeSiteId, inverterId, startDt, endDt } = job.data;
        const [solarEdgeSite, inverter] = await Promise.all([
          SolarEdgeSite.findByPk(solarEdgeSiteId),
          Inverter.findByPk(inverterId),
        ]);
        const records =
          await SolarEdgeService.fetchDailyInverterGenerationDataRange(
            solarEdgeSite,
            inverter,
            startDt,
            endDt
          );
        for (let index = 0; index < records.length; index += 1) {
          const record = records[parseInt(index, 10)];
          // eslint-disable-next-line no-await-in-loop
          await saveUpdateDailyInverterGeneration(record, inverterId);
        }
        // eslint-disable-next-line no-await-in-loop
        await sleep(500);
      } catch (err) {
        SlackService.logToSlack({
          title: `Failed to fetch recent SolarEdge inverter production data`,
          type: 'monitoring',
          data: [
            {
              label: 'Error',
              value: stringifyObject(err),
            },
          ],
        });
        done(
          new Error(
            `Error fetching recent SolarEdge inverter production data: ${err}`
          )
        );
        return null;
      }

      done();
      return null;
    }
  );

  backfillSGDDailyInverterGenerationDataQueue.process(2, async (job, done) => {
    try {
      const { sgdSystemId, inverterId, startDt, endDt } = job.data;
      const records = await SGDService.fetchInverterDailyGeneration(
        sgdSystemId,
        inverterId,
        startDt,
        endDt
      );
      for (let index = 0; index < records.length; index += 1) {
        const record = records[parseInt(index, 10)];
        // eslint-disable-next-line no-await-in-loop
        await saveUpdateDailyInverterGeneration(record, inverterId);
      }
      // eslint-disable-next-line no-await-in-loop
      await sleep(500);
    } catch (err) {
      // Only log to Slack if it's not a known API connection error
      if (!isAPIConnectionError(err)) {
        SlackService.logToSlack({
          title: `Failed to fetch recent SGD inverter production data`,
          type: 'monitoring',
          data: [
            {
              label: 'Error',
              value: stringifyObject(err),
            },
          ],
        });
      } else {
        console.warn(
          'SGD API connection error (not logging to Slack):',
          String(err).substring(0, 200)
        );
      }
      done(
        new Error(`Error fetching recent SGD inverter production data: ${err}`)
      );
      return null;
    }

    done();
    return null;
  });

  pollRecentGreenAntMeterProductionDataQueue.process(2, async (job, done) => {
    await GreenAntMeter.findAll({
      where: {
        isLivePollingEnabled: true,
      },
    })
      .then((greenAntMeters) => {
        greenAntMeters.forEach(async (greenAntMeter) => {
          await GreenAntService.fetchMostRecentProductionData(
            greenAntMeter
          ).then(async (records) => {
            // Save each record synchronously because we don't want duplicate records so need to know what already exists
            for (let index = 0; index < records.length; index += 1) {
              const record = records[parseInt(index, 10)];
              // eslint-disable-next-line no-await-in-loop
              await GreenAntService.saveProductionPeriod(
                record,
                greenAntMeter.id
              );
            }
          });
        });
      })
      .catch((error) => {
        SlackService.logToSlack({
          title: `Failed to fetch recent GreenAnt production data`,
          type: 'monitoring',
          data: [
            {
              label: 'Error',
              value: stringifyObject(error),
            },
          ],
        });
      });

    done();
  });

  pushInvestmentDataToHubSpotQueue.process(1, async (job, done) => {
    const { userId, investmentId } = job.data;

    const user = await User.findByPk(userId, {
      attributes: ['id', 'hubSpotContactId'],
    });
    try {
      // NOTE: Don't do this here because its a lot of weight on the server for every single
      // dividend that is reinvested at once. This is done within a cron job multiple times a
      // day anyway.
      // await HubSpotService.updateUserTotalInvested({
      //   userId: user.id,
      //   hubSpotContactId: user.hubSpotContactId,
      // }).catch((error) =>
      //   SlackService.logToSlack({
      //     title: `Error updating HubSpot contact total invested for investment id#${investmentId}`,
      //     url: `${process.env.CMS_HOST}/Investment/${investmentId}`,
      //     data: [
      //       {
      //         label: 'Error',
      //         value: stringifyObject(error),
      //       },
      //       {
      //         label: 'User ID',
      //         value: String(userId),
      //       },
      //       {
      //         label: 'User CMS Page',
      //         value: `${process.env.CMS_HOST}/User/${userId}`,
      //       },
      //     ],
      //     type: 'platform-error',
      //   })
      // );
      await HubSpotService.createDealFromInvestmentId(investmentId).catch(
        (error) =>
          SlackService.logToSlack({
            title: `Error creating HubSpot Deal for investment id#${investmentId}`,
            url: `${process.env.CMS_HOST}/Investment/${investmentId}`,
            data: [
              {
                label: 'Error',
                value: stringifyObject(error),
              },
              {
                label: 'User ID',
                value: String(userId),
              },
              {
                label: 'User CMS Page',
                value: `${process.env.CMS_HOST}/User/${userId}`,
              },
            ],
            type: 'platform-error',
          })
      );
    } catch (error) {
      sendIssueEmail({
        description: 'Error pushing new investment data to HubSpot',
        oData: {
          error,
          user,
        },
      });
    }
    // Sleep for 1 second before calling done() to prevent too many hubspot requests.
    // The above HubSpotService calls make 5+ requests total so this 0.6 second sleep
    // will safely keep us inside the 100 requests per 10 seconds rate limit
    await sleep(600);

    done();
  });

  logInvestmentToSlackQueue.process(1, async (job, done) => {
    const {
      userId,
      investmentId,
      portfolioId,
      subAccountId,
      value,
      shares,
      firstName,
      lastName,
      portfolioSubtitle,
      capSpaceAfterInvestment,
      isReferral,
      isPromo,
      autoInvestSubscriptionId,
    } = job.data;

    try {
      // Fetch data that was previously queried synchronously
      const slackPromises = [];

      // Check if this is the user's first investment
      slackPromises.push(
        Investment.count({
          where: {
            userId,
            cancelledDt: null,
            createdAt: {
              [Op.lt]: moment().add(-10, 'seconds'),
            },
          },
        }).then((ct) => ct === 0)
      );

      // Get referral program balance if applicable
      slackPromises.push(
        isReferral || isPromo
          ? SellOrder.findByPk(constants.referralProgramSellOrderId, {
              attributes: ['id', 'shares'],
            }).then((referralProgramSellOrder) =>
              SellOrderService.soldShares(referralProgramSellOrder.id).then(
                (soldShares) => {
                  const sharePrice = value / shares;
                  const remainingShares =
                    referralProgramSellOrder.shares - soldShares;
                  return remainingShares * sharePrice;
                }
              )
            )
          : Promise.resolve(null)
      );

      // Get sub account name if applicable
      slackPromises.push(
        subAccountId
          ? SubAccount.findByPk(subAccountId, {
              attributes: [
                'id',
                'subAccountTypeId',
                'millenniumTrustAccountLast4',
                'iraType',
                'entrustAccountId',
                'otherAccountId',
              ],
            }).then((subAccount) => subAccount?.name || null)
          : Promise.resolve(null)
      );

      const [isFirstInvestment, referralProgramBalance, subAccountName] =
        await Promise.all(slackPromises);

      // Build Slack message data
      const slackData = [
        {
          label: 'User',
          value: `${firstName} ${lastName}`,
        },
        {
          label: 'Amount',
          value: numeral(value).format('$0,0.00'),
        },
        {
          label: 'Remaining Cap Space',
          value: numeral(capSpaceAfterInvestment).format('$0,0.00'),
        },
      ];

      if (subAccountName) {
        slackData.push({
          label: 'SubAccount',
          value: subAccountName,
        });
      }

      // Build emoji based on investment amount and type
      let emoji = '';
      if (value >= 500_000) {
        emoji += ':rocket:';
      } else if (value >= 100_000) {
        emoji += ':money_mouth_face:';
      } else if (value >= 10_000) {
        emoji += ':money_with_wings::money_with_wings::money_with_wings:';
      } else if (value >= 1_000) {
        emoji += ':money_with_wings::money_with_wings:';
      } else {
        emoji += ':money_with_wings:';
      }

      if (isFirstInvestment) {
        emoji += ':handshake:';
        slackData.push({
          label: 'Is First Investment?',
          value: String(isFirstInvestment),
        });
      }

      if (autoInvestSubscriptionId) {
        emoji += ':repeat:';
        slackData.push({
          label: 'Is Scheduled Investment?',
          value: String(!!autoInvestSubscriptionId),
        });
      }

      if (isReferral) {
        emoji += ':gift:';
        slackData.push({
          label: 'Is Referral Reward?',
          value: String(isReferral),
        });
        slackData.push({
          label: 'Referral Program Balance',
          value: String(numeral(referralProgramBalance).format('$0,0[.]00')),
        });
      }

      if (isPromo) {
        emoji += ':gift:';
        slackData.push({
          label: 'Is Promo Reward?',
          value: String(isPromo),
        });
        slackData.push({
          label: 'Referral Program Balance',
          value: String(numeral(referralProgramBalance).format('$0,0[.]00')),
        });
      }

      // Send Slack notification
      SlackService.logToSlack({
        title: `${emoji} New Investment of ${numeral(value).format(
          '$0,0[.]00'
        )} in ${portfolioSubtitle}`,
        url: `${process.env.CMS_HOST}/Investment/${investmentId}`,
        data: slackData,
        type: 'platform-event',
      });
    } catch (error) {
      console.error('Error in logInvestmentToSlack worker:', error);
      SlackService.logToSlack({
        type: 'platform-error',
        title: 'Error in logInvestmentToSlack worker',
        data: [
          {
            label: 'Error',
            value: stringifyObject(error),
          },
          {
            label: 'Investment ID',
            value: String(investmentId),
          },
        ],
      });
    }

    done();
  });

  checkForRecurringInvestmentSubscriptionsQueue.process(
    1 /* Should only run exactly once a day */,
    async (job, done) => {
      const whereClause = {
        inactive: {
          [Sequelize.Op.not]: true,
        },
        archivedDt: null,
      };
      if (job.data?.dayOfMonthOverride) {
        const currentDay = moment().date();
        if (job.data.dayOfMonthOverride > currentDay) {
          sendIssueEmail({
            description: `Blocked executing recurring investments for future day of month ${job.data.dayOfMonthOverride}`,
            oData: { jobData: job.data, currentDayOfMonth: currentDay },
          });
          done();
          return;
        }
        if (job.data.dayOfMonthOverride < currentDay) {
          // Don't execute subscriptions created after that day
          whereClause.createdAt = {
            [Op.lte]: moment()
              .startOf('day')
              .date(job.data.dayOfMonthOverride)
              .toDate(),
          };
        }
      }
      const autoInvestSubscriptions = await AutoInvestSubscription.findAll({
        where: whereClause,
        order: [['createdAt', 'ASC']],
      }).catch((e) =>
        console.error('Error collecting auto-invest subscriptions', e)
      );
      const returnObj = {
        newInvestments: 0,
        skipped: 0,
        errors: 0,
        totalInvested: 0,
        totalValueErrored: 0,
      };
      const dayOfMonth = job.data?.dayOfMonthOverride || moment().date();
      const blendedProductHistory = {};
      for (let index = 0; index < autoInvestSubscriptions.length; index++) {
        const autoInvestSubscription =
          autoInvestSubscriptions[parseInt(index, 10)];
        // eslint-disable-next-line no-await-in-loop
        const latestInvestment = await autoInvestSubscription.getInvestments({
          attributes: [
            [Sequelize.fn('max', Sequelize.col('startDt')), 'latestDt'],
          ],
          where: {
            cancelledDt: null,
          },
          raw: true,
        });
        // 1) check that the day = curDay and that the autoInvestSubscription wasn't created today
        if (
          autoInvestSubscription.dayOfMonth === dayOfMonth &&
          moment().tz('America/New_York').format('MM/DD/YYYY') !==
            moment(autoInvestSubscription.createdAt)
              .tz('America/New_York')
              .format('MM/DD/YYYY')
        ) {
          // 2) Check if Recurring investment already exists
          if (
            latestInvestment &&
            latestInvestment[0] &&
            moment().diff(moment(latestInvestment[0].latestDt), 'days') < 20
          ) {
            returnObj.errors += 1;
            sendIssueEmail({
              description:
                'Tried to make an auto-invest less than a month apart',
              oData: {
                latestInvestment,
                autoInvestSubscriptionId: autoInvestSubscription?.id,
              },
            });
            // eslint-disable-next-line no-continue
            continue;
          }
          const {
            blendedProductId,
            documentAwsObjectKey,
            eversignDocumentId,
            portfolioId,
            dwollaFundingSourceId,
            userId,
            value,
          } = autoInvestSubscription;

          const input = {
            fromAccountId: dwollaFundingSourceId,
            // subAccountId,
            portfolioId,
            autoInvestSubscriptionId: autoInvestSubscription.id,
            value,
            userId,
          };

          let blendedProductInvestmentId = null;
          if (blendedProductId) {
            const docKey = documentAwsObjectKey || eversignDocumentId;
            if (blendedProductHistory[String(docKey)]) {
              // A blended product auto-invest subscription with same documentId have been processed
              blendedProductInvestmentId =
                blendedProductHistory[String(docKey)];
            } else {
              // eslint-disable-next-line no-await-in-loop
              blendedProductInvestmentId =
                await BlendedProductInvestment.create({
                  blendedProductId,
                }).then((res) => res.id);
              blendedProductHistory[String(docKey)] =
                blendedProductInvestmentId;
            }
            input.blendedProductInvestmentId = blendedProductInvestmentId;
          }

          // 3) create the investment and send email if error occurs
          // eslint-disable-next-line no-await-in-loop
          await createInvestment(input).then(
            () => {
              returnObj.newInvestments += 1;
              returnObj.totalInvested += parseFloat(input.value);
            },
            (e) => {
              // NOTE: Removed this because we do not want to cancel all investments for scheduled investments if 1 fails.
              // if (blendedProductInvestmentId) {
              //   // Cancel other auto-investments for this blendedProductInvestment
              //   console.error("Error occurred creating scheduled investment for blended product. Cancelling all associated investments.".bold.red)
              //   InvestmentService.cancelBlendedProductInvestment(blendedProductInvestmentId)
              // }
              console.error('Error creating scheduled investment', e);
              returnObj.errors += 1;
              returnObj.totalValueErrored += parseFloat(input.value);
              sendIssueEmail({
                description: 'Uncaught error creating scheduled investment',
                oData: {
                  investmentData: input,
                  e,
                },
              });
            }
          );
        }
      }
      SlackService.logToSlack({
        title: `Recurring investments check on ${numeral(
          autoInvestSubscriptions.length
        ).format('0,0')} subscriptions.`,
        data: [
          {
            label: 'New Investments',
            value: returnObj.newInvestments,
          },
          {
            label: 'Total Invested',
            value: numeral(returnObj.totalInvested).format('$0,0[.]00'),
          },
          {
            label: 'Errors',
            value: returnObj.errors,
          },
          {
            label: 'Total value missed due to errors',
            value: numeral(returnObj.totalValueErrored).format('$0,0[.]00'),
          },
        ],
        type: 'platform-info',
      });
      if (returnObj.totalInvested > 0) {
        SlackService.logToSlack({
          title: `:spiral_calendar_pad: Recurring investments completed for ${moment()
            .date(dayOfMonth)
            .tz('America/New_York')
            .format('M/D/YYYY')}.`,
          data: [
            {
              label: 'Total Invested',
              value: numeral(returnObj.totalInvested).format('$0,0[.]00'),
            },
            {
              label: 'New Investments',
              value: returnObj.newInvestments,
            },
          ],
          type: 'platform-event',
        });
      }

      done();
    }
  );

  hubSpotUserLeadSourceSyncQueue.process(
    maxJobsPerWorker,
    async (job, done) => {
      const promises = [
        HubSpotLeadSource.findAll().then((sources) => {
          const map = {};
          sources.forEach((src) => {
            map[String(src.name)] = src.id;
          });
          return map;
        }),
        HubSpotService.batchGetUserContacts(['lead_source', 'email']),
      ];

      const [leadSourceMap, hubSpotContacts] = await Promise.all(promises);

      const createdLeadSourceNames = [];
      const usersWithoutLeadSources = [];
      const otherErrors = [];
      for (let index = 0; index < hubSpotContacts.length; index += 1) {
        const contact = hubSpotContacts[parseInt(index, 10)];
        // eslint-disable-next-line no-await-in-loop
        let dbUser = await User.findOne({
          attributes: [
            'id',
            'firstName',
            'lastName',
            'hubSpotLeadSourceId', // This is needed so we don't add a row to database change log if its unchanged
          ],
          where: {
            hubSpotContactId: contact.properties.hs_object_id,
          },
        });
        if (!dbUser) {
          // eslint-disable-next-line no-await-in-loop
          const dbUserByEmail = await User.findOne({
            attributes: [
              'id',
              'email',
              'hubSpotContactId',
              'hubSpotLeadSourceId', // This is needed so we don't add a row to database change log if its unchanged
            ],
            where: {
              email: {
                [Op.iLike]: contact.properties.email,
              },
            },
          });
          if (dbUserByEmail) {
            const dbUserWithUpdatedHubSpotId = dbUserByEmail;
            dbUserWithUpdatedHubSpotId.hubSpotContactId = String(
              contact.properties.hs_object_id
            );
            // eslint-disable-next-line no-await-in-loop
            await dbUserWithUpdatedHubSpotId.save().then((savedUser) => {
              console.log(`Updated hubSpotContactId for user ${savedUser.id}`);
              dbUser = savedUser;
            });
          } else {
            otherErrors.push(
              `No user for HubSpot contact with HubSpot contact id ${contact.properties.hs_object_id}`
            );
            // eslint-disable-next-line no-continue
            continue;
          }
        }

        const hsLeadSource = contact.properties.lead_source;
        if (!hsLeadSource) {
          usersWithoutLeadSources.push(
            `${dbUser.firstName} ${dbUser.lastName}`
          );
          // eslint-disable-next-line no-continue
          continue;
        }
        const lintedHSLeadSource = hsLeadSource.substring(0, 255); // Theres a Press release with a very long URL. No need to keep the whole thing and make this field a String(512)
        const saveObj = dbUser;
        if (!leadSourceMap[String(lintedHSLeadSource)]) {
          // eslint-disable-next-line no-await-in-loop
          const newLeadSource = await HubSpotLeadSource.create({
            name: lintedHSLeadSource,
          });
          leadSourceMap[String(lintedHSLeadSource)] = newLeadSource.id;
          saveObj.hubSpotLeadSourceId = newLeadSource.id;
          createdLeadSourceNames.push(newLeadSource.name);
        } else {
          saveObj.hubSpotLeadSourceId =
            leadSourceMap[String(lintedHSLeadSource)];
        }
        // eslint-disable-next-line no-await-in-loop
        await saveObj.save();
      }

      if (
        createdLeadSourceNames.length > 0 ||
        usersWithoutLeadSources.length > 0 ||
        otherErrors.length > 0
      ) {
        SlackService.logToSlack({
          type: 'platform-info',
          title: 'HubSpot Lead Sources Pulled to Energea DB',
          data: [
            {
              label: 'Users Without HubSpot Lead Sources',
              value: usersWithoutLeadSources.join('\n'),
            },
            {
              label: 'Lead Sources Created (assign these to categories)',
              value: createdLeadSourceNames.join('\n'),
            },
            {
              label: 'Other Errors',
              value: otherErrors.join('\n'),
            },
          ],
        });
      }
      done();
    }
  );

  cashoutSellOrderQueue.process(1, async (job, done) => {
    await SellOrderService.executeShareBuybackSellOrder(job.data.sellOrderId);
    done();
  });

  sellOrderHealthCheckQueue.process(maxJobsPerWorker, async (job, done) => {
    const sellOrders = await SellOrder.findAll();
    const slackData = [];
    const promises = sellOrders.map(async (sellOrder) => {
      const errors = [];

      const [
        soldShares,
        investmentDollarsInTransit,
        shareTransferDollarsInTransit,
        dwollaLabelBalance,
        daysInQueue,
        purchasedShareCount,
        soldShareCount,
      ] = await Promise.all([
        SellOrderService.soldShares(sellOrder.id),
        SellOrderService.investmentDollarsInTransit(sellOrder),
        SellOrderService.shareTransferDollarsInTransit(sellOrder),
        SellOrderService.dwollaLabelBalance(sellOrder),
        SellOrderService.daysInQueue(sellOrder),
        Investment.findAll({
          attributes: [
            [
              Sequelize.fn('sum', Sequelize.col('shares')),
              'purchasedShareTotal',
            ],
          ],
          where: {
            cancelledDt: null,
            portfolioId: sellOrder.portfolioId,
            userId: sellOrder.userId,
            subAccountId: sellOrder.subAccountId || null,
          },
        }).then((res) =>
          parseFloat(res[0].dataValues.purchasedShareTotal || 0)
        ),
        ShareTransfer.findAll({
          attributes: [
            [
              Sequelize.fn('sum', Sequelize.col('shareTransfer.soldShares')),
              'soldShareTotal',
            ],
          ],
          raw: true,
          include: [
            {
              model: Investment,
              required: true,
              attributes: [],
              where: {
                cancelledDt: null,
              },
            },
            {
              model: SellOrder,
              required: true,
              attributes: [],
              where: {
                portfolioId: sellOrder.portfolioId,
                userId: sellOrder.userId,
                subAccountId: sellOrder.subAccountId || null,
              },
            },
          ],
        }).then((res) => parseFloat(res[0].soldShareTotal || 0)),
      ]);

      const currentOwnedShares = purchasedShareCount - soldShareCount;
      const notYetSoldShares = sellOrder.shares - soldShares;
      const allSellOrderDollarsProcessed =
        investmentDollarsInTransit === 0 &&
        shareTransferDollarsInTransit === 0 &&
        notYetSoldShares < constants.shareCountFloor;

      if (notYetSoldShares < constants.shareCountFloor * -1) {
        errors.push(`- Sell order has sold more shares than requested.`);
      }
      if (sellOrder.closedDt && notYetSoldShares > constants.shareCountFloor) {
        errors.push(
          `- Sell order has a closedDt but sold shares is less than requested shares.`
        );
      }
      if (sellOrder.cancelledDt && soldShares > constants.shareCountFloor) {
        errors.push(
          `- Sell order is cancelled but has associated share transfers.`
        );
      }
      if (sellOrder.cancelledDt && dwollaLabelBalance > 0) {
        errors.push(
          `- Sell order is cancelled but has a non-zero label balance.`
        );
      }
      if (allSellOrderDollarsProcessed && dwollaLabelBalance > 0) {
        errors.push(
          `- All shares have been sold and transfers completed but still has non-zero label balance.`
        );
      }
      if (
        sellOrder.userId !== 76 &&
        !sellOrder.closedDt &&
        !sellOrder.inactiveFlg &&
        !sellOrder.cancelledDt &&
        sellOrder.id !== 132 && // This is Aaron Dirks large Project 1 sell order that will take a while to sell
        daysInQueue > 30
      ) {
        errors.push(`- Sell order has been in queue for more than 30 days.`);
      }
      if (allSellOrderDollarsProcessed && !sellOrder.closedDt) {
        errors.push(
          `- All shares have been sold and transfers completed but has not been flagged as closed.`
        );
      }
      if (
        !sellOrder.cancelledDt &&
        currentOwnedShares + constants.shareCountFloor < notYetSoldShares
      ) {
        errors.push(`- Seller doesn't own as many shares as are for sale.`);
      }
      if (errors.length > 0) {
        slackData.push({
          label: `Sell Order ID: ${sellOrder.id}`,
          value: errors.join('\n'),
        });
      }
    });
    await Promise.all(promises).then(() => {
      if (slackData.length > 0) {
        SlackService.logToSlack({
          type: 'platform-info',
          title: ':warning: Sell Order Health Check Errors :warning:',
          data: slackData,
        });
      }
    });
    done();
  });

  checkPreferredReceivingAccountQueue.process(
    maxJobsPerWorker,
    async (job, done) => {
      const { userId, preferredReceivingAccountId } = job.data;
      try {
        DwollaService.getDwollaFundingSource(preferredReceivingAccountId).catch(
          (err) => {
            console.error(err);
            sendIssueEmail({
              description:
                'Failed to fetch preferred receiving account in health check',
              oData: {
                userId,
                preferredReceivingAccountId,
                err,
              },
            });
          }
        );
      } catch (err) {
        console.error(err);
        sendIssueEmail({
          description:
            'Failed to fetch preferred receiving account in health check',
          oData: {
            userId,
            preferredReceivingAccountId,
            err,
          },
        });
      }

      done();
    }
  );

  fetchExchangeRatesQueue.process(1, async (job, done) => {
    const [countries, latestRateMap] = await Promise.all([
      Country.findAll({
        attributes: ['id', 'currencyCode'],
        where: {
          currencyCode: {
            [Op.not]: null,
          },
          autoUpdateExchangeRateFlg: true,
        },
      }),
      ExchangeRate.findAll({
        attributes: [
          [Sequelize.fn('max', Sequelize.col('asOfDt')), 'asOfDt'],
          'countryId',
        ],
        group: ['countryId'],
      }).then((res) =>
        ExchangeRate.findAll({
          attributes: ['countryId', 'exchangeRate'],
          where: {
            asOfDt: {
              [Op.in]: res.map((r) => r.asOfDt),
            },
          },
        }).then((latestExchangeRates) => {
          const map = {};
          latestExchangeRates.forEach((rate) => {
            map[String(rate.countryId)] = parseFloat(rate.exchangeRate);
          });
          return map;
        })
      ),
    ]);

    // https://openexchangerates.org/account/app-ids
    openExchangeRates.set({ app_id: process.env.OPEN_EXCHANGE_RATES_APP_ID });

    const aPromises = [];
    await openExchangeRates.latest(
      () => {
        // Apply exchange rates and base rate to `fx` library object:
        money.rates = openExchangeRates.rates;
        money.base = openExchangeRates.base;

        // money.js is ready to use:
        countries.forEach((country) => {
          const fxr = money(1).from('USD').to(country.currencyCode);
          if (!fxr) {
            console.error(
              `Invalid exchange rate conversion returned for ${country.currencyCode}`,
              fxr
            );
            SlackService.logToSlack({
              type: 'platform-error',
              title: `Invalid exchange rate conversion returned for ${country.currencyCode}`,
              data: [
                {
                  label: 'Conversion',
                  value: fxr,
                },
              ],
            });
            return;
          }
          aPromises.push(
            ExchangeRate.create({
              countryId: country.id,
              exchangeRate: fxr,
              asOfDt: new Date(),
            }).then((res) => {
              const prevExchangeRate = latestRateMap[String(country.id)];
              SlackService.logToSlack({
                type: 'platform-info',
                title: `Exchange rate for ${
                  country.currencyCode
                } updated from ${prevExchangeRate || 'Unknown'} to ${
                  res.exchangeRate
                }`,
                data: [
                  {
                    label: 'Rate Delta',
                    value: prevExchangeRate
                      ? res.exchangeRate - prevExchangeRate
                      : 'N/A',
                  },
                  {
                    label: 'Percentage Delta',
                    value: prevExchangeRate
                      ? `${
                          Math.abs(res.exchangeRate - prevExchangeRate) /
                          prevExchangeRate
                        }%`
                      : 'N/A',
                  },
                ],
              });
            })
          );
        });
      },
      (err) => {
        console.error(err);
        SlackService.logToSlack({
          type: 'platform-error',
          title: `Error requesting latest from openExchangeRates`,
          data: [
            {
              label: 'Error',
              value: stringifyObject(err),
            },
          ],
        });
      }
    );

    await Promise.all(aPromises);
    done();
  });

  pushUserPortfolioInvestmentTotalsToHubSpot.process(
    maxJobsPerWorker,
    async (job, done) => {
      const portfolioPropertyMap = {
        1: 'currentInvestedPortfolioId1',
        2: 'currentInvestedPortfolioId2',
        4: 'currentInvestedPortfolioId4',
        5: 'currentInvestedPortfolioId5',
        7: 'currentInvestedPortfolioId7',
        9: 'currentInvestedPortfolioId9',
        48: 'currentInvestedPortfolioId48',
      };

      const investors = await User.findAll({
        attributes: ['id', 'hubSpotContactId'],
        where: {
          hubSpotContactId: {
            [Op.not]: null,
          },
        },
        include: [
          {
            required: true,
            model: Investment,
            attributes: [],
            // NOTE: We don't filter by cancelledDt = null because we want to update it for users who had an investment which was cancelled also. This fixes a bug where if a user has all their investments cancelled, their values in hubspot don't get reset to 0
          },
        ],
      });

      const hsUpdateObjs = [];
      const updateObjPromises = [];
      for (let index = 0; index < investors.length; index += 1) {
        const dbUser = investors[parseInt(index, 10)];
        // eslint-disable-next-line no-await-in-loop
        const investments = await dbUser.getInvestments({
          attributes: ['portfolioId', 'value', 'startDt'],
          where: {
            cancelledDt: null,
          },
        });
        if (!dbUser.hubSpotContactId) {
          console.error(`User ${dbUser.id} does not have a hubSpotContactId`);
          continue;
        }

        const userPromises = [];
        const processingPortfolioIds = [];
        const hsContactUpdateObj = {
          id: dbUser.hubSpotContactId,
          properties: {
            currentInvestedPortfolioId2: 0,
            currentInvestedPortfolioId5: 0,
            currentInvestedPortfolioId9: 0,
            currentInvestedPortfolioId7: 0,
            currentInvestedPortfolioId48: 0,
            currentInvestedPortfolioId1: 0,
            currentInvestedPortfolioId4: 0,
          },
        };
        let totalInvested = 0;
        let firstInvestmentDt = null;
        investments.forEach((investment) => {
          totalInvested += parseFloat(investment.value);
          if (!firstInvestmentDt || firstInvestmentDt > investment.startDt) {
            firstInvestmentDt = investment.startDt;
          }
          if (processingPortfolioIds.indexOf(investment.portfolioId) === -1) {
            processingPortfolioIds.push(investment.portfolioId);
            userPromises.push(
              UserService.currentTotalInvested({
                userId: dbUser.id,
                portfolioId: investment.portfolioId,
              }).then((currentTotalInvested) => {
                hsContactUpdateObj.properties[
                  portfolioPropertyMap[String(investment.portfolioId)]
                ] = Math.round(currentTotalInvested * 100) / 100;
              })
            );
          }
        });

        updateObjPromises.push(
          Promise.all(userPromises).then(() => {
            hsContactUpdateObj.properties.totalInvested =
              Math.round(totalInvested * 100) / 100;
            hsContactUpdateObj.properties.hasInvested = totalInvested > 0;
            hsContactUpdateObj.properties.firstInvestmentDt = firstInvestmentDt;
            hsUpdateObjs.push(hsContactUpdateObj);
          })
        );
      }
      await Promise.all(updateObjPromises).then(() => {
        HubSpotService.batchUpdateContacts(hsUpdateObjs);
      });

      done();
    }
  );

  pushInvestorRecapEmailKPIsToHubSpotQueue.process(1, async (job, done) => {
    const BATCH_SIZE = 100;

    // NOTE: Assumes this is ran at the start of the month after the one we are generating the Monthly Investor Recap email for.
    // Running this on Nov 1st or Nov 30th will calculate numbers as if ran Nov 1st at 12am and give October numbers as 'last months' data.
    const startOfCurrMonth = new Date(moment().startOf('month'));
    const startOfPrevMonth = new Date(
      moment(startOfCurrMonth).add(-1, 'month')
    );
    const dbUsers = await User.findAll({
      attributes: ['id'],
      where: {
        hubSpotContactId: {
          [Op.not]: null,
        },
      },
      order: [['id', 'asc']],
      include: [
        {
          required: true,
          attributes: [],
          model: Investment,
          where: {
            cancelledDt: null,
            startDt: {
              [Op.lt]: startOfCurrMonth,
            },
          },
        },
      ],
    });

    const queue = new Queue('handleInvestorRecapKPIsBatch', bullOptions);
    for (let i = 0; i < dbUsers.length; i += BATCH_SIZE) {
      const batch = dbUsers.slice(i, i + BATCH_SIZE);

      // eslint-disable-next-line no-await-in-loop
      await queue.add({
        userIds: batch.map((dbUser) => dbUser.id),
        currMonth: startOfCurrMonth,
        prevMonth: startOfPrevMonth,
        batchNumber: i / BATCH_SIZE + 1,
        batchCount: Math.ceil(dbUsers.length / BATCH_SIZE),
      });
    }
    queue.close();

    done();
  });

  handleInvestorRecapKPIsBatchQueue.process(1, async (job, done) => {
    const { batchNumber, batchCount, userIds, currMonth, prevMonth } = job.data;
    const startOfCurrMonth = new Date(currMonth);
    const startOfPrevMonth = new Date(prevMonth);

    let lintedUserIds = null;
    if (typeof userIds === 'string') {
      lintedUserIds = userIds.split(',');
    } else {
      lintedUserIds = userIds;
    }

    console.log(
      `Processing investor recap KPIs batch #${batchNumber}/${batchCount}. User ID ${
        lintedUserIds[0]
      } to ${
        lintedUserIds[lintedUserIds.length - 1]
      }. Timestamp: ${new Date().getTime()}`
    );

    const dbUsers = await User.findAll({
      attributes: ['id', 'firstName', 'lastName', 'hubSpotContactId'],
      where: {
        id: {
          [Op.in]: lintedUserIds,
        },
      },
    });
    const hsUpdateObjs = [];
    for (let index = 0; index < dbUsers.length; index += 1) {
      const dbUser = dbUsers[parseInt(index, 10)];

      if (!dbUser.hubSpotContactId) {
        console.error(`User ${dbUser.id} does not have a hubSpotContactId`);
        continue;
      }

      const aPromises = [];
      // showThisMonthsIRR
      aPromises.push(
        UserService.showIRR(
          dbUser,
          {
            asOfDate: startOfCurrMonth,
          },
          {
            models: database.models,
          }
        )
      );
      // showLastMonthsIRR
      aPromises.push(
        UserService.showIRR(
          dbUser,
          {
            asOfDate: startOfPrevMonth,
          },
          {
            models: database.models,
          }
        )
      );
      // irrToThisMonth
      aPromises.push(
        UserService.navBasedIRR(
          { user: dbUser },
          { endDt: startOfCurrMonth }
        ).catch(() => '--')
      );
      // irrToLastMonth
      aPromises.push(
        UserService.navBasedIRR(
          { user: dbUser },
          { endDt: startOfPrevMonth }
        ).catch(() => '--')
      );
      // dividendsToDate
      aPromises.push(
        UserService.dividendsReceived(dbUser, {
          endDt: startOfCurrMonth,
        })
      );
      // dividendsLastMonth
      aPromises.push(
        UserService.dividendsReceived(dbUser, {
          startDt: startOfPrevMonth,
          endDt: startOfCurrMonth,
        })
      );
      // cleanEnergyProducedToDate
      aPromises.push(
        UserService.cleanEnergyProduced(
          dbUser,
          {
            endDt: startOfCurrMonth,
          },
          {
            models: database.models,
          }
        )
      );
      // cleanEnergyProducedLastMonth
      aPromises.push(
        UserService.cleanEnergyProduced(
          dbUser,
          {
            startDt: startOfPrevMonth,
            endDt: startOfCurrMonth,
          },
          {
            models: database.models,
          }
        )
      );

      const [
        showThisMonthsIRR,
        showLastMonthsIRR,
        irrToThisMonth,
        irrToLastMonth,
        dividendsToDate,
        dividendsLastMonth,
        cleanEnergyProducedToDate,
        cleanEnergyProducedLastMonth,
      ] = await Promise.all(aPromises); // eslint-disable-line no-await-in-loop
      // NOTE: We are purposefully awaiting inside a for loop rather than pushing to
      // a promise list to await after all users have pushed because we run out of memory
      // with so many promises for so many users. The main culprit here for out of memory
      // errors is cleanEnergyProduced because it loops through every actual and does 4 fetches for each one.
      // This hubspot kpi push runs once a month and is okay to be slow. Doing this will save us $/month on
      // staying with a cheap worker node rather than scaling up with our user count and more actuals.
      // MATH: 500 investors * 2 calls to cleanEnergyProduced * 4 db findAll fetches in clean energy produced * x actuals since user first invested = so much greater than 10,000 promises all working at once

      const thisMonthsIRROutOfRange =
        (irrToThisMonth >= constants.irrUpperBound ||
          irrToThisMonth <= constants.irrLowerBound) &&
        dbUser.id !== 76;
      const lastMonthsIRROutOfRange =
        (irrToLastMonth >= constants.irrUpperBound ||
          irrToLastMonth <= constants.irrLowerBound) &&
        dbUser.id !== 76;

      hsUpdateObjs.push({
        id: dbUser.hubSpotContactId,
        properties: {
          cleanEnergyProducedToDate,
          cleanEnergyProducedLastMonth,
          dividendsToDate: numeral(dividendsToDate).format('0,0.00'),
          dividendsLastMonth: numeral(dividendsLastMonth).format('0,0.00'),
          irrToThisMonth: thisMonthsIRROutOfRange ? '--' : irrToThisMonth,
          irrToLastMonth: lastMonthsIRROutOfRange ? '--' : irrToLastMonth,
        },
      });
    }

    // eslint-disable-next-line no-await-in-loop
    await HubSpotService.batchUpdateContacts(hsUpdateObjs);

    SlackService.logToSlack({
      type: 'platform-info',
      title: 'Monthly Investor Recap KPIs pushed to HubSpot',
      data: [
        {
          label: 'Investors Updated Count',
          value: hsUpdateObjs.length,
        },
        {
          label: 'Batch #',
          value: `${batchNumber}/${batchCount}`,
        },
      ],
    });
    done();
  });

  manageQuarterlyStatementJobsQueue.process(1, async (job, done) => {
    const { userId, quarter, batchCreate, overwriteExistingStatements } =
      job.data;

    const [year, quarterValue] = quarter.split('-Q');
    const quarterEndDt = new Date(year, quarterValue * 3, 0).setUTCHours(
      23,
      59,
      59,
      999
    );

    const aPromises = [];
    // TODO: this investors call gets users "createdAt" during the quarter that have made sendNewInvestmentStateEmail...this logic includes someone that creates an account during the quarter and then invests after the quarter ends, but before we created the statements.
    aPromises.push(
      User.findAll({
        where: {
          [Op.and]: [
            {
              dwollaId: {
                [Op.not]: null,
              },
            },
            {
              createdAt: {
                [Op.lte]: quarterEndDt,
              },
            },
            batchCreate
              ? null
              : {
                  id: userId,
                },
          ],
          permSuspendedFlg: {
            [Op.not]: true,
          },
        },
        attributes: ['id'],
        include: [
          {
            where: {
              cancelledDt: null,
            },
            model: Investment,
            required: true,
            attributes: [],
          },
        ],
      })
    );

    const [investors] = await Promise.all(aPromises);

    const statementResultPromises = [];
    const queue = new Queue('createQuarterlyStatements', bullOptions);
    investors.forEach((investor) => {
      statementResultPromises.push(
        queue
          .add({
            userId: investor.id,
            quarter,
            quarterEndDt,
            overwriteExistingStatements,
          })
          .then(
            (j) => j.finished(),
            (err) => {
              console.error(
                'Error adding quarterly statements to queue for individual account',
                err
              );
            }
          )
      );
    });

    Promise.all(statementResultPromises).then(
      (results) => {
        let statementsCreated = 0;
        const errorMessages = [];
        results.forEach((res) => {
          if (res.status === 'created') {
            statementsCreated += 1;
          } else {
            errorMessages.push(res.errorMsg);
          }
        });
        SlackService.logToSlack({
          type: 'platform-info',
          title: 'Quarterly Financial Statements Uploaded',
          data: [
            {
              label: 'Quarter',
              value: quarter,
            },
            {
              label: 'Status',
              value: `Created ${numeral(statementsCreated).format(
                '0,0'
              )} statement(s) for ${numeral(investors.length).format(
                '0,0'
              )} investor(s)`,
            },
            {
              label: 'Errors',
              value:
                errorMessages.length > 0 ? errorMessages.join('\n') : 'None',
            },
          ],
        });
        queue.close();
      },
      (err) => {
        console.error('Error creating quarterly statements', err);
      }
    );

    done();
  });

  createQuarterlyStatementsQueue.process(1, async (job, done) => {
    const { userId, quarter, quarterEndDt, overwriteExistingStatements } =
      job.data;

    console.log(
      `Processing quarterly statement data: ${stringifyObject(job.data)}`
    );

    const investor = await User.findByPk(userId, {
      attributes: [
        'id',
        'dwollaId',
        'dwollaBalanceId',
        'firstName',
        'lastName',
        'email',
        'address1',
        'address2',
        'city',
        'state',
        'postalCode',
        'createdAt',
        'type',
        'businessName',
      ],
      include: [
        {
          model: QuarterlyUserFinancialStatement,
          // TODO: add attributes
          required: false,
          where: {
            quarter,
          },
        },
        {
          // NOTE: This is a quick way to get a more than likely correct answer on whether a user has
          // holdings and should get a statement. If none are returned here, we will do a more
          // in-depth check below
          model: Dividend,
          attributes: ['id'],
          required: false,
          where: {
            date: {
              [Op.between]: [
                moment(quarterEndDt).startOf('quarter').format('YYYY-MM-DD'),
                moment(quarterEndDt).endOf('quarter').format('YYYY-MM-DD'),
              ],
            },
          },
        },
      ],
    });

    const { id, quarterlyUserFinancialStatements } = investor;

    // NOTE: We don't want to create statements for users who have fully exited
    // and whose account is essentially closed/inactive. Having no dividends the
    // entire last quarter will be a good indicator of this but is not 100% bullet
    // proof. The below checks will be more bullet proof but take a bit longer to
    // run so we don't want to run them for every user - just those with no dividends
    // in the last quarter.
    if (investor.dividends.length === 0) {
      const [
        investedDuringQuarter,
        soldDuringQuarter,
        receivedDividendDuringQuarter,
        navAtEndOfQuarter,
      ] = await Promise.all([
        Investment.count({
          where: {
            userId,
            cancelledDt: null,
            startDt: {
              [Op.between]: [
                moment(quarterEndDt).startOf('quarter').format('YYYY-MM-DD'),
                moment(quarterEndDt).endOf('quarter').format('YYYY-MM-DD'),
              ],
            },
          },
        }).then((count) => count > 0),
        ShareTransfer.count({
          where: {
            sellDt: {
              [Op.between]: [
                moment(quarterEndDt).startOf('quarter').format('YYYY-MM-DD'),
                moment(quarterEndDt).endOf('quarter').format('YYYY-MM-DD'),
              ],
            },
          },
          include: [
            {
              model: Investment,
              attributes: [],
              required: true,
              where: {
                cancelledDt: null,
              },
            },
            {
              model: SellOrder,
              attributes: [],
              required: true,
              where: {
                userId,
              },
            },
          ],
        }).then((count) => count > 0),
        Dividend.count({
          where: {
            userId,
            date: {
              [Op.between]: [
                moment(quarterEndDt).startOf('quarter').format('YYYY-MM-DD'),
                moment(quarterEndDt).endOf('quarter').format('YYYY-MM-DD'),
              ],
            },
          },
        }).then((count) => count > 0),
        UserService.getNAV(investor, { endDt: quarterEndDt }),
      ]);
      if (
        !investedDuringQuarter &&
        !soldDuringQuarter &&
        !receivedDividendDuringQuarter &&
        navAtEndOfQuarter === 0
      ) {
        done(null, {
          errorMsg: `No activity and zero NAV for user ${id} this quarter.`,
          status: 'error',
        });
        return;
      }
    }

    try {
      // TODO: Dwolla created date is more accurate than user createdAt, but hurts performance
      // const dwollaIdCreatedDt = await getDwollaIdCreatedDt(dwollaId);
      const dwollaIdCreatedDt = investor.createdAt;
      if (
        !dwollaIdCreatedDt ||
        (dwollaIdCreatedDt && quarterEndDt < dwollaIdCreatedDt)
      ) {
        done(null, {
          errorMsg: `DwollaId didn't exist for user ${id} this quarter.`,
          status: 'error',
        });
        return;
      }

      const existingStatement = quarterlyUserFinancialStatements[0];
      if (existingStatement && !overwriteExistingStatements) {
        done(null, {
          errorMsg: `Statement already exists for user ${id}.`,
          status: 'error',
        });
        return;
      }

      const pass = new PassThrough();
      const doc = await createQuarterlyUserLedger(investor, quarter).catch(
        (err) => {
          console.error(err);
          return null;
        }
      );
      if (!doc || !doc.pipe) {
        done(null, {
          errorMsg: `Error creating statement for userId: ${userId}`,
          status: 'error',
        });
        return;
      }
      doc.pipe(pass);
      pass.on('error', (err) => {
        console.error('File Error', err);
        SlackService.logToSlack({
          type: 'platform-info', // sending to platform info because thats where quarterly statement success message will go also
          title: 'Error creating Quarterly Statement stream',
          data: [
            {
              label: 'User',
              value: userId,
            },
            {
              label: 'Quarter',
              value: quarter,
            },
            {
              label: 'Error',
              value: stringifyObject(err),
            },
          ],
        });
      });
      // NOTE: maybe this should wrap the rest of this function in a pass.on "success" action

      const awsObjectKey = lintAwsObjectKey(
        `${id}/QuarterlyFinancialStatements/${quarter}-Financial-Statement.pdf`
      );

      if (existingStatement) {
        uploadObjectToS3(awsObjectKey, pass);
        existingStatement.awsObjectKey = awsObjectKey;
        existingStatement.changed('updatedAt', true);
        existingStatement.save();
      } else {
        uploadObjectToS3(awsObjectKey, pass);
        const input = {
          userId: id,
          awsObjectKey,
          quarter,
        };
        QuarterlyUserFinancialStatement.create(input);
      }

      done(null, {
        errorMsg: null,
        status: 'created',
      });
      return;
    } catch (err) {
      const msg = `Unknown error occurred creating statement for userId ${userId}`;
      console.error(msg, err);
      done(null, {
        errorMsg: msg,
        status: 'error',
      });
    }
  });

  reinvestDividendsQueue.process(
    1 /* only process one job at a time for cap space purposes */,
    async (job, done) => {
      console.log(
        `Processing Dividend (${
          job.data.dividendId
        }) Flagged for Auto-Reinvest in ${
          job.data.portfolioName
        } at ${moment().format('YYYY-MM-DD HH:mm:ss')} (${
          job.data.progress?.index || 'UNKNOWN'
        } / ${job.data.progress?.total || 'UNKNOWN'})`
      );
      // Step 1) Fetch dividend record
      const dividend = await Dividend.findByPk(job.data.dividendId, {
        include: [
          {
            model: MonthlyPortfolioFinancialActual,
            attributes: ['id', 'portfolioId', 'investorNotes'],
            required: true,
            include: [
              {
                model: Portfolio,
                required: true,
                attributes: ['id', 'subtitle', 'dwollaFundingSourceId'],
              },
            ],
          },
          {
            model: SubAccount,
            attributes: ['id'],
            required: false,
          },
          {
            model: User,
            required: true,
            attributes: ['firstName', 'lastName', 'email', 'id'],
          },
          {
            model: Investment,
            required: false,
            attributes: ['id', 'startDt'],
          },
          {
            model: Transfer,
            required: false,
            attributes: ['id', 'createdAt'],
          },
        ],
      }).catch((error) => {
        const errorMsg = `Failed to fetch dividend with id: ${job.data.dividendId}. Error: ${error}`;
        console.error(errorMsg);
        sendIssueEmail({
          description: `Error Reinvesting Dividend ${job.data.dividendId}`,
          oData: {
            msg: errorMsg,
          },
        });
        done(null, {
          dividendId: job.data.dividendId,
          errorMsg,
          status: 'error',
        });
        return null;
      });

      // Step 2) Make sure dividend exists
      if (!dividend) {
        const errorMsg = `No dividend found with id ${job.data.dividendId}`;
        console.error(errorMsg);
        done(null, {
          dividendId: job.data.dividendId,
          errorMsg,
          status: 'error',
        });
        return;
      }
      // Step 3) Make sure dividend hasn't already been reinvested
      if (dividend.investment) {
        const errorMsg = `Dividend ${
          dividend.id
        } has already be reinvested on ${moment(
          dividend.investment.startDt
        ).format('YYYY-MM-DD HH:mm:ss')}`;
        console.error(errorMsg);
        sendIssueEmail({
          description: 'CAUGHT A DOUBLE FIRED DIVIDEND REINVESTMENT!',
          oData: {
            dividend,
            investment: dividend.investment,
          },
        });
        done(null, {
          dividendId: job.data.dividendId,
          errorMsg,
          status: 'error',
        });
        return;
      }

      // Step 3a) Make sure dividend hasn't already been transferred
      if (dividend.transfer) {
        const errorMsg = `Dividend ${
          dividend.id
        } has already been transferred on ${moment(
          dividend.transfer.createdAt
        ).format('YYYY-MM-DD HH:mm:ss')}`;
        console.error(errorMsg);
        sendIssueEmail({
          description: 'CAUGHT A DOUBLE FIRED DIVIDEND REINVESTMENT!',
          oData: {
            dividend,
            transfer: dividend.transfer,
          },
        });
        done(null, {
          dividendId: job.data.dividendId,
          errorMsg,
          status: 'error',
        });
        return;
      }

      const subAccountId = dividend.subAccount ? dividend.subAccount.id : null;
      const portfolioId = dividend.monthlyPortfolioFinancialActual.portfolio.id;
      const dividendId = dividend.id;
      const { userId, value } = dividend;

      // Step 4) Make sure user, dividend, and portfolio exist
      if (!userId || !dividendId || !portfolioId) {
        done(null, {
          dividendId: job.data.dividendId,
          errorMsg: `Cant reinvest dividend since userId, dividendId, or portfolioId are null. ${stringifyObject(
            { userId, dividendId, portfolioId }
          )}`,
          status: 'error',
        });
        return;
      }
      const {
        monthlyPortfolioFinancialActual,
        monthlyPortfolioFinancialActual: { portfolio },
        user: { email, firstName, lastName },
      } = dividend;

      // Step 5) Get autoReinvestIndicator
      const autoReinvestIndicator = await AutoReinvestIndicator.findOne({
        attributes: ['id', 'percentage', 'reinvestIntoPortfolioId'],
        where: {
          userId,
          portfolioId,
          subAccountId,
          inactive: {
            [Op.not]: true,
          },
          percentage: {
            [Op.gt]: 0,
          },
        },
        limit: 1,
      });
      if (!autoReinvestIndicator) {
        done(null, {
          dividendId: job.data.dividendId,
          errorMsg: `No active autoReinvestIndicator found for this user in this portfolio. ${stringifyObject(
            { userId, dividendId, portfolioId }
          )}`,
          status: 'error',
        });
        return;
      }

      let investmentValue = value;
      let dividendTransferValue = 0;
      if (autoReinvestIndicator.percentage < 100) {
        investmentValue =
          Math.ceil(value * autoReinvestIndicator.percentage) / 100;
        dividendTransferValue = Math.max(value - investmentValue, 0);
      }

      // Step 6) Create investment
      const input = {
        subAccountId,
        portfolioId,
        dividendId,
        value: investmentValue,
        userId,
        reinvestmentFlg: true,
      };
      try {
        const reinvestingIntoDifferentPortfolio =
          autoReinvestIndicator.reinvestIntoPortfolioId &&
          autoReinvestIndicator.reinvestIntoPortfolioId !== portfolioId;
        if (reinvestingIntoDifferentPortfolio) {
          input.portfolioId = autoReinvestIndicator.reinvestIntoPortfolioId;
          input.fromAccountId = portfolio.dwollaFundingSourceId;
        }
        const newInvestment = await createInvestment(input);
        if (newInvestment instanceof Error) {
          throw newInvestment;
        }
        if (reinvestingIntoDifferentPortfolio) {
          const receivingPortfolio = await Portfolio.findByPk(
            autoReinvestIndicator.reinvestIntoPortfolioId,
            {
              attributes: ['dwollaFundingSourceId'],
            }
          );
          await DwollaService.transferFunds({
            fromAccountId: portfolio.dwollaFundingSourceId,
            toAccountId: receivingPortfolio.dwollaFundingSourceId,
            amount: newInvestment.value,
            investmentId: newInvestment.id,
            userId,
            type: 'investment',
          });
        }

        if (dividendTransferValue === 0) {
          // Step 6a) Send auto reinvest email
          // NOTE: With the transition to one consolidated dividend summary email, we not longer want to send this
          // sendAutoReinvestDividendTransferCompleted({
          //   firstName,
          //   lastName,
          //   email,
          //   shares: formatShares(newInvestment.shares),
          //   sharePrice: `${numeral(
          //     newInvestment.value / newInvestment.shares
          //   ).format('$0,0.000')} / share`,
          //   portfolioName: portfolio.subtitle,
          //   dividendValue: numeral(newInvestment.value).format('$0,0.00'),
          //   investorNotes: monthlyPortfolioFinancialActual.investorNotes,
          // });
          // Step 6b) Create notification
          await EventNotification.create({
            autoReinvestInvestmentId: newInvestment.id,
            transferStatus: 'processed',
            userId,
          }).then(
            (res) => res,
            (e) =>
              console.error(
                `Error creating notification for reinvestment with investment id: ${newInvestment.id}`,
                e
              )
          );
        }
      } catch (error) {
        // Step 6a error) Transfer the WHOLE dividend to the users account instead. We do the whole dividend value so that we don't need to create two partial dividend bank transfers
        console.warn(
          `Error auto-reinvesting dividend with ID: ${job.data.dividendId}. Issuing transfer...`,
          `${firstName} ${lastName}`,
          `Dividend ID: ${dividend.id}`,
          error
        );

        try {
          const dividendTransferRes =
            await DividendService.issueDividendTransfer({
              dividend,
              userId,
              portfolioId,
              subAccountId,
            });
          if (dividendTransferRes instanceof Error) {
            const errorMsg = `Error issuing dividend transfer for failed auto-reinvestment. Dividend ID: ${job.data.dividendId}`;
            sendIssueEmail({
              description: errorMsg,
              oData: {
                dividendTransferRes,
                dividendId,
              },
            });
            done(null, {
              dividendId: dividend.id,
              errorMsg,
              status: 'error',
            });
            throw dividendTransferRes;
          }
          done(null, {
            dividendId: dividend.id,
            amount: dividend.value,
            status: 'distributed',
          });
          return;
        } catch (err) {
          const errorMsg = `Error creating transfer for dividend that failed to auto reinvest. Dividend ID: ${job.data.dividendId}. User ID: ${userId}`;
          console.error(
            errorMsg,
            `${firstName} ${lastName}`,
            `Dividend ID: ${dividend.id}. Error: ${err}`
          );
          sendIssueEmail({
            description: `Error issuing dividend transfer for failed auto-reinvestment. Dividend ID: ${job.data.dividendId}`,
            oData: {
              err,
              dividendId,
            },
          });
          done(null, {
            dividendId: dividend.id,
            errorMsg,
            status: 'error',
          });
          return;
        }
      }

      // NOTE: Its important that we only end up here if the auto reinvestment above was successful.
      //      We don't want to be here if it failed and we already transferred them the full dividend amount (to avoid making 2 bank transfers to them).
      if (dividendTransferValue >= 0.005) {
        try {
          const partialDividendInput = {
            id: dividend.id,
            date: dividend.date,
            portfolioId: dividend.portfolioId,
            value: dividendTransferValue,
          };
          const partialDividendTransferRes =
            await DividendService.issueDividendTransfer({
              dividend: partialDividendInput,
              userId,
              portfolioId,
              subAccountId,
            });
          if (partialDividendTransferRes instanceof Error) {
            sendIssueEmail({
              description: `Error issuing partial dividend transfer for dividend with partial auto-reinvestment enabled. Dividend ID: ${job.data.dividendId}`,
              oData: {
                partialDividendTransferRes,
                dividendId,
                autoReinvestIndicator,
              },
            });
            throw partialDividendTransferRes;
          }
        } catch (error) {
          const errorMsg = `Error issuing partial dividend transfer for dividend with partial auto-reinvestment enabled. Dividend ID: ${job.data.dividendId}`;
          sendIssueEmail({
            description: errorMsg,
            oData: {
              error,
              dividendId,
              autoReinvestIndicator,
            },
          });
          done(null, {
            dividendId: dividend.id,
            errorMsg,
            status: 'error',
          });
          return;
        }
      }

      // Step 7) Mark job as complete in redis queue
      done(null, {
        dividendId: dividend.id,
        amountReinvested: investmentValue,
        amountDistributed: dividendTransferValue,
        percentReinvested: autoReinvestIndicator.percentage / 100,
        status: 'partiallyReinvested',
      });
    }
  );

  emailEquitySummaryReportQueue.process(1, async (job, done) => {
    const { endDt, email, portfolioId } = job.data;

    // NOTE: Marta wants the end date to be in terms of EST so if the input is 12/31/2024, we want endDtDate to be 2025-01-01 05:00:00Z because that is how its stored in the db (UTC)
    const eodUTC = moment(endDt).endOf('day').format('YYYY-MM-DD HH:mm:ss');
    const est = moment.tz(eodUTC, 'America/New_York');
    const endDtDate = est.toDate();

    const [portfoliosWInvestments, userMap] = await Promise.all([
      Portfolio.findAll({
        // NOTE: We used to run all portfolios at once but consistently had RAM issues
        where: {
          id: portfolioId,
        },
        attributes: ['id', 'name'],
        include: [
          {
            model: Investment,
            attributes: [],
            required: true,
            where: {
              cancelledDt: null,
              startDt: {
                [Op.lte]: endDtDate,
              },
            },
          },
        ],
      }),
      User.findAll({
        attributes: ['id', 'firstName', 'lastName', 'state'],
        include: [
          {
            model: Investment,
            attributes: [],
            required: true,
            where: {
              cancelledDt: null,
              startDt: {
                [Op.lte]: endDtDate,
              },
            },
          },
        ],
      }).then((usersWInvestments) => {
        const map = {};
        usersWInvestments.forEach((user) => {
          map[user.id] = {
            state: user.state,
            fullName: `${user.firstName} ${user.lastName}`,
          };
        });
        return map;
      }),
    ]);

    const portfolioIdNameMap = {};
    portfoliosWInvestments.forEach((portfolio) => {
      portfolioIdNameMap[portfolio.id] = portfolio.name;
    });

    const getPortfolioTabName = (portfolioName) => portfolioName.slice(0, 31);

    // NOTE: If you reorder any of these columns, make sure to not break the formulas
    const investmentSheetColumns = [
      {
        label: 'Start dt',
        getValue: (record) => record.startDt,
      },
      {
        label: 'Completed dt',
        getValue: (record) => record.calculatedCompletedDt,
      },
      {
        label: 'Portfolio ID',
        getValue: (record) => record.portfolioId,
      },
      {
        label: 'Portfolio',
        getValue: (record) => portfolioIdNameMap[record.portfolioId],
      },
      {
        label: 'User',
        getValue: (record) => userMap[record.userId]?.fullName || 'Unknown',
      },
      {
        label: 'Investor state',
        getValue: (record) => userMap[record.userId]?.state || 'Unknown',
      },
      {
        label: 'Is historical?',
        getValue: (record) => !!record.historicalFlg,
      },
      {
        label: 'Is reinvestment?',
        getValue: (record) => !!record.dividendId,
      },
      {
        label: 'Is referral?',
        getValue: (record) => !!record.referralId,
      },
      {
        label: 'Shares purchased',
        getValue: (record) => parseFloat(record.shares),
      },
      {
        label: 'Paid for share purchased',
        getValue: (record) => parseFloat(record.value),
      },
      {
        label: 'Newly issued shares purchased',
        getValue: (record) =>
          parseFloat(record.shares) -
          parseFloat(record.dataValues.totalSharesResold || 0),
      },
      {
        label: 'Paid for newly issued shares purchased',
        getValue: (record) =>
          parseFloat(record.value) -
          parseFloat(record.dataValues.totalValueResold || 0),
      },
      {
        label: 'Shares purchased from the crowd',
        getValue: (record) => {
          const sharesResold = parseFloat(
            record.dataValues.totalSharesResold || 0
          );
          if (sharesResold === 0) return 0;
          return InvestmentService.crowdSharesPurchased(record).then(
            (res) => record.shares - res
          );
        },
      },
      {
        label: 'Paid for shares purchased from the crowd',
        getValue: (record) => {
          const sharesResold = parseFloat(
            record.dataValues.totalSharesResold || 0
          );
          if (sharesResold === 0) return 0;
          return InvestmentService.crowdSharesPurchasedValue(record).then(
            (res) => record.value - res
          );
        },
      },
      {
        label: 'Shares purchased from Energea',
        formula: true,
        getValue: (record, row) => `R${row} - N${row}`,
      },
      {
        label: 'Paid for shares purchased from Energea',
        formula: true,
        getValue: (record, row) => `S${row} - O${row}`,
      },
      {
        label: 'Total shares resold',
        formula: true,
        getValue: (record, row) => `J${row} - L${row}`,
      },
      {
        label: 'Total paid for shares resold',
        formula: true,
        getValue: (record, row) => `K${row} - M${row}`,
      },
    ];
    // NOTE: If you reorder any of these columns, make sure to not break the formulas

    const dividendSheetColumns = [
      {
        label: 'Date',
        getValue: (record) => record.date,
      },
      {
        label: 'Completed dt',
        getValue: (record) => record.calculatedCompletedDt,
      },
      {
        label: 'Portfolio ID',
        getValue: (record) =>
          record.monthlyPortfolioFinancialActual.portfolioId,
      },
      {
        label: 'Portfolio',
        getValue: (record) =>
          portfolioIdNameMap[
            record.monthlyPortfolioFinancialActual.portfolioId
          ],
      },
      {
        label: 'User',
        getValue: (record) => userMap[record.userId]?.fullName || 'Unknown',
      },
      {
        label: 'Value',
        getValue: (record) => parseFloat(record.value),
      },
      {
        label: 'Value reinvested',
        getValue: (record) => parseFloat(record.investment?.value || 0),
      },
    ];

    const boldStyle = {
      bold: true,
    };

    const wb = new ExcelJS.Workbook();
    const masterWs = wb.addWorksheet('MASTER');
    masterWs.getCell(1, 1).value = 'Portfolio';
    masterWs.getCell(1, 1).font = boldStyle;
    masterWs.getCell(1, 2).value = 'Shares purchased';
    masterWs.getCell(1, 2).font = boldStyle;
    masterWs.getCell(1, 3).value = 'Value of shares purchased';
    masterWs.getCell(1, 3).font = boldStyle;
    masterWs.getCell(1, 4).value = 'Newly issued shares purchased';
    masterWs.getCell(1, 4).font = boldStyle;
    masterWs.getCell(1, 5).value = 'Paid for newly issued shares purchased';
    masterWs.getCell(1, 5).font = boldStyle;
    masterWs.getCell(1, 6).value = 'Shares purchased from Energea';
    masterWs.getCell(1, 6).font = boldStyle;
    masterWs.getCell(1, 7).value = 'Paid for shares purchased from Energea';
    masterWs.getCell(1, 7).font = boldStyle;
    masterWs.getCell(1, 8).value = 'Shares purchased from the crowd';
    masterWs.getCell(1, 8).font = boldStyle;
    masterWs.getCell(1, 9).value = 'Paid for shares purchased from the crowd';
    masterWs.getCell(1, 9).font = boldStyle;
    masterWs.getCell(1, 10).value = 'Total shares resold';
    masterWs.getCell(1, 10).font = boldStyle;
    masterWs.getCell(1, 11).value = 'Total paid for resold shares';
    masterWs.getCell(1, 11).font = boldStyle;

    masterWs.getColumn(1).width = 30;
    masterWs.getColumn(2).width = 15;
    masterWs.getColumn(3).width = 23;
    masterWs.getColumn(4).width = 28;
    masterWs.getColumn(5).width = 35;
    masterWs.getColumn(6).width = 27;
    masterWs.getColumn(7).width = 35;
    masterWs.getColumn(8).width = 29;
    masterWs.getColumn(9).width = 36;
    masterWs.getColumn(10).width = 16;
    masterWs.getColumn(11).width = 24;

    for (
      let portfolioIndex = 0;
      portfolioIndex < portfoliosWInvestments.length;
      portfolioIndex += 1
    ) {
      const portfolio = portfoliosWInvestments[parseInt(portfolioIndex, 10)];
      console.info(`Processing Equity Summary Report for ${portfolio.name}`);

      const masterSheetRow = portfolioIndex + 2;
      masterWs.getCell(masterSheetRow, 1).value = portfolio.name;
      masterWs.getCell(masterSheetRow, 2).value = {
        formula: `SUM('${getPortfolioTabName(portfolio.name)}'!J:J)`,
      };
      masterWs.getCell(masterSheetRow, 3).value = {
        formula: `SUM('${getPortfolioTabName(portfolio.name)}'!K:K)`,
      };
      masterWs.getCell(masterSheetRow, 4).value = {
        formula: `SUM('${getPortfolioTabName(portfolio.name)}'!L:L)`,
      };
      masterWs.getCell(masterSheetRow, 5).value = {
        formula: `SUM('${getPortfolioTabName(portfolio.name)}'!M:M)`,
      };
      masterWs.getCell(masterSheetRow, 6).value = {
        formula: `J${masterSheetRow}-H${masterSheetRow}`,
      };
      masterWs.getCell(masterSheetRow, 7).value = {
        formula: `K${masterSheetRow}-I${masterSheetRow}`,
      };
      masterWs.getCell(masterSheetRow, 8).value = {
        formula: `SUM('${getPortfolioTabName(portfolio.name)}'!N:N)`,
      };
      masterWs.getCell(masterSheetRow, 9).value = {
        formula: `SUM('${getPortfolioTabName(portfolio.name)}'!O:O)`,
      };
      masterWs.getCell(masterSheetRow, 10).value = {
        formula: `SUM('${getPortfolioTabName(
          portfolio.name
        )}'!J:J) - SUM('${getPortfolioTabName(portfolio.name)}'!L:L)`,
      };
      masterWs.getCell(masterSheetRow, 11).value = {
        formula: `SUM('${getPortfolioTabName(
          portfolio.name
        )}'!K:K) - SUM('${getPortfolioTabName(portfolio.name)}'!M:M)`,
      };

      console.time(`${portfolio.name} - queryInvestmentsAndDividends`);
      const [investments, dividends] = await Promise.all([
        portfolio.getInvestments({
          attributes: [
            'id',
            'startDt',
            'shares',
            'value',
            'portfolioId',
            'completedDt',
            'historicalFlg',
            'referralId',
            'dividendId',
            'userId',
            [fn('sum', col('soldShares')), 'totalSharesResold'],
            [fn('sum', col('shareTransfers.value')), 'totalValueResold'],
          ],
          group: ['investment.id', 'transfer.id'],
          where: {
            startDt: {
              [Op.lte]: endDtDate,
            },
            cancelledDt: null,
          },
          include: [
            {
              model: ShareTransfer,
              required: false,
              attributes: [],
            },
            {
              model: Transfer,
              attributes: ['completedDt'],
              required: false,
            },
          ],
          order: [['startDt', 'desc']],
        }),
        Dividend.findAll({
          attributes: ['id', 'date', 'value', 'userId'],
          where: {
            date: {
              [Op.lte]: endDtDate,
            },
          },
          include: [
            {
              model: MonthlyPortfolioFinancialActual,
              attributes: ['portfolioId'],
              required: true,
              where: {
                portfolioId: portfolio.id,
              },
            },
            {
              model: Investment,
              attributes: ['value'],
              required: false,
              where: {
                cancelledDt: null,
              },
            },
            {
              model: Transfer,
              attributes: ['completedDt'],
              required: false,
            },
          ],
          order: [['date', 'desc']],
        }),
      ]);
      console.timeEnd(`${portfolio.name} - queryInvestmentsAndDividends`);

      const investmentWs = wb.addWorksheet(getPortfolioTabName(portfolio.name));
      investmentSheetColumns.forEach((column, columnIndex) => {
        investmentWs.getCell(1, columnIndex + 1).value = column.label;
        investmentWs.getCell(1, columnIndex + 1).font = boldStyle;
      });
      investmentWs.getColumn(1).width = 10;
      investmentWs.getColumn(2).width = 12;
      investmentWs.getColumn(3).width = 10;
      investmentWs.getColumn(4).width = 25;
      investmentWs.getColumn(5).width = 25;
      investmentWs.getColumn(6).width = 12;
      investmentWs.getColumn(7).width = 11;
      investmentWs.getColumn(8).width = 14;
      investmentWs.getColumn(9).width = 10;
      investmentWs.getColumn(10).width = 15;
      investmentWs.getColumn(11).width = 22;
      investmentWs.getColumn(12).width = 31;
      investmentWs.getColumn(13).width = 37;
      investmentWs.getColumn(14).width = 29;
      investmentWs.getColumn(15).width = 36;
      investmentWs.getColumn(16).width = 27;
      investmentWs.getColumn(17).width = 34;
      investmentWs.getColumn(18).width = 16;
      investmentWs.getColumn(19).width = 24;

      console.time(`${portfolio.name} - completedDts`);
      const investmentsWithCompletedDts = [];
      const dividendsWithCompletedDts = [];

      const maxConcurrentPromises = 100;
      let aCompletedDtPromises = [];
      for (let index = 0; index < investments.length; index += 1) {
        const record = investments[parseInt(index, 10)];
        if (aCompletedDtPromises.length >= maxConcurrentPromises) {
          await Promise.all(aCompletedDtPromises);
          const percentComplete = Math.round(
            (index / investments.length) * 100
          );
          console.log(
            `Calculate investment completedDt progress: ${percentComplete}%`
          );
          aCompletedDtPromises = [];
        }
        aCompletedDtPromises.push(
          record.completedDt.then((completedDt) => {
            const updatedRecord = record;
            updatedRecord.calculatedCompletedDt = completedDt;
            investmentsWithCompletedDts.push(updatedRecord);
          })
        );
      }
      for (let index = 0; index < dividends.length; index += 1) {
        const record = dividends[parseInt(index, 10)];
        if (aCompletedDtPromises.length >= maxConcurrentPromises) {
          await Promise.all(aCompletedDtPromises);
          const percentComplete = Math.round((index / dividends.length) * 100);
          console.log(
            `Calculate dividend completedDt progress: ${percentComplete}%`
          );
          aCompletedDtPromises = [];
        }
        aCompletedDtPromises.push(
          record.completedDt.then((completedDt) => {
            const updatedRecord = record;
            updatedRecord.calculatedCompletedDt = completedDt;
            dividendsWithCompletedDts.push(updatedRecord);
          })
        );
      }
      await Promise.all(aCompletedDtPromises);

      investmentsWithCompletedDts.sort((a, b) =>
        a.calculatedCompletedDt < b.calculatedCompletedDt ? 1 : -1
      );
      dividendsWithCompletedDts.sort((a, b) =>
        a.calculatedCompletedDt < b.calculatedCompletedDt ? 1 : -1
      );
      console.timeEnd(`${portfolio.name} - completedDts`);

      console.time(`${portfolio.name} - writeRecords`);
      const aPromises = [];
      for (
        let recordIndex = 0;
        recordIndex < investmentsWithCompletedDts.length;
        recordIndex += 1
      ) {
        const record = investmentsWithCompletedDts[parseInt(recordIndex, 10)];
        for (
          let columnIndex = 0;
          columnIndex < investmentSheetColumns.length;
          columnIndex += 1
        ) {
          const column = investmentSheetColumns[parseInt(columnIndex, 10)];
          const value = column.getValue(record, recordIndex + 2);
          if (isPromise(value)) {
            // eslint-disable-next-line no-await-in-loop
            aPromises.push(
              value.then((val) => {
                investmentWs.getCell(recordIndex + 2, columnIndex + 1).value =
                  val;
              })
            );
          } else {
            investmentWs.getCell(recordIndex + 2, columnIndex + 1).value =
              column.formula ? { formula: value } : value;
          }
        }
      }

      const dividendWs = wb.addWorksheet(
        `${getPortfolioTabName(portfolio.name).slice(0, 27)} DIV`
      );
      dividendSheetColumns.forEach((column, columnIndex) => {
        dividendWs.getCell(1, columnIndex + 1).value = column.label;
        dividendWs.getCell(1, columnIndex + 1).font = boldStyle;
      });
      dividendWs.getColumn(1).width = 10;
      dividendWs.getColumn(2).width = 12;
      dividendWs.getColumn(3).width = 10;
      dividendWs.getColumn(4).width = 25;
      dividendWs.getColumn(5).width = 25;
      dividendWs.getColumn(6).width = 12;
      dividendWs.getColumn(7).width = 14;

      for (
        let recordIndex = 0;
        recordIndex < dividendsWithCompletedDts.length;
        recordIndex += 1
      ) {
        const record = dividendsWithCompletedDts[parseInt(recordIndex, 10)];
        for (
          let columnIndex = 0;
          columnIndex < dividendSheetColumns.length;
          columnIndex += 1
        ) {
          const column = dividendSheetColumns[parseInt(columnIndex, 10)];
          const value = column.getValue(record, recordIndex + 2);
          if (isPromise(value)) {
            // eslint-disable-next-line no-await-in-loop
            aPromises.push(
              value.then((val) => {
                dividendWs.getCell(recordIndex + 2, columnIndex + 1).value =
                  val;
              })
            );
          } else {
            dividendWs.getCell(recordIndex + 2, columnIndex + 1).value =
              column.formula ? { formula: value } : value;
          }
        }
      }
      await Promise.all(aPromises);
      console.timeEnd(`${portfolio.name} - writeRecords`);
    }

    const fileName = `Equity Summary ${moment(endDt).format(
      'MM-DD-YYYY'
    )} ${new Date().getTime()}.xlsx`;
    const awsObjectKey = lintAwsObjectKey(`temporary/${fileName}`);

    const passThrough = new PassThrough();
    wb.xlsx.write(passThrough);
    uploadStreamToS3(awsObjectKey, passThrough).then(
      () =>
        getSignedUrl(awsObjectKey, null, null, 24 * 60 * 60).then(
          (downloadUrl) => {
            sendEquitySummaryReportEmail({
              email,
              downloadUrl,
            }).then(() => {
              done();
            });
          }
        ),
      (err) => {
        SlackService.logToSlack({
          title: 'Error writing equity summary file to S3',
          type: 'platform-error',
          data: [
            {
              label: 'File',
              value: fileName,
            },
            {
              label: 'Error',
              value: stringifyObject(err),
            },
          ],
        });
        done(err);
      }
    );
  });

  utilityBillConversionQueue.process(1, async (job, done) => {
    try {
      const { email, awsObjectKeys, utilityCompany } = job.data;
      const fileName = 'utility-bills.json';
      const jsonObj = [];

      await Promise.all(
        awsObjectKeys.map((awsObjectKey) =>
          getObjectAsBuffer(
            awsObjectKey,
            process.env.S3_BUCKET_CREDIT_MGMT
          ).then(async (buffer) => {
            const base64PdfString = buffer.toString('base64');
            let func = null;
            if (utilityCompany.toLowerCase() === 'energisa') {
              func = fetchEnergisaBillConversion;
            } else {
              func = fetchLightBillConversion;
            }
            return func(base64PdfString).then((res) => {
              jsonObj.push(res);
            });
          })
        )
      );

      jsonfile.writeFile(fileName, jsonObj, {}, (err) => {
        if (err) {
          console.error(err);
          throw err;
        }
        const deleteFile = () => {
          fs.unlink(fileName, (err1) => {
            if (err1) {
              console.error(
                'Error deleting utility bill conversion json after emailing',
                fileName,
                err1
              );
            }
          });
        };
        sendUtilityBillConversionEmail({ email, filePath: fileName }).then(
          () => {
            deleteFile();
          },
          (err2) => {
            console.error('Error sending utility Bill conversion email', err2);
            deleteFile();
            throw err2;
          }
        );
      });
    } catch (err) {
      console.error(err);
      done(err);
      return;
    }

    done();
  });

  emailMonthlyPortfolioReconciliationQueue.process(1, async (job, done) => {
    // Probably fine to let more than 1 process at a time but no need and this uses a lot of memory
    // Assumes ran during month after the one data is wanted for. Run April 1-30 for March data.
    const { month, portfolioId, emailOverride } = job.data;
    console.time(`reconciliation - portfolio id ${portfolioId}`);

    const roundDollars = (val) => Math.round(val * 100) / 100;

    const dollarFormat = {
      numberFormat: '$#,##0.00; ($#,##0.00); -',
    };

    const userIdNameMap = {};
    await User.findAll({
      attributes: ['id', 'type', 'firstName', 'lastName', 'businessName'],
      include: [
        {
          attributes: [],
          required: true,
          model: Investment,
          where: { cancelledDt: null },
        },
      ],
    }).then((res) => {
      res.forEach((user) => {
        userIdNameMap[String(user.id)] =
          user.type === 'business'
            ? user.businessName
            : `${user.firstName} ${user.lastName}`;
      });
    });

    const getUserNameFromId = (userId) => {
      if (!userId) {
        console.error('No userId provided to function');
        return 'ERROR';
      }
      if (!userIdNameMap[String(userId)]) {
        console.error(`User data not in userIdNameMap. ID: ${userId}`);
        return 'ERROR';
      }
      return userIdNameMap[String(userId)];
    };

    const consolidatedTabColumns = [
      {
        label: 'Portfolio ID',
        getValue: (record) => record.portfolio.id,
        dataType: 'number',
      },
      {
        label: 'Portfolio',
        getValue: (record) => record.portfolio.name,
        dataType: 'string',
      },
      {
        label: 'Month',
        getValue: (record) => record.month,
        dataType: 'string',
      },
      {
        label: 'Total Invested',
        getValue: (record) => roundDollars(record.totalInvested),
        dataType: 'number',
        style: dollarFormat,
      },
      {
        label: 'Total Crowd Invested',
        getValue: (record) => roundDollars(record.totalCrowdInvested),
        dataType: 'number',
        style: dollarFormat,
      },
      {
        label: 'Total Energea Invested',
        getValue: (record) => roundDollars(record.totalEnergeaInvested),
        dataType: 'number',
        style: dollarFormat,
      },
      {
        label: 'Total Sold from Crowd',
        getValue: (record) => roundDollars(record.totalSoldFromCrowd),
        dataType: 'number',
        style: dollarFormat,
      },
      {
        label: 'Total Sold from Energea',
        getValue: (record) => roundDollars(record.totalSoldFromEnergea),
        dataType: 'number',
        style: dollarFormat,
      },
      {
        label: 'Total Bought Back by Issuer',
        getValue: (record) => roundDollars(record.totalBoughtBackByIssuer),
        dataType: 'number',
        style: dollarFormat,
      },
      {
        label: 'Total Sold for Referrals',
        getValue: (record) => roundDollars(record.totalSoldForReferrals || 0),
        dataType: 'number',
        style: dollarFormat,
      },
      {
        label: 'Total Sold for Promos',
        getValue: (record) => roundDollars(record.totalSoldForPromos || 0),
        dataType: 'number',
        style: dollarFormat,
      },
      {
        label: 'Total Sold for Reinvestments',
        getValue: (record) =>
          roundDollars(record.totalSoldForReinvestments || 0),
        dataType: 'number',
        style: dollarFormat,
      },
      {
        label: 'Total Distributions',
        getValue: (record) => roundDollars(record.totalDividends),
        dataType: 'number',
        style: dollarFormat,
      },
      {
        label: 'Total Distributions Distributed',
        getValue: (record) => roundDollars(record.totalDividendsDistributed),
        dataType: 'number',
        style: dollarFormat,
      },
      {
        label: 'Total Distributions Reinvested',
        getValue: (record) => roundDollars(record.totalDividendsReinvested),
        dataType: 'number',
        style: dollarFormat,
      },
      {
        label: 'Total Distributions Reinvested Sold to Energea',
        getValue: (record) =>
          roundDollars(record.totalDividendsReinvestedSoldToEnergea),
        dataType: 'number',
        style: dollarFormat,
      },
      {
        label: 'Total Distributions Reinvested Sold to Crowd',
        getValue: (record) =>
          roundDollars(record.totalDividendsReinvestedSoldToCrowd),
        dataType: 'number',
        style: dollarFormat,
      },
    ];

    const investmentListColumns = [
      {
        label: 'Initiated Dt',
        getValue: (record) =>
          moment(record.startDt)
            .tz('America/New_York')
            .format('MM-DD-YYYY hh:mm:ss a z'),
        dataType: 'string',
      },
      {
        label: 'Completed Dt',
        getValue: (record) =>
          (record.resolvedCompletedDt &&
            moment(record.resolvedCompletedDt)
              .tz('America/New_York')
              .format('MM-DD-YYYY hh:mm:ss a z')) ||
          '',
        dataType: 'string',
      },
      {
        label: 'ID',
        getValue: (record) => record.id,
        dataType: 'number',
      },
      {
        label: 'User',
        getValue: (record) => getUserNameFromId(record.userId),
        dataType: 'string',
      },
      {
        label: 'Value',
        getValue: (record) => roundDollars(record.value),
        dataType: 'number',
        style: dollarFormat,
      },
      {
        label: 'Share Price',
        getValue: (record) => {
          if (parseFloat(record.value) === 0) return 0;
          return parseFloat(record.value / record.shares);
        },
        dataType: 'number',
        style: dollarFormat,
      },
      {
        label: 'Shares',
        getValue: (record) => parseFloat(record.shares),
        dataType: 'number',
      },
      {
        label: 'Natural Shares Purchased',
        getValue: (record) => InvestmentService.naturalSharesPurchased(record),
        dataType: 'number',
      },
      {
        label: 'Natural Shares Purchased Value',
        getValue: (record) =>
          InvestmentService.naturalSharesPurchasedValue(record),
        dataType: 'number',
        style: dollarFormat,
      },
      {
        label: 'Additional Paid in Capital',
        getValue: (record) => {
          const naturalSharesPurchasedValue =
            InvestmentService.naturalSharesPurchasedValue(record);
          const naturalSharesPurchased =
            InvestmentService.naturalSharesPurchased(record);
          return naturalSharesPurchasedValue - naturalSharesPurchased;
        },
        dataType: 'number',
        style: dollarFormat,
      },
    ];

    const dividendListColumns = [
      {
        label: 'Initiated Date',
        getValue: (record) =>
          (record.resolvedCompletedDt &&
            moment(record.resolvedCompletedDt)
              .tz('America/New_York')
              .format('MM-DD-YYYY hh:mm:ss a z')) ||
          '',
        dataType: 'string',
      },
      {
        label: 'ID',
        getValue: (record) => record.id,
        dataType: 'number',
      },
      {
        label: 'User',
        getValue: (record) => getUserNameFromId(record.userId),
        dataType: 'string',
      },
      {
        label: 'Value',
        getValue: (record) => roundDollars(record.value),
        dataType: 'number',
        style: dollarFormat,
      },
    ];

    const reinvestedDividendSoldToListColumns = [
      {
        label: 'Initiated Dt',
        getValue: (record) =>
          (record.resolvedCompletedDt &&
            moment(record.resolvedCompletedDt)
              .tz('America/New_York')
              .format('MM-DD-YYYY hh:mm:ss a z')) ||
          '',
        dataType: 'string',
      },
      {
        label: 'ID', // dividend.id
        getValue: (record) => record.id,
        dataType: 'number',
      },
      // NOTE: This column requires us to have a very deep nested include that seems to be the main reason this process has gotten extremely slow.
      // Marta stated that she doesnt need the names to appear on this tab we are removing them in hopes to slim down the sequelize query and speed up the process.
      // {
      //   label: 'Sold From',
      //   getValue: (record) => {
      //     const soldFromUsers = [];
      //     record.investment.shareTransfers.forEach((shareTransfer) => {
      //       const { userId } = shareTransfer.sellOrder;
      //       const userName = getUserNameFromId(userId);
      //       if (soldFromUsers.indexOf(userName) === -1) {
      //         soldFromUsers.push(userName);
      //       }
      //     });
      //     return soldFromUsers.join(', ');
      //   },
      //   dataType: 'string',
      // },
      {
        label: 'Sold To', // dividend.userId
        getValue: (record) => getUserNameFromId(record.userId),
        dataType: 'string',
      },
      {
        label: 'Value',
        getValue: (record) => roundDollars(parseFloat(record.valueSold)),
        dataType: 'number',
        style: dollarFormat,
      },
    ];

    const shareTransferListColumns = [
      {
        label: 'Transfer Created Dt',
        getValue: (record) =>
          (record.resolvedCompletedDt &&
            moment(record.resolvedCompletedDt)
              .tz('America/New_York')
              .format('MM-DD-YYYY hh:mm:ss a z')) ||
          '',
        dataType: 'string',
      },
      {
        label: 'ID',
        getValue: (record) => record.id,
        dataType: 'number',
      },
      {
        label: 'Sold From',
        getValue: (record) => getUserNameFromId(record.sellOrder.userId),
        dataType: 'string',
      },
      {
        label: 'Sold To',
        getValue: (record) => getUserNameFromId(record.investment.userId),
        dataType: 'string',
      },
      {
        label: 'Value',
        getValue: (record) => roundDollars(record.value),
        dataType: 'number',
        style: dollarFormat,
      },
    ];

    // NOTE: If a tabs name is more than 31 characters long, it will show an error when you open it
    const tabs = [
      {
        label: 'Investments',
        getRecords: (data) => data.totalInvestedRecords,
        columns: investmentListColumns,
      },
      {
        label: 'Crowd Investments',
        getRecords: (data) => data.totalCrowdInvestedRecords,
        columns: investmentListColumns,
      },
      {
        label: 'Energea Investments',
        getRecords: (data) => data.totalEnergeaInvestedRecords,
        columns: investmentListColumns,
      },
      {
        label: 'Crowd Share Transfer',
        getRecords: (data) => data.totalSoldFromCrowdRecords,
        columns: shareTransferListColumns,
      },
      {
        label: 'Energea Share Transfers',
        getRecords: (data) => data.totalSoldFromEnergeaRecords,
        columns: shareTransferListColumns,
      },
      {
        label: 'Referral Share Transfers',
        getRecords: (data) => data.totalSoldForReferralsRecords,
        columns: shareTransferListColumns,
      },
      {
        label: 'Promo Share Transfers',
        getRecords: (data) => data.totalSoldForPromosRecords,
        columns: shareTransferListColumns,
      },
      {
        label: 'Reinvestment Share Transfers',
        getRecords: (data) => data.totalSoldForReinvestmentsRecords,
        columns: shareTransferListColumns,
      },
      {
        label: 'Distributions',
        getRecords: (data) => data.totalDividendsRecords,
        columns: dividendListColumns,
      },
      {
        label: 'Distributed Distributions',
        getRecords: (data) => data.totalDividendsDistributedRecords,
        columns: dividendListColumns,
      },
      {
        label: 'Reinvested Distributions',
        getRecords: (data) => data.totalDividendsReinvestedRecords,
        columns: dividendListColumns,
      },
      {
        label: 'Reinvstd Dist. Sold to Energea',
        getRecords: (data) => data.totalDividendsReinvestedSoldToEnergeaRecords,
        columns: reinvestedDividendSoldToListColumns,
      },
      {
        label: 'Reinvstd Dist. Sold to Crowd',
        getRecords: (data) => data.totalDividendsReinvestedSoldToCrowdRecords,
        columns: reinvestedDividendSoldToListColumns,
      },
    ];

    let monthOffset = -1;
    if (month) {
      const thisMonth = moment().startOf('month');
      const thatMonth = moment(month, 'YYYY-MM').startOf('month');
      monthOffset = thatMonth.diff(thisMonth, 'month');
    }
    const lastMonth = moment().add(monthOffset, 'month');
    const lastMonthStr = lastMonth.format('MM-YYYY');

    const portfolio = await Portfolio.findByPk(portfolioId);
    const input = `${portfolio.id}_${lastMonthStr}`;
    console.time('reconciliation - getPortfolioMonthReconciliation');
    // eslint-disable-next-line no-await-in-loop
    const res = await PortfolioService.getPortfolioMonthReconciliation(
      input,
      monthOffset
    );
    console.timeEnd('reconciliation - getPortfolioMonthReconciliation');

    if (res) {
      const wb = new xl.Workbook();
      const ws = wb.addWorksheet('Consolidated');
      consolidatedTabColumns.forEach((column, index) => {
        ws.cell(1, index + 1).string(column.label);
        writeToCell({
          worksheet: ws,
          row: 2,
          col: index + 1,
          val: column.getValue(res),
          dataType: column.dataType,
          style: column.style,
        });
      });

      // const aPromises = [];
      tabs.forEach((tab) => {
        console.log('Writing tab', tab.label);
        const ws1 = wb.addWorksheet(tab.label);
        tab.columns.forEach((column, index) => {
          ws1.cell(1, index + 1).string(column.label);
        });
        const records = tab.getRecords(res) || [];
        records.forEach((record, rowIndex) => {
          tab.columns.forEach((column, columnIndex) => {
            const value = column.getValue(record);
            writeToCell({
              worksheet: ws1,
              row: rowIndex + 2,
              col: columnIndex + 1,
              val: value,
              dataType: column.dataType,
              style: column.style,
            });
          });
        });
      });

      const fileName = `${portfolio.name} ${lastMonthStr} - Portfolio Month Reconciliation.xlsx`;

      const awsObjectKey = lintAwsObjectKey(`temporary/${fileName}`);
      // const passThrough = new PassThrough();

      // Convert to buffer
      const buffer = await wb.writeToBuffer();

      await uploadStreamToS3(awsObjectKey, buffer).then(
        () =>
          getSignedUrl(awsObjectKey, null, null, 24 * 60 * 60).then(
            (downloadUrl) => {
              SlackService.logToSlack({
                title: 'Accounting Reconciliation Report Ready',
                type: 'it',
                data: [
                  {
                    label: 'Portfolio',
                    value: portfolio.name,
                  },
                  {
                    label: 'Month',
                    value: lastMonth.format('MMM YYYY'),
                  },
                  {
                    label: 'Download URL (expires in 24hrs)',
                    value: downloadUrl,
                  },
                ],
              });
              // NOTE: The sequelize queries involved with sending the email are failing with SequelizeConnectionAcquireTimeoutError with larger portfolios
              sendMonthlyPortfolioReconciliationDocumentsReadyEmail({
                emailOverride,
                portfolioName: portfolio.name,
                month: lastMonth.format('MMM YYYY'),
                downloadUrl,
              }).then(() => {
                console.log('HIT reconciliation email sent');
              });
              done();
            }
          ),
        (err) => {
          SlackService.logToSlack({
            title: 'Error writing reconciliation report to s3',
            type: 'platform-error',
            data: [
              {
                label: 'File',
                value: fileName,
              },
              {
                label: 'Error',
                value: stringifyObject(err),
              },
            ],
          });
          done(err);
        }
      );
    }

    console.timeEnd(`reconciliation - portfolio id ${portfolioId}`);

    // done();
  });

  runMonthlyOMReportQueue.process(1, async (job, done) => {
    try {
      const { projectId, monthStr, email } = job.data;
      const month = moment(monthStr, 'MM-YYYY').startOf('month');
      const startDt = moment(month).startOf('month');
      const endDt = moment(month).endOf('month');

      const [employees, employeeTypesMap] = await Promise.all([
        Employee.findAll({
          attributes: ['id', 'firstName', 'lastName', 'employeeTypeIds'],
        }),
        EmployeeType.findAll({ attributes: ['id', 'name'] }).then((res) => {
          const map = {};
          res.forEach((employeeType) => {
            map[String(employeeType.id)] = employeeType.name;
          });
          return map;
        }),
      ]);
      const lintedEmployees = employees.map((e) => {
        const updatedEmployee = e;
        updatedEmployee.employeeTypes = e.employeeTypeIds
          ? e.employeeTypeIds.map((etid) => employeeTypesMap[String(etid)])
          : [];
        return updatedEmployee;
      });

      const aPromises = [];
      aPromises.push(
        Project.findByPk(projectId, {
          attributes: ['name', 'systemSizeDC'],
        })
      );
      aPromises.push(
        getProductionTotalsData({
          projectId,
          groupedBy: 'day',
          startDt,
          endDt,
        })
      );
      aPromises.push(
        OMTicket.findAll({
          attributes: [
            'id',
            'startDt',
            'endDt',
            'createdAt',
            'estimatedPercentageLoss',
            'projectId',
            'equipmentItemId',
            'title',
            'deviceName',
            'notes',
            'clientNotifiedDt',
            'clientNotificationType',
          ],
          where: {
            projectId,
            internalOnlyFlg: {
              [Op.not]: true,
            },
            startDt: {
              [Op.lt]: endDt.format('YYYY-MM-DD HH:mm:ss'),
            },
            [Op.or]: [
              {
                endDt: null,
              },
              {
                endDt: {
                  [Op.between]: [
                    startDt.format('YYYY-MM-DD HH:mm:ss'),
                    endDt.format('YYYY-MM-DD HH:mm:ss'),
                  ],
                },
              },
            ],
          },
          order: [
            [Sequelize.literal('"omTicket"."endDt" is null'), 'desc'],
            ['estimatedPercentageLoss', 'desc nulls last'],
            ['id', 'asc'],
            [OMReport, 'startDt', 'ASC'],
          ],
          include: [
            {
              model: EquipmentItem,
              required: false,
              attributes: ['manufacturer', 'model'],
            },
            {
              model: OMTicketType,
              required: false,
              attributes: ['name'],
            },
            {
              model: Project,
              required: false,
              attributes: [
                'id',
                'address1',
                'address2',
                'city',
                'postalCode',
                'state',
              ],
              include: [
                {
                  model: OMTruck,
                  attributes: ['name'],
                },
              ],
            },
            {
              model: OMReport,
              attributes: [
                'id',
                'resolvedFlg',
                'startDt',
                'endDt',
                'notes',
                'ppeNotes',
                'rmaNotes',
                'partsUsedNotes',
                'partsOrderedNotes',
                'employeeIds',
                'submittedDt',
                'thirdPartyFlg',
                'thirdPartyCompanyName',
                'thirdPartyReportEnglishPublicId',
                'thirdPartyReportPortuguesePublicId',
              ],
              include: [
                {
                  model: OMReportImage,
                  attributes: ['id', 'public_id', 'description'],
                  where: { includeInReportFlg: true },
                  required: false,
                  limit: 12,
                },
                { model: OMReportType, attributes: ['name'] },
                {
                  model: Employee,
                  as: 'submittedByEmployee',
                  attributes: ['firstName', 'lastName'],
                },
              ],
            },
          ],
        }).then((res) =>
          Promise.all(
            res.map((ticket) => {
              const updatedTicket = ticket;
              return getTicketEstimatedImpact(ticket).then((impact) => {
                updatedTicket.estimatedGenerationLoss =
                  impact.estimatedGenerationLoss;
                updatedTicket.omReports.forEach((report) => {
                  // eslint-disable-next-line no-param-reassign
                  report.employees = lintedEmployees.filter(
                    (e) => report.employeeIds.indexOf(e.id) > -1
                  );
                });
                return updatedTicket;
              });
            })
          )
        )
      );
      const [project, generationDays, tickets] = await Promise.all(aPromises);
      // NOTE: Due to timezone differences around endOf('month') and startOf('month'),
      // getProductionTotalsData sometimes returns an extra day at the start or end
      // of the range. Filter them out below and sort ASC
      const lintedGenerationDays = generationDays
        .filter(
          (day) =>
            moment(day.date, 'MMM D, YYYY').isSameOrAfter(startDt) &&
            moment(day.date, 'MMM D, YYYY').isSameOrBefore(endDt)
        )
        .sort((a, b) =>
          moment(a.date, 'MMM D, YYYY').isBefore(moment(b.date, 'MMM D, YYYY'))
            ? -1
            : 1
        );

      const pdfDoc = await omReport({
        projectName: project.name,
        systemSizekWDC: (project.systemSizeDC || 0) * 1000,
        month,
        generationDays: lintedGenerationDays,
        tickets,
      });

      const fileName = `${
        project.name
      }_${monthStr}_${new Date().getTime()}.pdf`;

      // Finalize PDF file and save buffer for email
      const buffs = [];
      let buffer = null;
      pdfDoc.on('data', (d) => {
        buffs.push(d);
      });
      pdfDoc.on('end', () => {
        buffer = Buffer.concat(buffs);
        if (email) {
          sendOMMonthlyReportEmail({
            attachment: {
              content: buffer.toString('base64'),
              filename: fileName,
              type: 'text/pdf',
              disposition: 'attachment',
            },
            month: monthStr,
            projectName: project.name,
            email,
          }).catch((err) => {
            console.error(
              'Error sending sendOMMonthlyReportEmail',
              job.data,
              err
            );
          });
        }
      });
      pdfDoc.end();

      const awsObjectKey = lintAwsObjectKey(`OMMonthlyReports/${fileName}`);
      await uploadObjectToS3(awsObjectKey, pdfDoc).then(
        () =>
          OMMonthlyReport.findOne({
            where: {
              projectId,
              month: monthStr,
            },
          }).then(
            (existingReport) => {
              if (!existingReport) {
                const createInput = {
                  projectId,
                  awsObjectKey,
                  month: monthStr,
                };
                return OMMonthlyReport.create(createInput).catch((err) => {
                  console.error(
                    'Error creating OMMonthlyReport',
                    createInput,
                    err
                  );
                });
              }
              const updatedReport = existingReport;
              updatedReport.awsObjectKey = awsObjectKey;
              return updatedReport.save().catch((err) => {
                console.error('Error updating OMMonthlyReport', err);
              });
            },
            (err) => {
              console.error('Error finding OMMonthlyReport', err);
            }
          ),
        (err) => {
          console.error('Error uploading O&M report', err);
        }
      );

      SlackService.logToSlack({
        type: 'monitoring',
        title: 'Monthly O&M Report created',
        data: [
          { label: 'Project', value: project.name },
          { label: 'Month', value: monthStr },
          { label: 'Email', value: email },
        ],
      });
      done();
    } catch (err) {
      console.error('Error running runMonthlyOMReportQueue', err);
      done(err);
    }
  });

  sendMonitoringPunchlistReportQueue.process(1, async (job, done) => {
    const { emailAddresses, name, projectIds } = job.data;
    try {
      // Fetch project data in a single batch query
      const projects = await Project.findAll({
        where: { id: projectIds },
        attributes: ['id', 'name'],
        order: [['name', 'asc']],
      });
      const projectIdNameMap = projects.reduce((map, project) => {
        map[String(project.id)] = project.name;
        return map;
      }, {});

      const projectPunchListRows = [];
      for (let index = 0; index < projectIds.length; index += 1) {
        const pid = projectIds[parseInt(index, 10)];
        // eslint-disable-next-line no-await-in-loop
        const res = await projectMonitoringPunchList(pid).catch((e) => {
          console.error(
            `Error retrieving monitoring punch list for project id: ${pid}`,
            e
          );
          SlackService.logToSlack({
            type: 'monitoring',
            title: 'Failed to retrieve monitoring punchlist report for project',
            data: [
              { label: 'Report', value: name },
              { label: 'Project', value: projectIdNameMap[String(pid)] },
              { label: 'Error', value: stringifyObject(e) },
            ],
          });
          return { error: true, id: pid };
        });
        projectPunchListRows.push(res);
      }

      // Put into Excel
      const attrs = [
        'inverterPower',
        'windSpeed',
        'directIrradiance',
        'horizontalIrradiance',
        'reflectedIrradiance',
        'moduleTemperature',
        'revenueGradeMeter',
        'trackerPosition',
      ];

      const getTextFromStatus = (statusObj) => {
        if (!statusObj) return 'Error';
        switch (statusObj.status) {
          case 'Pass':
            return '\u2713';
          case 'N/A':
            return '...';
          case 'Unknown':
            return '?';
          case 'Fail':
            return 'X';
          case 'No data last 24hrs':
            return 'No data';
          default:
            return 'Partial';
        }
      };

      const getColor = (color) => {
        switch (color) {
          case 'gray':
            return 'gray-40';
          case 'red':
            return '#FCE4D6';
          case 'green':
            return '#E2EFDA';
          case 'orange':
            return '#FFF2CC';
          default:
            return 'white';
        }
      };

      const boldStyle = { font: { bold: true } };
      const verticalCenterStyle = { alignment: { vertical: 'center' } };

      const wb = new xl.Workbook();
      const ws = wb.addWorksheet('Overview');

      // Add header row
      const headers = [
        'Truck',
        'Project',
        '2.4 Inverter Power',
        '2.9 Wind Speed',
        '2.14.1 Direct Irradiance',
        '2.14.2 Horizontal Irradiance',
        '2.14.3 Reflected Irradiance',
        '2.16 Module Temperature',
        '2.18 Revenue Grade Meter',
        '2.23 Tracker Position',
      ];

      headers.forEach((header, i) => {
        ws.cell(1, i + 1)
          .string(header)
          .style(boldStyle)
          .style(verticalCenterStyle);
      });
      ws.row(1).setHeight(32);
      ws.column(1).setWidth(20);

      // Add project rows
      const aDetailTabPromises = [];
      projectPunchListRows.sort((a, b) => {
        if (a.truckName < b.truckName) return -1;
        if (a.truckName > b.truckName) return 1;
        return a.projectName < b.projectName ? -1 : 1;
      });

      projectPunchListRows.forEach((project, rowIndex) => {
        ws.row(rowIndex + 2).setHeight(32);
        if (project.error) {
          ws.cell(rowIndex + 2, 1).string(
            projectIdNameMap[project.id] || `Project ID: ${project.id}`
          );
          ws.cell(rowIndex + 2, 2)
            .string('Error preparing report')
            .style(verticalCenterStyle);
          return;
        }
        ws.cell(rowIndex + 2, 1)
          .string(project.truckName || 'Error')
          .style(boldStyle)
          .style(verticalCenterStyle)
          .style(
            project.truckColor
              ? {
                  fill: {
                    type: 'pattern',
                    patternType: 'solid',
                    fgColor: project.truckColor,
                  },
                  font: { color: 'white' },
                }
              : {}
          );
        ws.cell(rowIndex + 2, 2)
          .string(project.projectName || 'Error')
          .style(boldStyle)
          .style(verticalCenterStyle);

        attrs.forEach((attr, colIndex) => {
          ws.column(colIndex + 2).setWidth(25);
          const cellText = getTextFromStatus(project[attr]);
          ws.cell(rowIndex + 2, colIndex + 3)
            .string(cellText)
            .style({
              fill: {
                type: 'pattern',
                patternType: 'solid',
                fgColor: getColor(project[attr]?.color),
              },
              alignment: {
                horizontal: cellText.length > 10 ? 'left' : 'center',
                vertical: 'center',
              },
            });
        });

        const ws1 = wb.addWorksheet(project.projectName);
        aDetailTabPromises.push(createProjectPunchDetailTab(project.id, ws1));
      });

      await Promise.all(aDetailTabPromises);

      // Write the file to local file system
      const dt = moment().format('MMM D, YYYY');
      const fileName = `${name} - ${dt}.xlsx`;
      const buffer = await wb.writeToBuffer();

      await fs.promises.writeFile(fileName, buffer);

      try {
        await sendMonitoringPunchlistReportEmail({
          emailAddresses,
          title: name,
          date: dt,
          projects: projectPunchListRows.map((p) => projectIdNameMap[p.id]),
          filePath: fileName,
        });
      } catch (error) {
        console.error('Error sending punchlist report', error);
        done(new Error('Error sending punchlist report', error));
      } finally {
        try {
          await fs.promises.unlink(fileName);
        } catch (err1) {
          console.error(
            'Error deleting monitoring punchlist report after sending email',
            fileName,
            err1
          );
        }
      }

      done();
    } catch (error) {
      console.error('Error processing punchlist report', error);
      done(error);
    }
  });

  checkMillenniumTrustAccountCashBalancesQueue.process(
    maxJobsPerWorker,
    async (job, done) => {
      const sendAlert = ({ subAccount, balances }) => {
        const {
          lastKnownCustodianCashBalance,
          estimatedBalance,
          inspiraBalance,
        } = balances;
        return SubAccount.findByPk(subAccount.id, {
          include: [
            {
              model: User,
              attributes: ['firstName', 'lastName'],
            },
          ],
        }).then((res) =>
          res.name.then((subAccountName) => {
            SlackService.logToSlack({
              title: `Inspira cash balance has changed or doesn't match what energea displays`,
              type: 'millennium-activity',
              data: [
                {
                  label: 'Account',
                  value: `${res.user?.fullName} - ${subAccountName}`,
                },
                {
                  label: 'Energea displayed balance',
                  value:
                    estimatedBalance === null
                      ? null
                      : numeral(estimatedBalance).format('$0,0[.]00'),
                },
                {
                  label: 'Previous balance',
                  value:
                    lastKnownCustodianCashBalance === null
                      ? null
                      : numeral(lastKnownCustodianCashBalance).format(
                          '$0,0[.]00'
                        ),
                },
                {
                  label: 'Current balance',
                  value:
                    inspiraBalance === null
                      ? null
                      : numeral(inspiraBalance).format('$0,0[.]00'),
                },
              ],
            });
          })
        );
      };

      const subAccounts = await SubAccount.findAll({
        attributes: [
          'id',
          'millenniumTrustAccountGuid',
          'lastKnownCustodianCashBalance',
        ],
        where: {
          millenniumTrustAccountGuid: {
            [Op.not]: null,
          },
          closedDt: null,
        },
      });
      await Promise.all(
        subAccounts.map((subAccount) =>
          Promise.all([
            getMTCEstimatedInvestableBalance(subAccount.id),
            getAccountCashBalance(subAccount.millenniumTrustAccountGuid).catch(
              (err) => {
                console.error(
                  'Error getting inspira account cash balance in cron job',
                  err
                );
                return null;
              }
            ),
          ]).then(([estimatedBalance, inspiraBalance]) => {
            if (inspiraBalance === null) {
              return null;
            }
            if (subAccount.lastKnownCustodianCashBalance === null) {
              const updatedSubAccount = subAccount;
              updatedSubAccount.lastKnownCustodianCashBalance = inspiraBalance;
              return updatedSubAccount.save();
            }

            if (
              parseFloat(inspiraBalance) - 10 >
              parseFloat(estimatedBalance)
            ) {
              // Check if inspira balance is more than $10 greater than estimated balance. Inspira pays interest to accounts during transfer processing times so many accounts have cents in them from this that we can ignore.
              sendAlert({
                subAccount,
                balances: {
                  lastKnownCustodianCashBalance:
                    subAccount.lastKnownCustodianCashBalance,
                  estimatedBalance,
                  inspiraBalance,
                },
              });
            } else if (
              // Check if estimated balance is more than inspira balance. We don't want to allow them to invest more than they have but this will occur after a user funds before Inspira processes incoming funds.
              // If there are recent funding transfers, dividends, or sharetransfers, this would happen also
              parseFloat(estimatedBalance) > parseFloat(inspiraBalance)
            ) {
              const lookBackToDate = moment().subtract(1, 'week').toDate();
              Promise.all([
                MillenniumTrustFundingSession.count({
                  where: {
                    subAccountId: subAccount.id,
                    toMillenniumTransferCancelledDt: null,
                    createdAt: {
                      [Op.gte]: lookBackToDate,
                    },
                  },
                }),
                // NOTE: We only want to consider non-reinvested dividends
                // Dividend.count({
                //   where: {
                //     subAccountId: subAccount.id,
                //     date: {
                //       [Op.gte]: lookBackToDate,
                //     },
                //   },
                // }),
                ShareTransfer.count({
                  where: {
                    sellDt: {
                      [Op.gte]: lookBackToDate,
                    },
                  },
                  include: [
                    {
                      attributes: [],
                      model: SellOrder,
                      where: {
                        subAccountId: subAccount.id,
                      },
                    },
                  ],
                }),
              ]).then(
                ([
                  recentFundingTransferCount,
                  // recentDividendCount,
                  recentShareTransferCount,
                ]) => {
                  if (
                    recentFundingTransferCount === 0 &&
                    // recentDividendCount === 0 &&
                    recentShareTransferCount === 0
                  ) {
                    sendAlert({
                      subAccount,
                      balances: {
                        lastKnownCustodianCashBalance:
                          subAccount.lastKnownCustodianCashBalance,
                        estimatedBalance,
                        inspiraBalance,
                      },
                    });
                  }
                }
              );
            } else if (
              parseFloat(subAccount.lastKnownCustodianCashBalance) !==
              parseFloat(inspiraBalance)
            ) {
              // Check if Inspira cash balance changed from yesterday to today
              sendAlert({
                subAccount,
                balances: {
                  lastKnownCustodianCashBalance:
                    subAccount.lastKnownCustodianCashBalance,
                  estimatedBalance,
                  inspiraBalance,
                },
              });
            }

            // Update last known balance
            if (
              parseFloat(subAccount.lastKnownCustodianCashBalance) !==
              parseFloat(inspiraBalance)
            ) {
              const updatedSubAccount = subAccount;
              updatedSubAccount.lastKnownCustodianCashBalance = inspiraBalance;
              return updatedSubAccount.save();
            }
            return null;
          })
        )
      );
      done();
    }
  );

  checkMillenniumTrustVerificationStatusesQueue.process(
    maxJobsPerWorker,
    async (job, done) => {
      const pendingVerificationSessions =
        await MillenniumTrustAuthSession.findAll({
          attributes: ['id'],
          where: {
            verificationId: {
              [Op.not]: null,
            },
            verificationDocumentStatusPollingFlg: true,
          },
        });

      const aPromises = [];
      pendingVerificationSessions.forEach((verificationSession) => {
        aPromises.push(handleCheckVerificationStatus(verificationSession.id));
      });

      await Promise.all(aPromises);

      done();
    }
  );

  checkMillenniumTrustAccountStatusesQueue.process(1, async (job, done) => {
    const millenniumAccounts = await SubAccount.findAll({
      where: {
        millenniumTrustAccountGuid: {
          [Op.not]: null,
        },
        subAccountTypeId: constants.millenniumTrustSubAccountTypeID,
        closedDt: null,
      },
      include: [
        {
          attributes: ['firstName', 'lastName', 'email', 'dwollaId'],
          required: true,
          model: User,
        },
      ],
    });

    for (let index = 0; index < millenniumAccounts.length; index += 1) {
      const subAccount = millenniumAccounts[parseInt(index, 10)];
      /*
          Closed - Your Millennium Trust account is closed.
          Open - Your Millennium Trust account is open.
          Pending - Millennium Trust has received your request but needs more information to open the account. Please contact us.
        */
      // eslint-disable-next-line no-await-in-loop
      const accountInfo = await getAccountDetails(
        subAccount.millenniumTrustAccountGuid
      ).catch(() => null);
      if (!accountInfo || !accountInfo.status) {
        console.error(
          `MTC API error getting account status for subAccount ${subAccount.id}. Resp: ${accountInfo}`
        );
        return null;
      }
      switch (accountInfo.status) {
        case 'Open': {
          if (subAccount.accountStatus !== 'Open') {
            const updatedSubAccount = subAccount;
            updatedSubAccount.millenniumTrustAccountLast4 =
              accountInfo['account-number-last4'];
            updatedSubAccount.offlinePaymentDetails =
              getMTOfflinePaymentDetailsString(
                accountInfo['account-number-last4']
              );
            // eslint-disable-next-line no-await-in-loop
            await updatedSubAccount.save();

            const formattedIRAType = formatEnergeaIRAType(subAccount.iraType);
            sendIRAAccountOpenedEmail({
              firstName: subAccount.user.firstName,
              lastName: subAccount.user.lastName,
              email: subAccount.user.email,
              iraType: formattedIRAType,
            });
            // eslint-disable-next-line no-await-in-loop
            const subAcctName = await updatedSubAccount.name.catch(
              () => subAccount.id
            );
            HubSpotService.updateSubAccount(updatedSubAccount.hubSpotId, {
              name: subAcctName,
              status: 'Open',
            });
            SlackService.logToSlack({
              title: `Millennium account status changed from '${subAccount.accountStatus}' to 'Open'`,
              url: `${process.env.CMS_HOST}/SubAccount/${subAccount.id}`,
              data: [
                {
                  label: 'User',
                  value: `${subAccount.user.firstName} ${subAccount.user.lastName}`,
                },
                {
                  label: 'SubAccount',
                  value: stringifyObject(subAcctName),
                },
              ],
              type: 'millennium-activity',
            });
          }

          if (
            subAccount.accountStatus === 'Open' &&
            !subAccount.dwollaLabelId
          ) {
            // Let's use this opportunity to ensure all open subAccounts have dwolla labels
            // eslint-disable-next-line no-await-in-loop
            const dwollaLabelResponse = await DwollaService.createLabel({
              dwollaId: subAccount.user.dwollaId,
              amount: 0,
            }).catch((e) => {
              sendIssueEmail({
                description: 'Error create subAccount dwolla label',
                oData: {
                  subAccount,
                  e,
                },
              });
            });
            if (dwollaLabelResponse && dwollaLabelResponse.headers) {
              const location = dwollaLabelResponse.headers.get('location');
              const aLocation = location.split('/');
              const dwollaLabelId = aLocation[aLocation.length - 1];
              const updatedSubAccount = subAccount;
              updatedSubAccount.dwollaLabelId = dwollaLabelId;
              // eslint-disable-next-line no-await-in-loop
              await updatedSubAccount.save().catch((error) => {
                sendIssueEmail({
                  description: 'Error saving subAccount dwollaLabelId',
                  oData: {
                    subAccount: updatedSubAccount,
                    dwollaLabelId,
                    error,
                  },
                });
              });
            }
          }
          break;
        }
        case 'Pending': {
          if (subAccount.accountStatus !== 'Pending') {
            // eslint-disable-next-line no-await-in-loop
            const subAcctName = await subAccount.name.catch(
              () => subAccount.id
            );
            HubSpotService.updateSubAccount(subAccount.hubSpotId, {
              name: subAcctName,
              status: 'Pending',
            });
            SlackService.logToSlack({
              title: `Millennium account status changed from '${subAccount.accountStatus}' to 'Pending'`,
              url: `${process.env.CMS_HOST}/SubAccount/${subAccount.id}`,
              data: [
                {
                  label: 'User',
                  value: `${subAccount.user.firstName} ${subAccount.user.lastName}`,
                },
                // {
                //   label: 'Message',
                //   value:
                //     "We do not expect accounts to become 'Pending' after being opened. If the previous status was either 'Open' or 'Closed', look into whats going on with this account on Millenniums side.",
                // },
                {
                  label: 'SubAccount',
                  value: stringifyObject(subAcctName),
                },
              ],
              type: 'millennium-activity',
            });
          }
          break;
        }
        case 'Closed': {
          if (subAccount.accountStatus !== 'Closed') {
            const updatedSubAccount = subAccount;
            updatedSubAccount.closedDt = new Date();
            // eslint-disable-next-line no-await-in-loop
            await updatedSubAccount.save();

            // eslint-disable-next-line no-await-in-loop
            const subAcctName = await subAccount.name.catch(
              () => subAccount.id
            );
            HubSpotService.updateSubAccount(subAccount.hubSpotId, {
              name: subAcctName,
              status: 'Closed',
            });
            SlackService.logToSlack({
              title: `Millennium account status changed from '${subAccount.accountStatus}' to 'Closed'`,
              url: `${process.env.CMS_HOST}/SubAccount/${subAccount.id}`,
              data: [
                {
                  label: 'User',
                  value: `${subAccount.user.firstName} ${subAccount.user.lastName}`,
                },
                // {
                //   label: 'Message',
                //   value:
                //     "If this is unexpected, reach out to Millennium Trust to re-open this account. Typically Millennium will close an account automatically after 60 days after opening if there hasn't been any action.",
                // },
                {
                  label: 'SubAccount',
                  value: stringifyObject(subAcctName),
                },
              ],
              type: 'millennium-activity',
            });
          }
          break;
        }
        default:
          SlackService.logToSlack({
            title: `Unhandled Millennium account status. Account status changed from '${subAccount.accountStatus}' to '${accountInfo.status}'`,
            data: [
              {
                label: 'User',
                value: `${subAccount.user.firstName} ${subAccount.user.lastName}`,
              },
              {
                label: 'Message',
                value:
                  'Hopefully we never read this message. Handle case by case.',
              },
              {
                label: 'Account Info',
                value: stringifyObject(accountInfo),
              },
              {
                label: 'SubAccount ID',
                value: stringifyObject(subAccount.id),
              },
            ],
            type: 'millennium-activity',
          });
          break;
      }
      if (subAccount.accountStatus !== accountInfo.status) {
        const updatedSubAccount = subAccount;
        updatedSubAccount.accountStatus = accountInfo.status;
        unhideFromIRATracker(subAccount.userId);
        // eslint-disable-next-line no-await-in-loop
        await updatedSubAccount.save();
      }
      return 'success';
    }

    done();
  });

  sendMTCMonthlyReconciliationReportsQueue.process(
    maxJobsPerWorker,
    async (job, done) => {
      const dollarFormat = {
        numberFormat: '$#,##0.00; ($#,##0.00); -',
      };

      // const sharePriceFormat = {
      //   numberFormat: '$#,##0.0000; ($#,##0.0000); -',
      // };

      const shareCountFormat = {
        numberFormat: '#,##0.0000; (#,##0.0000); -',
      };

      /* -------------------------------------------------------------------------- */
      /*                     Dividends not reinvested worksheet                     */
      /* -------------------------------------------------------------------------- */

      const dividendFilterClauses = {
        // $investment$: null, NOTE: Using this filter removed all partially reinvested dividends from the export
      };
      const dividends = await Dividend.findAll({
        attributes: [
          'id',
          'date',
          'value',
          'monthlyPortfolioFinancialActualId',
          'userId',
          'subAccountId',
        ],
        where: dividendFilterClauses,
        order: [['date', 'DESC']],
        include: [
          {
            model: Investment,
            as: 'investment',
            required: false,
            attributes: ['id', 'value'],
            where: { cancelledDt: null },
          },
          {
            attributes: ['id', 'millenniumTrustAccountLast4'],
            model: SubAccount,
            where: {
              subAccountTypeId: constants.millenniumTrustSubAccountTypeID,
              millenniumTrustAccountLast4: {
                [Op.not]: null,
              },
            },
            required: true,
          },
          {
            attributes: ['firstName', 'lastName'],
            model: User,
          },
          {
            model: MonthlyPortfolioFinancialActual,
            attributes: ['portfolioId'],
            include: [
              {
                attributes: ['name'],
                model: Portfolio,
              },
            ],
          },
        ],
      }).catch((err) => {
        console.error(err);
      });

      const dividendWb = new xl.Workbook();
      const ws = dividendWb.addWorksheet();

      ws.column(1).setWidth(20);
      ws.column(2).setWidth(20);
      ws.column(3).setWidth(20);
      ws.column(4).setWidth(30);
      ws.column(5).setWidth(20);

      // Add header row
      ws.cell(1, 1).string('Investor');
      ws.cell(1, 2).string('Account # Last 4');
      ws.cell(1, 3).string('Effective Date');
      ws.cell(1, 4).string('Paying Asset Legal Name');
      ws.cell(1, 5).string('Dividend Amount');

      dividends
        .filter(
          (dividend) =>
            // Exclude fully reinvested dividends
            !dividend.investment ||
            parseFloat(dividend.investment?.value) < parseFloat(dividend.value)
        )
        .forEach((dividend, rowIndex) => {
          ws.cell(rowIndex + 2, 1).string(dividend.user.fullName);
          ws.cell(rowIndex + 2, 2).string(
            dividend.subAccount.millenniumTrustAccountLast4
          );
          ws.cell(rowIndex + 2, 3).date(dividend.date);
          ws.cell(rowIndex + 2, 4).string(
            dividend.monthlyPortfolioFinancialActual.portfolio.name
          );
          ws.cell(rowIndex + 2, 5)
            .number(
              // Include only the value that was not reinvested if any
              parseFloat(
                parseFloat(dividend.value) -
                  parseFloat(dividend.investment?.value || 0)
              )
            )
            .style(dollarFormat);
        });

      // Write the file to local file system
      const fileName = `Dividends Paid Out ${moment().format(
        'YYYY-MM-DD'
      )}.xlsx`;
      const buffer = await dividendWb.writeToBuffer();
      fs.writeFileSync(fileName, buffer);

      /* -------------------------------------------------------------------------- */
      /*                           Share prices worksheet                           */
      /* -------------------------------------------------------------------------- */

      const portfolios = await Portfolio.findAll({
        attributes: ['id'],
        where: {
          millenniumTrustAssetId: {
            [Op.not]: null,
          },
        },
      });
      const mtcAssetLatestSharePrices = await Promise.all(
        portfolios.map((portfolio) =>
          PortfolioSharePrice.findOne({
            where: {
              portfolioId: portfolio.id,
              date: {
                [Op.lte]: moment().startOf('day'),
              },
            },
            attributes: ['sharePrice', 'date'],
            include: [{ model: Portfolio, attributes: ['name'] }],
            limit: 1,
            order: [['date', 'desc']],
          })
        )
      );

      const sharePriceWb = new xl.Workbook();
      const ws1 = sharePriceWb.addWorksheet();

      ws1.column(1).setWidth(30);
      ws1.column(2).setWidth(15);
      ws1.column(3).setWidth(15);

      // Add header row
      ws1.cell(1, 1).string('Asset Name');
      ws1.cell(1, 2).string('Share Price');
      ws1.cell(1, 3).string('Effective Date');

      mtcAssetLatestSharePrices.forEach((sharePriceRecord, rowIndex) => {
        ws1.cell(rowIndex + 2, 1).string(sharePriceRecord.portfolio.name);
        ws1
          .cell(rowIndex + 2, 2)
          .string(numeral(sharePriceRecord.sharePrice).format('$0,0.[000000]'));
        ws1.cell(rowIndex + 2, 3).date(sharePriceRecord.date);
      });

      // Write the file to local file system
      const fileName1 = `Asset Share Prices ${moment().format(
        'YYYY-MM-DD'
      )}.xlsx`;
      const buffer1 = await sharePriceWb.writeToBuffer();
      fs.writeFileSync(fileName1, buffer1);

      /* -------------------------------------------------------------------------- */
      /*                   Account Asset Reconciliation worksheet                   */
      /* -------------------------------------------------------------------------- */

      const accountAssetWb = new xl.Workbook();
      const ws2 = accountAssetWb.addWorksheet();

      ws2.column(1).setWidth(30);
      ws2.column(2).setWidth(15);
      ws2.column(3).setWidth(30);
      ws2.column(4).setWidth(15);
      ws2.column(5).setWidth(15);
      ws2.column(6).setWidth(15);
      ws2.column(7).setWidth(30);
      ws2.column(8).setWidth(30);
      ws2.column(9).setWidth(35);
      ws2.column(10).setWidth(30);
      ws2.column(11).setWidth(35);
      ws2.column(12).setWidth(30);

      // Add header row
      ws2.cell(1, 1).string('Investor');
      ws2.cell(1, 2).string('Account # Last 4');
      ws2.cell(1, 3).string('Asset Legal Name');
      ws2.cell(1, 4).string('Effective Date');
      ws2.cell(1, 5).string('Current NAV');
      ws2.cell(1, 6).string('Cost Basis');
      ws2.cell(1, 7).string('Total Dividends to Date');
      ws2.cell(1, 8).string('Total Reinvested Dividends to Date');
      ws2.cell(1, 9).string('Total Non-reinvested Dividends to Date');
      ws2.cell(1, 10).string('Current Shares Owned');
      ws2.cell(1, 11).string('Total Sold Value to Date (return of capital)');
      ws2.cell(1, 12).string('Total Sold Shares to Date');

      const subAccounts = await SubAccount.findAll({
        attributes: ['id', 'millenniumTrustAccountLast4'],
        where: {
          millenniumTrustAccountLast4: {
            [Op.not]: null,
          },
          subAccountTypeId: constants.millenniumTrustSubAccountTypeID,
        },
        include: [{ model: User, attributes: ['id', 'firstName', 'lastName'] }],
      });

      let rowIndex = 2;
      // Loop through this synchronously because theres no need for speed and this could eat up a lot of memory as it scales
      for (let index = 0; index < subAccounts.length; index += 1) {
        const subAccount = subAccounts[parseInt(index, 10)];
        // eslint-disable-next-line no-await-in-loop
        const userPortfolioInvestments =
          await UserService.getUserPortfolioInvestments(subAccount.user, {
            accountFilter: {
              allAccounts: false,
              subAccountId: subAccount.id,
            },
          });
        for (
          let index2 = 0;
          index2 < userPortfolioInvestments.length;
          index2 += 1
        ) {
          const portfolioInvestment =
            userPortfolioInvestments[parseInt(index2, 10)];
          ws2
            .cell(rowIndex, 1)
            .string(`${subAccount.user.firstName} ${subAccount.user.lastName}`);
          ws2.cell(rowIndex, 2).string(subAccount.millenniumTrustAccountLast4);
          ws2.cell(rowIndex, 3).string(portfolioInvestment.portfolio.name);
          ws2.cell(rowIndex, 4).date(moment());
          ws2
            .cell(rowIndex, 5)
            .number(portfolioInvestment.nav)
            .style(dollarFormat);
          ws2
            .cell(rowIndex, 6)
            .number(portfolioInvestment.costBasis)
            .style(dollarFormat);
          ws2
            .cell(rowIndex, 7)
            .number(portfolioInvestment.dividendSum)
            .style(dollarFormat);
          ws2
            .cell(rowIndex, 8)
            .number(portfolioInvestment.dividendsAutoReinvested)
            .style(dollarFormat);
          ws2
            .cell(rowIndex, 9)
            .number(
              portfolioInvestment.dividendSum -
                portfolioInvestment.dividendsAutoReinvested
            )
            .style(dollarFormat);
          ws2
            .cell(rowIndex, 10)
            .number(portfolioInvestment.shareSum)
            .style(shareCountFormat);
          ws2
            .cell(rowIndex, 11)
            .number(portfolioInvestment.soldValue)
            .style(dollarFormat);
          ws2
            .cell(rowIndex, 12)
            .number(portfolioInvestment.sharesSold)
            .style(shareCountFormat);

          rowIndex += 1;
        }
      }

      // Write the file to local file system
      const fileName2 = `Account Asset Reconciliation ${moment().format(
        'YYYY-MM-DD'
      )}.xlsx`;
      const buffer2 = await accountAssetWb.writeToBuffer();
      fs.writeFileSync(fileName2, buffer2);

      /* -------------------------------------------------------------------------- */
      /*                       Dividends reinvested worksheet                       */
      /* -------------------------------------------------------------------------- */

      const reinvestedDividends = await Dividend.findAll({
        attributes: [
          'id',
          'date',
          'value',
          'monthlyPortfolioFinancialActualId',
          'userId',
          'subAccountId',
        ],
        where: {
          // date: {
          //   [Op.gte]: moment().add(-1, 'month').toDate(),
          // },
        },
        order: [['date', 'DESC']],
        include: [
          {
            model: Investment,
            as: 'investment',
            required: true,
            attributes: ['id', 'startDt', 'value', 'shares'],
            where: { cancelledDt: null },
            include: [{ model: Portfolio, attributes: ['name'] }],
          },
          {
            attributes: ['id', 'millenniumTrustAccountLast4'],
            model: SubAccount,
            where: {
              subAccountTypeId: constants.millenniumTrustSubAccountTypeID,
              millenniumTrustAccountLast4: {
                [Op.not]: null,
              },
            },
            required: true,
          },
          {
            attributes: ['firstName', 'lastName'],
            model: User,
          },
          {
            model: MonthlyPortfolioFinancialActual,
            attributes: ['portfolioId'],
            include: [
              {
                attributes: ['name'],
                model: Portfolio,
              },
            ],
          },
        ],
      }).catch((err) => {
        console.error(err);
      });

      const reinvestedDividendWb = new xl.Workbook();
      const ws3 = reinvestedDividendWb.addWorksheet();

      ws3.column(1).setWidth(20);
      ws3.column(2).setWidth(20);
      ws3.column(3).setWidth(20);
      ws3.column(4).setWidth(30);
      ws3.column(5).setWidth(30);
      ws3.column(6).setWidth(20);
      ws3.column(7).setWidth(20);
      ws3.column(8).setWidth(20);
      ws3.column(9).setWidth(20);

      // Add header row
      ws3.cell(1, 1).string('Investor');
      ws3.cell(1, 2).string('Account # Last 4');
      ws3.cell(1, 3).string('Effective Date');
      ws3.cell(1, 4).string('Paying Asset Legal Name');
      ws3.cell(1, 5).string('Receiving Asset Legal Name');
      ws3.cell(1, 6).string('Dividend Amount');
      ws3.cell(1, 7).string('Reinvested Amount');
      ws3.cell(1, 8).string('Shares');
      ws3.cell(1, 9).string('Share Price');

      reinvestedDividends.forEach((dividend, index) => {
        ws3.cell(index + 2, 1).string(dividend.user.fullName);
        ws3
          .cell(index + 2, 2)
          .string(dividend.subAccount.millenniumTrustAccountLast4 || '');
        ws3.cell(index + 2, 3).date(dividend.date);
        ws3
          .cell(index + 2, 4)
          .string(dividend.monthlyPortfolioFinancialActual.portfolio.name);
        ws3.cell(index + 2, 5).string(dividend.investment.portfolio.name);
        ws3
          .cell(index + 2, 6)
          .number(parseFloat(dividend.value))
          .style(dollarFormat);
        ws3
          .cell(index + 2, 7)
          .number(parseFloat(dividend.investment.value))
          .style(dollarFormat);
        ws3
          .cell(index + 2, 8)
          .number(parseFloat(dividend.investment.shares))
          .style(shareCountFormat);
        ws3
          .cell(index + 2, 9)
          .string(
            numeral(
              dividend.investment.value / dividend.investment.shares
            ).format('$0,0.[000000]')
          );
      });

      // Write the file to local file system
      const fileName3 = `Reinvested Dividends ${moment().format(
        'YYYY-MM-DD'
      )}.xlsx`;
      const buffer3 = await reinvestedDividendWb.writeToBuffer();
      fs.writeFileSync(fileName3, buffer3);

      /* -------------------------------------------------------------------------- */
      /*                       Completed Sell Orders worksheet                      */
      /* -------------------------------------------------------------------------- */

      const sellOrders = await SellOrder.findAll({
        attributes: [
          'id',
          [fn('sum', col('shareTransfers.soldShares')), 'totalSoldShares'],
          [fn('sum', col('shareTransfers.value')), 'totalSoldValue'],
          [fn('max', col('shareTransfers.sellDt')), 'mostRecentSellDate'],
        ],
        where: {
          closedDt: {
            [Op.not]: null,
          },
          cancelledDt: null,
          shares: {
            [Op.gt]: 0,
          },
        },
        raw: true,
        group: ['sellOrder.id', 'subAccount.id', 'user.id', 'portfolio.id'],
        order: [['closedDt', 'DESC']],
        include: [
          {
            model: ShareTransfer,
            required: true,
            attributes: [],
            include: [
              {
                model: Investment,
                required: true,
                attributes: [],
                where: { cancelledDt: null },
              },
            ],
          },
          {
            attributes: ['id', 'millenniumTrustAccountLast4'],
            model: SubAccount,
            where: {
              subAccountTypeId: constants.millenniumTrustSubAccountTypeID,
              millenniumTrustAccountLast4: {
                [Op.not]: null,
              },
            },
            required: true,
          },
          {
            model: User,
            required: true,
            attributes: ['id', 'firstName', 'lastName'],
          },
          {
            model: Portfolio,
            required: true,
            attributes: ['id', 'name'],
          },
        ],
      }).catch((err) => {
        console.error(err);
      });

      const soldSharesWb = new xl.Workbook();
      const ws4 = soldSharesWb.addWorksheet();

      ws4.column(1).setWidth(20);
      ws4.column(2).setWidth(20);
      ws4.column(3).setWidth(20);
      ws4.column(4).setWidth(30);
      ws4.column(5).setWidth(20);
      ws4.column(6).setWidth(20);
      ws4.column(7).setWidth(20);
      ws4.column(8).setWidth(20);
      ws4.column(9).setWidth(20);

      // Add header row
      ws4.cell(1, 1).string('Investor');
      ws4.cell(1, 2).string('Account # Last 4');
      ws4.cell(1, 3).string('Effective Date');
      ws4.cell(1, 4).string('Asset Legal Name');
      ws4.cell(1, 5).string('Sold Shares');
      ws4.cell(1, 6).string('Sold Value');
      ws4.cell(1, 7).string('Average Share Price');
      ws4.cell(1, 8).string('Holdback Amount');
      ws4.cell(1, 9).string('Remaining NAV');

      const sellOrderPromises = [];
      sellOrders.forEach((sellOrder, rowIndex2) => {
        ws4
          .cell(rowIndex2 + 2, 1)
          .string(
            `${sellOrder['user.firstName']} ${sellOrder['user.lastName']}`
          );
        ws4
          .cell(rowIndex2 + 2, 2)
          .string(sellOrder['subAccount.millenniumTrustAccountLast4']);
        ws4.cell(rowIndex2 + 2, 3).date(sellOrder.mostRecentSellDate);
        ws4.cell(rowIndex2 + 2, 4).string(sellOrder['portfolio.name']);
        ws4
          .cell(rowIndex2 + 2, 5)
          .number(parseFloat(sellOrder.totalSoldShares));
        ws4
          .cell(rowIndex2 + 2, 6)
          .number(parseFloat(sellOrder.totalSoldValue))
          .style(dollarFormat);
        ws4
          .cell(rowIndex2 + 2, 7)
          .number(
            parseFloat(sellOrder.totalSoldValue / sellOrder.totalSoldShares)
          )
          .style(dollarFormat);
        ws4
          .cell(rowIndex2 + 2, 8)
          .number(0)
          .style(dollarFormat);
        sellOrderPromises.push(
          User.findByPk(sellOrder['user.id'], { attributes: ['id'] }).then(
            (dbUser) =>
              UserService.getNAV(dbUser, {
                portfolioId: sellOrder['portfolio.id'],
                accountFilter: {
                  allAccounts: false,
                  subAccountId: sellOrder['subAccount.id'],
                },
              }).then((res) => {
                ws4
                  .cell(rowIndex2 + 2, 9)
                  .number(parseFloat(res || 0))
                  .style(dollarFormat);
              })
          )
        );
      });

      await Promise.all(sellOrderPromises);

      // Write the file to local file system
      const fileName4 = `Sold Shares ${moment().format('YYYY-MM-DD')}.xlsx`;
      const buffer4 = await soldSharesWb.writeToBuffer();
      fs.writeFileSync(fileName4, buffer4);

      /* -------------------------------------------------------------------------- */
      /*                         Promo investments worksheet                        */
      /* -------------------------------------------------------------------------- */

      const promoInvestments = await Investment.findAll({
        attributes: ['id', 'startDt', 'value', 'shares'],
        where: {
          cancelledDt: null,
          promoRewardCodeId: {
            [Op.not]: null,
          },
        },
        order: [['startDt', 'DESC']],
        include: [
          {
            attributes: ['id', 'millenniumTrustAccountLast4'],
            model: SubAccount,
            where: {
              subAccountTypeId: constants.millenniumTrustSubAccountTypeID,
              millenniumTrustAccountLast4: {
                [Op.not]: null,
              },
            },
            required: true,
          },
          {
            attributes: ['firstName', 'lastName'],
            model: User,
          },
          {
            attributes: ['name'],
            model: Portfolio,
          },
        ],
      }).catch((err) => {
        console.error(err);
      });

      const promoInvestmentsWb = new xl.Workbook();
      const ws5 = promoInvestmentsWb.addWorksheet();

      ws5.column(1).setWidth(20);
      ws5.column(2).setWidth(20);
      ws5.column(3).setWidth(20);
      ws5.column(4).setWidth(30);
      ws5.column(5).setWidth(22);
      ws5.column(6).setWidth(20);
      ws5.column(7).setWidth(20);

      // Add header row
      ws5.cell(1, 1).string('Investor');
      ws5.cell(1, 2).string('Account # Last 4');
      ws5.cell(1, 3).string('Effective Date');
      ws5.cell(1, 4).string('Asset Legal Name');
      ws5.cell(1, 5).string('Promo Investment Value');
      ws5.cell(1, 6).string('Shares');
      ws5.cell(1, 7).string('Share Price');

      promoInvestments.forEach((investment, index) => {
        ws5.cell(index + 2, 1).string(investment.user.fullName);
        ws5
          .cell(index + 2, 2)
          .string(investment.subAccount.millenniumTrustAccountLast4 || '');
        ws5.cell(index + 2, 3).date(investment.startDt);
        ws5.cell(index + 2, 4).string(investment.portfolio.name);
        ws5
          .cell(index + 2, 5)
          .number(parseFloat(investment.value))
          .style(dollarFormat);
        ws5
          .cell(index + 2, 6)
          .number(parseFloat(investment.shares))
          .style(shareCountFormat);
        ws5
          .cell(index + 2, 7)
          .string(
            numeral(investment.value / investment.shares).format(
              '$0,0.[000000]'
            )
          );
      });

      // Write the file to local file system
      const fileName5 = `Promo Investments ${moment().format(
        'YYYY-MM-DD'
      )}.xlsx`;
      const buffer5 = await promoInvestmentsWb.writeToBuffer();
      fs.writeFileSync(fileName5, buffer5);

      /* -------------------------------------------------------------------------- */

      const filePaths = [
        fileName,
        fileName1,
        fileName2,
        fileName3,
        fileName4,
        fileName5,
      ];
      // send email w sendgrid
      try {
        sendMTMonthlyReconciliationReportsEmail({
          filePaths,
        }).then(() => {
          filePaths.forEach((filePath) => {
            fs.unlink(filePath, (err) => {
              if (err) {
                console.error(
                  'Error deleting MT monthly reconciliation reports after sending email',
                  filePath,
                  err
                );
              }
            });
          });
        });
      } catch (error) {
        console.error('Error sending MT monthly reconciliation reports', error);
        done(
          new Error('Error sending MT monthly reconciliation reports', error)
        );
      }

      done();
    }
  );

  createMatchPromosIfEligibleQueue.process(
    maxJobsPerWorker,
    async (job, done) => {
      try {
        const { userId } = job.data;
        const daysSince = 90;

        // Get user and include past match promos
        const user = await User.findByPk(userId, {
          attributes: ['id', 'firstName', 'lastName', 'email'],
          include: [
            {
              model: PromoCode,
              attributes: ['id'],
              required: false,
              where: {
                promoType: {
                  [Op.in]: [
                    constants.greenLevelMatchPromo.typeName,
                    constants.goldLevelMatchPromo.typeName,
                    constants.platinumLevelMatchPromo.typeName,
                    constants.foundersCardMatchPromo.typeName,
                    constants.holiday2024Promo.typeName,
                  ],
                },
                // TODO: We don't want the below filters because as soon as someone completes
                // a promo, we would create a new one for them. Add a check to see when it was
                // completed.
                // completedDt: null,
                // expirationDt: {
                //   [Op.gt]: moment().toDate(),
                // },
              },
            },
          ],
        });

        if (!user) {
          sendIssueEmail({
            description: 'User not found in createMatchPromosIfEligibleQueue',
            oData: { userId },
          });
          done();
          return;
        }

        if (user.promoCodes?.length > 0) {
          console.warn(
            `User ${user.id} already has ${user.promoCodes.length} match promo(s)`
          );
          done();
          return;
        }

        const [costBasis, totalInvestedLastNDays] = await Promise.all([
          UserService.costBasis({ user }),
          Investment.findAll({
            attributes: [
              [Sequelize.fn('sum', Sequelize.col('value')), 'totalInvested'],
            ],
            where: {
              cancelledDt: null,
              userId: user.id,
              startDt: {
                [Op.gt]: moment().subtract(daysSince, 'days').toDate(),
              },
            },
          }).then((res) => parseFloat(res[0].dataValues.totalInvested || 0)),
        ]);

        const processCreatePromo = async (promoConfig, color) => {
          // Create promo code
          await PromoCode.create({
            userId: user.id,
            promoType: promoConfig.typeName,
            expirationDt: moment('2024-03-31', 'YYYY-MM-DD')
              .endOf('day')
              .toDate(),
          }).then(() => {
            SlackService.logToSlack({
              title: `Match Promo Created for Investor`,
              type: 'platform-info',
              data: [
                {
                  label: 'User',
                  value: `${user.firstName} ${user.lastName}`,
                },
                {
                  label: 'Promo Type',
                  value: promoConfig.typeName,
                },
              ],
            });
          });
          // Send email
          sendMatchPromoCodeGrantedEmail({
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
            minInvestmentString: numeral(promoConfig.minInvestment).format(
              '$0,0'
            ),
            portfolioName: promoConfig.portfolioName,
            promoColor: color,
            matchPercentageString: numeral(promoConfig.percentageMatch).format(
              '0[.]0%'
            ),
          });
        };

        if (
          costBasis >
          constants.platinumLevelMatchPromo.eligibility.totalInvestedLowerBound
        ) {
          const tenPercentOfLowerBound =
            constants.platinumLevelMatchPromo.eligibility
              .totalInvestedLowerBound * 0.1;
          if (
            totalInvestedLastNDays < Math.min(10_000, tenPercentOfLowerBound)
          ) {
            await processCreatePromo(
              constants.platinumLevelMatchPromo,
              'Platinum'
            );
          }
        } else if (
          costBasis >
          constants.goldLevelMatchPromo.eligibility.totalInvestedLowerBound
        ) {
          const tenPercentOfLowerBound =
            constants.goldLevelMatchPromo.eligibility.totalInvestedLowerBound *
            0.1;
          if (
            totalInvestedLastNDays < Math.min(10_000, tenPercentOfLowerBound)
          ) {
            await processCreatePromo(constants.goldLevelMatchPromo, 'Gold');
          }
        } else if (
          costBasis >
          constants.greenLevelMatchPromo.eligibility.totalInvestedLowerBound
        ) {
          const tenPercentOfLowerBound =
            constants.greenLevelMatchPromo.eligibility.totalInvestedLowerBound *
            0.1;
          if (
            totalInvestedLastNDays < Math.min(10_000, tenPercentOfLowerBound)
          ) {
            await processCreatePromo(constants.greenLevelMatchPromo, 'Green');
          }
        } else {
          console.log('User not eligible for match promos');
        }
        done();
        return;
      } catch (err) {
        sendIssueEmail({
          description: `Error creating match promos if eligible`,
          oData: {
            error: err,
          },
        });
        done(err);
      }
    }
  );
}

// Initialize the clustered worker process
// See: https://devcenter.heroku.com/articles/node-concurrency for more info
throng({
  workers,
  start,
});
