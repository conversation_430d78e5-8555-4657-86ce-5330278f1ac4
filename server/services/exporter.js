import fs from 'fs';
import xl from 'excel4node';
import moment from 'moment';

import { oktaClient } from './okta';
import { isPromise, sleep, stringifyObject } from '../../utils/global';
import { sendExportedCMSList } from './mail';
import UserService from './user';
import database from '../models/index';
import InvestmentService from './investment';
import { writeToCell } from './excel';
import SlackService from './slack';
import {
  getBrInvoicePaymentStatus,
  getConsumerUnitStartDt,
  getCurrentDiscountRate,
  getCurrentTermsOfAdhesionFromBrInvoiceId,
} from './creditManagement';
import { getSignedUrl } from './aws';
import { Op } from 'sequelize';

const BrContact = database.models.brContact;
const BrConsumerUnit = database.models.brConsumerUnit;
const BrSalesPerson = database.models.brSalesPerson;
const Portfolio = database.models.portfolio;
const SalesforceProject = database.models.salesforceProject;
const User = database.models.user;

const brInvoiceColumns = [
  {
    label: 'Id',
    getValue: (record) => record.id,
    dataType: 'number',
  },
  {
    label: 'Billing cycle',
    getValue: (record) =>
      record.brBillingCycle?.label ||
      record.getBrBillingCycle().then((res) => res.label),
    dataType: 'string',
  },
  {
    label: 'Consumer unit',
    getValue: (record) =>
      record.brConsumerUnit?.name ||
      record
        .getBrCreditCompensation({
          include: [{ model: BrConsumerUnit, attributes: ['name'] }],
        })
        .then((res) => res?.brConsumerUnit?.name),
    dataType: 'string',
  },
  {
    label: 'Self-consumption offtaker',
    getValue: (record) => {
      if (record.brSelfConsumptionOfftakerId) {
        return (
          record.brSelfConsumptionOfftaker?.name ||
          record
            .getBrSelfConsumptionOfftaker({ attributes: ['name'] })
            .then((res) => res?.name)
        );
      }
      return null;
    },
    dataType: 'string',
  },
  {
    label: 'Payment status',
    getValue: (record) => getBrInvoicePaymentStatus(record)?.label,
    dataType: 'string',
  },
  {
    label: 'Amount due',
    getValue: (record) => record.amountDue,
    dataType: 'number',
  },
  {
    label: 'Amount paid',
    getValue: (record) => record.amountPaid,
    dataType: 'number',
  },
  {
    label: 'Payment processing fees',
    getValue: (record) => record.paymentProcessingFees,
    dataType: 'number',
  },
  {
    label: 'Due date',
    getValue: (record) =>
      record.dueDt && moment(record.dueDt).format('DD/MM/YYYY'),
    dataType: 'string',
  },
  {
    label: 'Original Due date',
    getValue: (record) =>
      record.originalDueDt && moment(record.originalDueDt).format('DD/MM/YYYY'),
    dataType: 'string',
  },
  {
    label: 'Invoice sent date',
    getValue: (record) =>
      record.invoiceSentDt && moment(record.invoiceSentDt).format('DD/MM/YYYY'),
    dataType: 'string',
  },
  {
    label: 'Invoice paid date',
    getValue: (record) =>
      record.invoicePaidDt && moment(record.invoicePaidDt).format('DD/MM/YYYY'),
    dataType: 'string',
  },
  {
    label: 'Cancelled date',
    getValue: (record) =>
      record.cancelledDt && moment(record.cancelledDt).format('DD/MM/YYYY'),
    dataType: 'string',
  },
  {
    label: 'Sent to SERASA date',
    getValue: (record) =>
      record.sentToCollectionsDt &&
      moment(record.sentToCollectionsDt).format('DD/MM/YYYY'),
    dataType: 'string',
  },
  {
    label: 'Created Date',
    getValue: (record) =>
      record.createdAt &&
      moment(record.createdAt).format('MMM D, YYYY HH:mm:ss'),
    dataType: 'string',
  },
  {
    label: 'Current Terms of Adhesion',
    getValue: (record) =>
      record.brConsumerUnit?.currentBrTermsOfAdhesion?.label ||
      getCurrentTermsOfAdhesionFromBrInvoiceId(record.id).then(
        (res) => res?.label
      ),
    dataType: 'string',
  },
  {
    label: 'Discount Rate (Contracted)',
    getValue: (record) =>
      record.brConsumerUnit?.currentBrTermsOfAdhesion?.brPowerPlan
        ?.discountRate ||
      getCurrentTermsOfAdhesionFromBrInvoiceId(record.id).then((res) =>
        res
          ?.getBrPowerPlan({ attributes: ['discountRate'] })
          .then((res2) => res2?.discountRate)
      ),
    dataType: 'number',
  },
  {
    label: 'Discount Rate (Realized)',
    getValue: (record) =>
      record?.brCreditCompensation?.discountRate ||
      record
        .getBrCreditCompensation({ attributes: ['discountRate'] })
        .then((res) => res?.discountRate),
    dataType: 'number',
  },
  {
    label: 'Project',
    getValue: (record) =>
      record?.brBillingCycle?.salesforceProject?.name ||
      record
        .getBrBillingCycle({
          include: [{ model: SalesforceProject, attributes: ['name'] }],
        })
        .then((res) => res?.salesforceProject?.name),
    dataType: 'string',
  },
  {
    label: 'Injected Electricity Price',
    getValue: (record) =>
      record?.brCreditCompensation?.injectedElectricityPrice ||
      record
        .getBrCreditCompensation({ attributes: ['injectedElectricityPrice'] })
        .then((res) => res?.injectedElectricityPrice),
    dataType: 'number',
  },
  {
    label: 'Utility Electricity Price',
    getValue: (record) =>
      record?.brCreditCompensation?.utilityElectricityPrice ||
      record
        .getBrCreditCompensation({ attributes: ['utilityElectricityPrice'] })
        .then((res) => res?.utilityElectricityPrice),
    dataType: 'number',
  },
  {
    label: 'Energea Electricity Price',
    getValue: (record) =>
      record?.brCreditCompensation?.discountedInjectedElectricityPrice ||
      record
        .getBrCreditCompensation()
        .then((res) => res?.discountedInjectedElectricityPrice),
    dataType: 'number',
  },
  {
    label: 'Sales Partner',
    getValue: (record) =>
      record
        .getBrCreditCompensation({
          attributes: ['id'],
          include: [
            {
              model: BrConsumerUnit,
              attributes: ['id'],
              include: [{ model: BrSalesPerson, attributes: ['name'] }],
            },
          ],
        })
        .then((res) => res?.brConsumerUnit?.brSalesPerson?.name),
    dataType: 'string',
  },
  {
    label: 'Sales Person',
    getValue: (record) =>
      record
        .getBrCreditCompensation({
          attributes: ['id'],
          include: [
            {
              model: BrConsumerUnit,
              attributes: ['id'],
              include: [
                {
                  model: BrContact,
                  as: 'salesPersonBrContact',
                  attributes: ['firstName', 'lastName'],
                },
              ],
            },
          ],
        })
        .then((res) => res?.brConsumerUnit?.salesPersonBrContact?.fullName),
    dataType: 'string',
  },
  {
    label: 'Admin Upfront Commission Rate',
    getValue: (record) => {
      if (record.brConsumerUnit?.currentBrTermsOfAdhesion?.brPowerPlan) {
        return (
          record.brConsumerUnit.currentBrTermsOfAdhesion.brPowerPlan
            .adminUpfrontCommissionRate || 0
        );
      }
      return getCurrentTermsOfAdhesionFromBrInvoiceId(record.id).then((res) =>
        res
          ?.getBrPowerPlan({ attributes: ['adminUpfrontCommissionRate'] })
          .then((res2) => res2?.adminUpfrontCommissionRate)
      );
    },
    dataType: 'number',
  },
  {
    label: 'Sales Person Upfront Commission Rate',
    getValue: (record) => {
      if (record.brConsumerUnit?.currentBrTermsOfAdhesion?.brPowerPlan) {
        return (
          record.brConsumerUnit.currentBrTermsOfAdhesion.brPowerPlan
            .salesPersonUpfrontCommissionRate || 0
        );
      }
      return getCurrentTermsOfAdhesionFromBrInvoiceId(record.id).then((res) =>
        res
          ?.getBrPowerPlan({ attributes: ['salesPersonUpfrontCommissionRate'] })
          .then((res2) => res2?.salesPersonUpfrontCommissionRate)
      );
    },
    dataType: 'number',
  },
  {
    label: 'Admin Residual Commission Rate',
    getValue: (record) => {
      if (record.brConsumerUnit?.currentBrTermsOfAdhesion?.brPowerPlan) {
        return (
          record.brConsumerUnit.currentBrTermsOfAdhesion.brPowerPlan
            .adminResidualCommissionRate || 0
        );
      }
      return getCurrentTermsOfAdhesionFromBrInvoiceId(record.id).then((res) =>
        res
          ?.getBrPowerPlan({ attributes: ['adminResidualCommissionRate'] })
          .then((res2) => res2?.adminResidualCommissionRate)
      );
    },
    dataType: 'number',
  },
  {
    label: 'Sales Person Residual Commission Rate',
    getValue: (record) => {
      if (record.brConsumerUnit?.currentBrTermsOfAdhesion?.brPowerPlan) {
        return (
          record.brConsumerUnit.currentBrTermsOfAdhesion.brPowerPlan
            .salesPersonResidualCommissionRate || 0
        );
      }
      return getCurrentTermsOfAdhesionFromBrInvoiceId(record.id).then((res) =>
        res
          ?.getBrPowerPlan({
            attributes: ['salesPersonResidualCommissionRate'],
          })
          .then((res2) => res2?.salesPersonResidualCommissionRate)
      );
    },
    dataType: 'number',
  },
];

const brConsumerUnitColumns = [
  {
    label: 'Número de Instalação',
    getValue: (record) => record.installationCode,
    dataType: 'string',
  },
  {
    label: 'Número de Cliente',
    getValue: (record) => record.utilityCustomerCode,
    dataType: 'string',
  },
  {
    label: 'Tipo',
    getValue: (record) =>
      record.brCustomer?.type ||
      record
        .getBrCustomer({
          attributes: ['type'],
        })
        .then((res) => res?.type),
    dataType: 'string',
  },
  {
    label: 'Documento',
    getValue: (record) => {
      if (record.brCustomer?.type) {
        return record.brCustomer?.type?.toLowerCase() === 'cpf'
          ? record.brCustomer.cpf
          : record.brCustomer.cnpj;
      }
      return record
        .getBrCustomer({
          attributes: ['type', 'cpf', 'cnpj'],
        })
        .then((res) =>
          res?.type?.toLowerCase() === 'cpf' ? res.cpf : res.cnpj
        );
    },
    dataType: 'string',
  },
  {
    label: 'Cliente',
    getValue: (record) => record.name,
    dataType: 'string',
  },
  {
    label: 'Status',
    getValue: (record) =>
      record.brConsumerUnitStage?.name ||
      record.getBrConsumerUnitStage().then((res) => res?.name),
    dataType: 'string',
  },
  {
    label: 'Parceiro',
    getValue: (record) =>
      record?.brSalesPerson?.name ||
      record.getBrSalesPerson().then((res) => res?.name),
    dataType: 'string',
  },
  {
    label: 'Vendedor',
    getValue: (record) =>
      record?.alesPersonBrContact?.fullName ||
      record
        .getSalesPersonBrContact({ attributes: ['firstName', 'lastName'] })
        .then((res) => res?.fullName),
    dataType: 'string',
  },
  {
    label: 'Projeto',
    getValue: (record) =>
      record.salesforceProject?.name ||
      record
        .getSalesforceProject({ attributes: ['name'] })
        .then((res) => res?.name),
    dataType: 'string',
  },
  {
    label: 'Desconto',
    getValue: (record) => getCurrentDiscountRate(record.id),
    dataType: 'number',
  },
  {
    label: 'Conexão',
    getValue: (record) =>
      record.brVoltagePhase?.name ||
      record.getBrVoltagePhase().then((res) => res?.name),
    dataType: 'string',
  },
  {
    label: 'Average Consumption',
    getValue: (record) => record.avgMonthlyConsumption,
    dataType: 'number',
  },
  {
    label: 'Adjusted Average Consumption',
    getValue: (record) => record.adjustedAvgMonthlyConsumption,
    dataType: 'number',
  },
  {
    label: 'Start Date',
    getValue: (record) =>
      getConsumerUnitStartDt(record.id).then(
        (res) => res && moment(res).format('MMM D, YYYY')
      ),
    dataType: 'string',
  },
  {
    label: 'Address 1',
    getValue: (record) => record.address1,
    dataType: 'string',
  },
  {
    label: 'Address 2',
    getValue: (record) => record.address2,
    dataType: 'string',
  },
  {
    label: 'Bairro Unidade',
    getValue: (record) => record.district,
    dataType: 'string',
  },
  {
    label: 'Cidade Unidade',
    getValue: (record) => record.city,
    dataType: 'string',
  },
  {
    label: 'Estado Unidade',
    getValue: (record) => record.state,
    dataType: 'string',
  },
  {
    label: 'CEP Unidade',
    getValue: (record) => record.postalCode,
    dataType: 'string',
  },
  {
    label: 'Previous Consortium Contract Termination Dt',
    getValue: (record) =>
      record.previousConsortiumContractTerminationDt &&
      moment(record.previousConsortiumContractTerminationDt).format(
        'MMM D, YYYY'
      ),
    dataType: 'string',
  },
  {
    label: 'Created Date',
    getValue: (record) =>
      record.createdAt && moment(record.createdAt).format('MMM D, YYYY'),
    dataType: 'string',
  },
  {
    label: 'Terms of adhesion download link',
    getValue: (record) =>
      record
        .getBrTermsOfAdhesions({
          attributes: ['startDt', 'signatureDt', 'awsObjectKey'],
          where: {
            startDt: {
              [Op.not]: null,
            },
            signatureDt: {
              [Op.not]: null,
            },
            awsObjectKey: {
              [Op.not]: null,
            },
          },
          order: [['startDt', 'desc']],
          limit: 1,
        })
        .then((res) => {
          if (res?.length > 0) {
            return getSignedUrl(
              res[0].awsObjectKey,
              `TermoDeAdesao_${record.installationCode}`,
              process.env.S3_BUCKET_CREDIT_MGMT
            );
          }
          return '';
        }),
    dataType: 'string',
  },
];

const partnerBrConsumerUnitColumns = [
  {
    label: 'Código de instalação',
    getValue: (record) => record.installationCode,
    dataType: 'string',
  },
  {
    label: 'CNPJ/CPF',
    getValue: (record) => {
      if (record.brCustomer?.type) {
        return record.brCustomer?.type?.toLowerCase() === 'cpf'
          ? record.brCustomer.cpf
          : record.brCustomer.cnpj;
      }
      return record
        .getBrCustomer({
          attributes: ['type', 'cpf', 'cnpj'],
        })
        .then((res) =>
          res?.type?.toLowerCase() === 'cpf' ? res.cpf : res.cnpj
        );
    },
    dataType: 'string',
  },
  {
    label: 'Cliente',
    getValue: (record) => record.name,
    dataType: 'string',
  },
  {
    label: 'Status',
    getValue: (record) =>
      record.brConsumerUnitStage?.name ||
      record.getBrConsumerUnitStage().then((res) => res?.name),
    dataType: 'string',
  },
];

const investmentColumns = [
  // {
  //   label: 'id',
  //   getValue: (record) => record.id,
  //   dataType: 'number',
  // },
  {
    label: 'startDt',
    getValue: (record) => record.startDt,
    dataType: 'date',
  },
  {
    label: 'completedDt',
    getValue: (record) => record.completedDt,
    dataType: 'date',
  },
  {
    label: 'portfolioId',
    getValue: (record) => record.portfolioId,
    dataType: 'number',
  },
  {
    label: 'portfolio',
    getValue: (record) =>
      record.portfolio?.name ||
      record.getPortfolio({ attributes: ['name'] }).then((res) => res.name),
    dataType: 'string',
  },
  // {
  //   label: 'userId',
  //   getValue: (record) => record.userId,
  //   dataType: 'number',
  // },
  {
    label: 'user',
    getValue: (record) =>
      record.user
        ? `${record.user?.firstName} ${record.user?.lastName}`
        : record
          .getUser({ attributes: ['firstName', 'lastName'] })
          .then((res) => `${res?.firstName} ${res?.lastName}`),
    dataType: 'string',
  },
  {
    label: 'investorState',
    getValue: (record) =>
      record.user?.state ||
      record.getUser({ attributes: ['state'] }).then((res) => res?.state),
    dataType: 'string',
  },
  // {
  //   label: 'address',
  //   getValue: (record) => {
  //     if (
  //       record.user &&
  //       record.user.address1 &&
  //       record.user.city &&
  //       record.user.state &&
  //       record.user.postalCode
  //     ) {
  //       return `${record.user.address1 || ''} ${record.user.address2 || ''} ${
  //         record.user.city || ''
  //       }, ${record.user.state || ''} ${record.user.postalCode || ''}`;
  //     }
  //     return record
  //       .getUser({
  //         attributes: ['address1', 'address2', 'city', 'state', 'postalCode'],
  //       })
  //       .then((res) =>
  //         res
  //           ? `${record.user.address1 || ''} ${record.user.address2 || ''} ${
  //               record.user.city || ''
  //             }, ${record.user.state || ''} ${record.user.postalCode || ''}`
  //           : ''
  //       );
  //   },
  //   dataType: 'string',
  // },
  // {
  //   label: 'email',
  //   getValue: (record) =>
  //     record.user?.email ||
  //     record.getUser({ attributes: ['email'] }).then((res) => res?.email),
  //   dataType: 'string',
  // },
  {
    label: 'value',
    getValue: (record) => parseFloat(record.value),
    dataType: 'number',
  },
  {
    label: 'shares',
    getValue: (record) => parseFloat(record.shares),
    dataType: 'number',
  },
  // {
  //   label: 'sharePrice',
  //   getValue: (record) =>
  //     record.shares > 0 ? parseFloat(record.value / record.shares) : '',
  //   dataType: 'number',
  // },
  {
    label: 'natural shares',
    getValue: (record) => InvestmentService.naturalSharesPurchased(record),
    dataType: 'number',
  },
  {
    label: 'natural share value',
    getValue: (record) => InvestmentService.naturalSharesPurchasedValue(record),
    dataType: 'number',
  },
  // {
  //   label: 'cancelledDt',
  //   getValue: (record) => record.cancelledDt,
  //   dataType: 'date',
  // },
  {
    label: 'historicalFlg',
    getValue: (record) => !!record.historicalFlg,
    dataType: 'boolean',
  },
  {
    label: 'isReinvestment',
    getValue: (record) => !!record.dividendId,
    dataType: 'boolean',
  },
  {
    label: 'isReferral',
    getValue: (record) => !!record.referralId,
    dataType: 'boolean',
  },
  {
    label: 'isAccredited',
    getValue: (record) => !!record.user?.isAccredited,
    dataType: 'boolean',
  },
];

const dividendColumns = [
  // {
  //   label: 'id',
  //   getValue: (record) => record.id,
  //   dataType: 'number',
  // },
  {
    label: 'date',
    getValue: (record) => record.date,
    dataType: 'date',
  },
  {
    label: 'portfolio',
    getValue: (record) => {
      if (!record.monthlyPortfolioFinancialActual) {
        return record
          .getMonthlyPortfolioFinancialActual({
            attributes: ['portfolioId'],
            include: [
              {
                attributes: ['name'],
                model: Portfolio,
              },
            ],
          })
          .then((res) => res.monthlyPortfolioFinancialActual?.portfolio?.name);
      }
      if (
        !record.monthlyPortfolioFinancialActual.portfolio ||
        !record.monthlyPortfolioFinancialActual.portfolio?.name
      ) {
        return record.monthlyPortfolioFinancialActual
          .getPortfolio({ attributes: ['name'] })
          .then((res) => res.name);
      }
      return record.monthlyPortfolioFinancialActual?.portfolio?.name;
    },
    dataType: 'string',
  },
  {
    label: 'value',
    getValue: (record) => parseFloat(record.value),
    dataType: 'number',
  },
  // {
  //   label: 'userId',
  //   getValue: (record) => record.userId,
  //   dataType: 'number',
  // },
  {
    label: 'user',
    getValue: (record) =>
      record.user
        ? `${record.user?.firstName} ${record.user?.lastName}`
        : record
          .getUser({ attributes: ['firstName', 'lastName'] })
          .then((res) => `${res?.firstName} ${res?.lastName}`),
    dataType: 'string',
  },
  {
    label: 'investorState',
    getValue: (record) =>
      record.user?.state ||
      record.getUser({ attributes: ['state'] }).then((res) => res?.state),
    dataType: 'string',
  },
  // {
  //   label: 'address',
  //   getValue: (record) => {
  //     if (
  //       record.user &&
  //       record.user.address1 &&
  //       record.user.city &&
  //       record.user.state &&
  //       record.user.postalCode
  //     ) {
  //       return `${record.user.address1 || ''} ${record.user.address2 || ''} ${
  //         record.user.city || ''
  //       }, ${record.user.state || ''} ${record.user.postalCode || ''}`;
  //     }
  //     return record
  //       .getUser({
  //         attributes: ['address1', 'address2', 'city', 'state', 'postalCode'],
  //       })
  //       .then((res) =>
  //         res
  //           ? `${record.user.address1 || ''} ${record.user.address2 || ''} ${
  //               record.user.city || ''
  //             }, ${record.user.state || ''} ${record.user.postalCode || ''}`
  //           : ''
  //       );
  //   },
  //   dataType: 'string',
  // },
  // {
  //   label: 'email',
  //   getValue: (record) =>
  //     record.user?.email ||
  //     record.getUser({ attributes: ['email'] }).then((res) => res?.email),
  //   dataType: 'string',
  // },
  {
    label: 'isReinvested',
    getValue: (record) =>
      record.investment
        ? true
        : record
          .getInvestment({
            attributes: ['id'],
            where: { cancelledDt: null },
          })
          .then((res) => !!res),
    dataType: 'boolean',
  },
];

const arthurInvestmentColumns = [
  {
    label: 'startDt',
    getValue: (record) => record.startDt,
    dataType: 'date',
    style: {
      numberFormat: 'dd-mm-yyyy',
    },
  },
  {
    label: 'portfolioId',
    getValue: (record) => record.portfolioId,
    dataType: 'number',
  },
  {
    label: 'portfolio',
    getValue: (record) =>
      record.portfolio?.name ||
      record.getPortfolio({ attributes: ['name'] }).then((res) => res.name),
    dataType: 'string',
  },
  // {
  //   label: 'userId',
  //   getValue: (record) => record.userId,
  //   dataType: 'number',
  // },
  {
    label: 'user',
    getValue: (record) =>
      record.user
        ? `${record.user?.firstName} ${record.user?.lastName}`
        : record
          .getUser({ attributes: ['firstName', 'lastName'] })
          .then((res) => `${res?.firstName} ${res?.lastName}`),
    dataType: 'string',
  },
  {
    label: 'value',
    getValue: (record) => parseFloat(record.value),
    dataType: 'number',
  },
  {
    label: 'shares',
    getValue: (record) => parseFloat(record.shares),
    dataType: 'number',
  },
];

const transferColumns = [
  {
    label: 'id',
    getValue: (record) => record.id,
    dataType: 'number',
  },
  {
    label: 'type',
    getValue: (record) => record.type,
    dataType: 'string',
  },
  {
    label: 'userId',
    getValue: (record) => record.userId,
    dataType: 'number',
  },
  {
    label: 'user',
    getValue: (record) =>
      record.user
        ? `${record.user?.firstName} ${record.user?.lastName}`
        : record
          .getUser({ attributes: ['firstName', 'lastName'] })
          .then((res) => `${res?.firstName} ${res?.lastName}`),
    dataType: 'string',
  },
  {
    label: 'completedDt',
    getValue: (record) => record.completedDt,
    dataType: 'date',
  },
  {
    label: 'investment',
    getValue: (record) =>
      record.investmentId
        ? record.getInvestment().then((res) => res.label)
        : null,
    dataType: 'string',
  },
  {
    label: 'buy direction',
    getValue: (record) =>
      record.buyDirectionId
        ? record.getBuyDirection().then((res) => res.label)
        : null,
    dataType: 'string',
  },
  {
    label: 'dividend',
    getValue: (record) =>
      record.dividendId ? record.getDividend().then((res) => res.label) : null,
    dataType: 'string',
  },
  {
    label: 'share transfer',
    getValue: (record) =>
      record.shareTransferId
        ? record.getShareTransfer().then((res) => res.label)
        : null,
    dataType: 'string',
  },
  {
    label: 'share transfer value',
    getValue: (record) =>
      record.shareTransferId
        ? record.getShareTransfer().then((res) => res.value)
        : null,
    dataType: 'string',
  },
  {
    label: 'dwolla transfer ids',
    getValue: (record) =>
      record.dwollaTransferIds ? record.dwollaTransferIds.join(', ') : null,
    dataType: 'string',
  },
];

const shareTransferColumns = [
  {
    label: 'id',
    getValue: (record) => record.id,
    dataType: 'number',
  },
  {
    label: 'fromUserId',
    getValue: (record) =>
      record.sellOrder?.userId ||
      record
        .getSellOrder({
          attributes: ['userId'],
        })
        .then((res) => res.userId),
    dataType: 'number',
  },
  {
    label: 'fromUser',
    getValue: (record) =>
      record.sellOrder?.user
        ? `${record.sellOrder?.user?.firstName} ${record.sellOrder?.user?.lastName}`
        : record
          .getSellOrder({
            attributes: ['userId'],
            include: [
              {
                attributes: ['firstName', 'lastName'],
                model: User,
              },
            ],
          })
          .then((res) => `${res.user?.firstName} ${res.user?.lastName}`),
    dataType: 'string',
  },
  {
    label: 'toUserId',
    getValue: (record) =>
      record.investment?.userId ||
      record
        .getInvestment({
          attributes: ['userId'],
        })
        .then((res) => res.userId),
    dataType: 'number',
  },
  {
    label: 'toUser',
    getValue: (record) =>
      record.investment?.user
        ? `${record.investment.user?.firstName} ${record.investment.user?.lastName}`
        : record
          .getInvestment({
            attributes: ['userId'],
            include: [
              {
                attributes: ['firstName', 'lastName'],
                model: User,
              },
            ],
          })
          .then((res) => `${res.user?.firstName} ${res.user?.lastName}`),
    dataType: 'string',
  },
  {
    label: 'investmentId',
    getValue: (record) => record.investmentId,
    dataType: 'number',
  },
  // {
  //   label: 'investment',
  //   getValue: (record) =>
  //     record
  //       .getInvestment({
  //         attributes: ['label', 'startDt', 'value', 'cancelledDt'],
  //       })
  //       .then((res) => res.label),
  //   dataType: 'string',
  // },
  {
    label: 'portfolioId',
    getValue: (record) =>
      record.sellOrder?.portfolioId ||
      record
        .getSellOrder({
          attributes: ['portfolioId'],
        })
        .then((res) => res.portfolioId),
    dataType: 'number',
  },
  {
    label: 'portfolio',
    getValue: (record) =>
      record.sellOrder?.portfolio?.name ||
      record
        .getSellOrder({
          attributes: ['portfolioId'],
          include: [
            {
              attributes: ['name'],
              model: Portfolio,
            },
          ],
        })
        .then((res) => res.portfolio.name),
    dataType: 'string',
  },
  {
    label: 'sellDt',
    getValue: (record) => record.sellDt,
    dataType: 'date',
  },
  {
    label: 'soldShares',
    getValue: (record) => parseFloat(record.soldShares),
    dataType: 'number',
  },
  {
    label: 'value',
    getValue: (record) => parseFloat(record.value),
    dataType: 'number',
  },
  {
    label: 'historicalFlg',
    getValue: (record) => !!record.historicalFlg,
    dataType: 'boolean',
  },
  {
    label: 'createdAt',
    getValue: (record) => record.createdAt,
    dataType: 'date',
    style: {
      numberFormat: 'mm-dd-yyyy',
    },
  },
];

const arthurShareTransferColumns = [
  {
    label: 'id',
    getValue: (record) => record.id,
    dataType: 'number',
  },
  {
    label: 'investmentId',
    getValue: (record) => record.investmentId,
    dataType: 'number',
  },
  // {
  //   label: 'investment',
  //   getValue: (record) =>
  //     record
  //       .getInvestment({
  //         attributes: ['label', 'startDt', 'value', 'cancelledDt'],
  //       })
  //       .then((investment) => investment.label),
  //   dataType: 'string',
  // },
  {
    label: 'portfolioId',
    getValue: (record) =>
      record
        .getSellOrder({
          attributes: ['portfolioId'],
        })
        .then((res) => res.portfolioId),
    dataType: 'number',
  },
  {
    label: 'portfolio',
    getValue: (record) =>
      record.sellOrder?.portfolio?.name ||
      record
        .getSellOrder({
          attributes: ['portfolioId'],
          include: [
            {
              attributes: ['name'],
              model: Portfolio,
            },
          ],
        })
        .then((res) => res.portfolio.name),
    dataType: 'string',
  },
  {
    label: 'fromUserId',
    getValue: (record) =>
      record.sellOrder?.userId ||
      record
        .getSellOrder({
          attributes: ['userId'],
        })
        .then((res) => res.userId),
    dataType: 'number',
  },
  {
    label: 'fromUser',
    getValue: (record) =>
      record.sellOrder?.user
        ? `${record.sellOrder.user?.firstName} ${record.sellOrder.user?.lastName}`
        : record
          .getSellOrder({
            attributes: ['userId'],
            include: [
              {
                attributes: ['firstName', 'lastName'],
                model: User,
              },
            ],
          })
          .then((res) => `${res.user?.firstName} ${res.user?.lastName}`),
    dataType: 'string',
  },
  {
    label: 'toUserId',
    getValue: (record) =>
      record.investment?.userId ||
      record
        .getInvestment({
          attributes: ['userId'],
        })
        .then((res) => res.userId),
    dataType: 'number',
  },
  {
    label: 'toUser',
    getValue: (record) =>
      record.investment?.user
        ? `${record.investment.user?.firstName} ${record.investment.user?.lastName}`
        : record
          .getInvestment({
            attributes: ['userId'],
            include: [
              {
                attributes: ['firstName', 'lastName'],
                model: User,
              },
            ],
          })
          .then((res) => `${res.user?.firstName} ${res.user?.lastName}`),
    dataType: 'string',
  },
  {
    label: 'sellDt',
    getValue: (record) => record.sellDt,
    dataType: 'date',
    style: {
      numberFormat: 'dd-mm-yyyy',
    },
  },
  {
    label: 'soldShares',
    getValue: (record) => parseFloat(record.soldShares),
    dataType: 'number',
  },
  {
    label: 'value',
    getValue: (record) => parseFloat(record.value),
    dataType: 'number',
  },
  {
    label: 'historicalFlg',
    getValue: (record) => !!record.historicalFlg,
    dataType: 'boolean',
  },
  {
    label: 'createdAt',
    getValue: (record) => record.createdAt,
    dataType: 'date',
    style: {
      numberFormat: 'dd-mm-yyyy',
    },
  },
];

const userColumns = [
  {
    label: 'id',
    getValue: (record) => record.id,
    dataType: 'number',
  },
  {
    label: 'Name',
    getValue: (record) => `${record.firstName} ${record.lastName}`,
    dataType: 'string',
  },
  {
    label: 'Email',
    getValue: (record) => record.email,
    dataType: 'string',
  },
  {
    label: 'Current total invested',
    getValue: (record) =>
      UserService.currentTotalInvested({
        userId: record.id,
      }),
    dataType: 'number',
  },
  {
    label: 'Member since',
    getValue: (record) => record.createdAt,
    dataType: 'date',
    style: {
      numberFormat: 'mm-dd-yyyy',
    },
  },
  {
    label: 'Address 1',
    getValue: (record) => record.address1,
    dataType: 'string',
  },
  {
    label: 'Address 2',
    getValue: (record) => record.address2,
    dataType: 'string',
  },
  {
    label: 'City',
    getValue: (record) => record.city,
    dataType: 'string',
  },
  {
    label: 'State',
    getValue: (record) => record.state,
    dataType: 'string',
  },
  {
    label: 'Postal code',
    getValue: (record) => String(record.postalCode),
    dataType: 'string',
  },
  {
    label: 'Country',
    getValue: (record) => record.countryCode,
    dataType: 'string',
  },
];

const getColumnConfig = (type) => {
  switch (type) {
    case 'user':
      return userColumns;
    case 'dividend':
      return dividendColumns;
    case 'investment':
      return investmentColumns;
    case 'investment-arthur':
      return arthurInvestmentColumns;
    case 'transfer':
      return transferColumns;
    case 'shareTransfer':
      return shareTransferColumns;
    case 'shareTransfer-arthur':
      return arthurShareTransferColumns;
    case 'brInvoice':
      return brInvoiceColumns;
    case 'brConsumerUnit':
      return brConsumerUnitColumns;
    case 'partnerBrConsumerUnits':
      return partnerBrConsumerUnitColumns;
    default:
      console.error(`Unknown record type: ${type}`);
      return null;
  }
};

const getFileName = (type) => {
  switch (type) {
    case 'shareTransfer-arthur':
      return 'shareTransfer';
    case 'investment-arthur':
      return 'investment';
    default:
      return type;
  }
};

const getWorkbook = async ({ type, records }) => {
  const columns = getColumnConfig(type);

  const wb = new xl.Workbook();
  const ws = wb.addWorksheet();
  columns.forEach((column, index) => {
    ws.cell(1, index + 1).string(column.label);
  });

  for (let rowIndex = 0; rowIndex < records.length; rowIndex += 1) {
    const record = records[parseInt(rowIndex, 10)];
    for (let columnIndex = 0; columnIndex < columns.length; columnIndex += 1) {
      const column = columns[parseInt(columnIndex, 10)];
      const value = column.getValue(record);
      if (isPromise(value)) {
        // eslint-disable-next-line no-await-in-loop
        await value.then((val) => {
          if (val !== null && val !== undefined) {
            writeToCell({
              worksheet: ws,
              row: rowIndex + 2,
              col: columnIndex + 1,
              val,
              dataType: column.dataType,
              style: column.style,
            });
          }
        });
      } else {
        if (value !== null && value !== undefined) {
          writeToCell({
            worksheet: ws,
            row: rowIndex + 2,
            col: columnIndex + 1,
            val: value,
            dataType: column.dataType,
            style: column.style,
          });
        }
      }
    }
  }

  return wb;
}

const emailExport = async ({ email, type, records }) => {
  const wb = await getWorkbook({ type, records })

  const fileName = `${getFileName(type)}.xlsx`;
  const filePath = `${__dirname}/${fileName}`;

  const buffer = await wb.writeToBuffer();
  fs.writeFile(filePath, buffer, function (err) {
    if (err) {
      SlackService.logToSlack({
        title: 'Error writing file to disk',
        type: 'platform-error',
        data: [
          {
            label: 'File',
            value: filePath,
          },
          {
            label: 'Error',
            value: stringifyObject(err),
          },
        ],
      });
      return;
    }
    sendExportedCMSList({
      email,
      fileName,
      filePath,
    }).then(() => {
      fs.unlink(filePath, (err) => {
        if (err) {
          console.error('Error deleting list export after email sent', err);
        }
      });
    });
  });

  return true;
};

export default {
  emailExport,
  getWorkbook
};
