import crypto from 'crypto';

module.exports = (sequelize, DataTypes) => {
  const BrSalesPerson = sequelize.define(
    'brSalesPerson',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        unique: true,
      },
      cnpj: {
        type: DataTypes.STRING,
        set(value) {
          if (!value) {
            this.setDataValue('cnpj', null);
          } else {
            const lintedValue = value.replace(/\D/g, '');
            if (lintedValue.length !== 14) {
              console.error('Invalid cnpj', value);
              // throw new Error('Invalid cnpj');
            }
            this.setDataValue('cnpj', lintedValue);
          }
        },
      },
      name: DataTypes.STRING,
      legalName: DataTypes.STRING,
      salesExperience: DataTypes.STRING,
      hideCommissionSimulatorFlg: DataTypes.BOOLEAN,
      referral: DataTypes.STRING,
      thirdPartyFlg: DataTypes.BOOLEAN,
      agreementAwsObjectKey: DataTypes.STRING,
      pendingAgreementAwsObjectKey: DataTypes.STRING,
      signatureRequestedDt: DataTypes.DATE,
      signatureDt: DataTypes.DATE,
      createdAt: DataTypes.DATE,
      updatedAt: DataTypes.DATE,

      // VIRTUAL Fields
      hash: {
        type: DataTypes.VIRTUAL,
        comment:
          'Hash of sales partner row used as key for public access of document',
        get() {
          const data = `${this.id}-${this.adminBrContactId}-${this.createdAt}`;
          const hash = crypto
            .createHash('sha256') // Use the desired algorithm, like 'sha256'
            .update(data)
            .digest('hex');
          return hash;
        },
      },
      formattedCnpj: {
        type: DataTypes.VIRTUAL,
        get() {
          if (!this.cnpj) return null;
          if (this.cnpj.length !== 14) return this.cnpj;
          return this.cnpj.replace(
            /(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/,
            '$1.$2.$3/$4-$5'
          );
        },
      },
    },
    {
      comment: 'Brazil Sales Partner',
      tableName: 'brSalesPersons', // without this, sequelize thinks the table should be called brSalesPeople and queries fail
    }
  );
  BrSalesPerson.associate = (models) => {
    BrSalesPerson.hasMany(models.BrContact);
    BrSalesPerson.hasMany(models.BrConsumerUnit);
    BrSalesPerson.hasMany(models.BrCommissionPayment);
    BrSalesPerson.belongsTo(models.BrSalesPartnerStatus);
    BrSalesPerson.belongsTo(models.BrContact, {
      as: 'adminBrContact',
    });
  };
  return BrSalesPerson;
};
