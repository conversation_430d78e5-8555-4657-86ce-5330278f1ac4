module.exports = {
  up: (queryInterface, Sequelize) =>
    queryInterface.sequelize.transaction((t) =>
      Promise.all([
        queryInterface.addColumn(
          'brSalesPersons',
          'hideCommissionSimulatorFlg',
          {
            type: Sequelize.BOOLEAN,
            allowNull: false,
            defaultValue: false,
            comment:
              'Flag used to hide the commission simulator for a salesperson',
          },
          {
            transaction: t,
          }
        ),
      ])
    ),

  down: (queryInterface) =>
    queryInterface.sequelize.transaction((t) =>
      Promise.all([
        queryInterface.removeColumn(
          'brSalesPersons',
          'hideCommissionSimulatorFlg',
          {
            transaction: t,
          }
        ),
      ])
    ),
};
