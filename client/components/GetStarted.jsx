/* eslint react/forbid-prop-types: 0 */

import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { graphql } from '@apollo/client/react/hoc';

import { Link, Redirect, withRouter } from 'react-router-dom';
import { Image, Video } from 'cloudinary-react';
import classNames from 'classnames';
import queryString from 'query-string';
import SwipeableViews from 'react-swipeable-views';
import validator from 'validator';
import numeral from 'numeral';
import TagManager from 'react-gtm-module';

// @material-ui
import {
  Alert,
  Button,
  Chip,
  Collapse,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControl,
  FormHelperText,
  Grid,
  Hidden,
  Icon,
  IconButton,
  InputAdornment,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  TextField,
  Typography,
} from '@mui/material';

import { ArrowForward, Close } from '@mui/icons-material';

import { withStyles } from '@mui/styles';
import owasp from 'owasp-password-strength-test';

import FaviconLogo from './FaviconLogo';
// graphql
import signupMutation from '../mutations/Signup.graphql';
import createInternationalHubSpotContactMutation from '../mutations/CreateInternationalHubSpotContact.graphql';
import logToSlackMutation from '../mutations/LogToSlack.graphql';
import claimPromoCodeMutation from '../mutations/ClaimPromoCode.graphql';
import getAuth0TokensMutation from '../mutations/GetAuth0Tokens.graphql';

import { constants, stringifyObject } from '../../utils/global';
import withSnackbar from './hocs/withSnackbar';
import withIpQualityScript from './hocs/withIpQualityScript';
import withAuth from './hocs/withAuth';
import ReferralProgramAlert from './ReferralProgramAlert';
import PhoneFormat from './PhoneFormat';
import countries from '../../utils/countries';
// import component-specific styling
import styles from './login.css.js';
import { safelySetSessionStorageItem } from '../lib/browser';

import { trackBehavioralEvent } from '../lib/analytics';
import { setToken } from '../lib/apollo';
import withFullScreen from './hocs/withFullScreen';

import FadeInWrapper from './FadeInWrapper';
import Auth0LoginButton from './Auth0LoginButton';

import CardSelector from './CardSelector';
import CustomButton from './CustomButton';
import CustomCheckbox from './CustomCheckbox';
import {
  BusinessIcon,
  EmailIcon,
  EyeIcon,
  EyeSlashIcon,
  UserIcon,
  PhoneIcon,
  BankIcon,
  PlayVideoIcon,
} from './icons';

owasp.config({
  allowPassphrases: false, // When true, passwords > minPhraseLength get to bypass special character requirements in the owasp check. This is not inline with how auth0 works
  maxLength: 128,
  minLength: 8,
  minPhraseLength: 20, // Not applicable when allowPassphrases: false
  minOptionalTestsToPass: 4,
});

const maxFormWidth = '600px';

const queryStringBiPass = () => {
  const code = `?${process.env.BETA_SIGNUP_CODE}`;
  return (
    process.env.PREVENT_NEW_USERS !== 'true' || code === window.location.search
  );
};

class GetStarted extends Component {
  constructor(props) {
    super(props);
    const {
      autoPlayVideo,
      r,
      promo,
      fcpc: foundersCardPromoCodeId,
    } = queryString.parse(window.location.search);

    this.state = {
      loading: false,
      errors: [],
      firstName: '',
      lastName: '',
      primaryPhone: '',
      email: '',
      password: '',
      isUSCitizen: true,
      isUSResident: true,
      is18Yrs: true,
      type: null,
      videoDialogOpen: !!autoPlayVideo,
      hidePassword: true,
      readTerms: false,
      referrerId: r,
      isReferralCodeActive: true, // Set from child <ReferralProgramAlert />. Assumed true until query finishes. When false, don't pass referrerId to signup resolver
      step: 0,
    };
    this.foundersCardPromoCodeId = foundersCardPromoCodeId;
    this.showMoneyShow = promo === 'moneyshow';
    this.showGenericPromo = promo === 't';
    this.showACEPromo = promo === 'ace';
    this.showWealthFactoryPromo = promo === 'wealth-factory';
    this.showWelcomePromo = promo === 'welcome';
    this.onSubmit = this.onSubmit.bind(this);
  }

  async handleClaimPromoCode(promoCodeId, email) {
    const { logToSlack, claimPromoCode } = this.props;
    try {
      await claimPromoCode({
        variables: {
          input: {
            promoCodeId: parseInt(promoCodeId, 10),
            email,
          },
        },
      }).catch((err) => {
        logToSlack({
          variables: {
            input: {
              title: `Error claiming promo code`,
              type: 'platform-error',
              data: [
                {
                  label: 'Email',
                  value: email,
                },
                {
                  label: 'Promo Code ID',
                  value: promoCodeId,
                },
                {
                  label: 'Error',
                  value: stringifyObject(err),
                },
              ],
            },
          },
        });
      });
    } catch (err) {
      logToSlack({
        variables: {
          input: {
            title: `Error claiming promo code`,
            type: 'platform-error',
            data: [
              {
                label: 'Email',
                value: email,
              },
              {
                label: 'Promo Code ID',
                value: promoCodeId,
              },
              {
                label: 'Error',
                value: stringifyObject(err),
              },
            ],
          },
        },
      });
    }
  }

  onSubmit() {
    this.setState({ loading: true });
    const {
      mutate,
      history,
      logToSlack,
      // storeDevice,
      getAuth0Tokens,
      isAuthenticatedRefetch,
      authState,
    } = this.props;
    const {
      email,
      firstName,
      lastName,
      password,
      primaryPhone,
      referrerId,
      isReferralCodeActive,
      type,
      intendedFundingSrcType,
    } = this.state;
    let promoCode;
    if (!referrerId && this.showMoneyShow) {
      promoCode = 'moneyshow';
    } else if (!referrerId && this.showGenericPromo) {
      promoCode = 'generic';
    } else if (this.showACEPromo) {
      promoCode = 'ace';
    } else if (this.showWealthFactoryPromo) {
      promoCode = 'wealth-factory';
    } else if (this.showWelcomePromo) {
      promoCode = 'welcome';
    }
    // eslint-disable-next-line no-undef
    fetch('https://api.ipify.org/?format=json')
      .then(
        (results) => results.json(),
        (res) => {
          const eMsg = `Error retrieving ip address from https://api.ipify.org. Allowing user creation without ip check for : ${email}.`;
          logToSlack({
            variables: {
              input: {
                title: eMsg,
                type: 'platform-error',
                data: [
                  {
                    label: 'Error Response',
                    value: String(res),
                  },
                ],
              },
            },
          });
          return null;
        }
      )
      .then((ipAddress) => {
        mutate({
          variables: {
            input: {
              email,
              firstName,
              lastName,
              password,
              primaryPhone,
              promoCode,
              intendedFundingSrcType,
              type,
              referral: {
                referrerId,
                deactivated: !isReferralCodeActive,
              },
              identity: {
                ipAddress: ipAddress && ipAddress.ip,
                userAgent: navigator?.userAgent,
              },
            },
          },
        }).then(
          async (res1) => {
            const userId = res1?.data?.signup?.id;
            // const { authId } = res1?.data?.signup;
            if (!userId) {
              const errors = res1.graphQLErrors.map((error) => error.message); // array of all errors in response object
              this.setState({ errors, loading: false });
            }

            TagManager.dataLayer({
              dataLayer: {
                event: 'signup',
                userId,
                // The below fields are used for "Enhanced Conversions" for GoogleAds
                email,
                firstName,
                lastName,
                primaryPhone,
              },
            });
            // TODO: handle claim promo first
            if (this.foundersCardPromoCodeId) {
              await this.handleClaimPromoCode(
                this.foundersCardPromoCodeId,
                email
              );
            }

            // Store device with ipquality
            // await storeDevice({
            //   // oktaId: oktaUser?.id,
            //   authId,
            //   email,
            // }).catch((err) => {
            //   logToSlack({
            //     variables: {
            //       input: {
            //         title: `Error storing device info`,
            //         type: 'platform-error',
            //         data: [
            //           {
            //             label: 'Error',
            //             value: stringifyObject(err),
            //           },
            //           {
            //             label: 'Email',
            //             value: email,
            //           },
            //           {
            //             label: 'User Agent',
            //             value: navigator?.userAgent,
            //           },
            //         ],
            //       },
            //     },
            //   });
            // });
            // Step 1 retrieve token from backend
            const tokenData = await getAuth0Tokens({
              variables: {
                input: {
                  email,
                  password,
                },
              },
            }).then((res) => res.data.getAuth0Tokens);
            // Step 2 set the token in local storage so that the backend can use it
            if (tokenData?.access_token && tokenData.expires_in) {
              // eslint-disable-next-line camelcase
              const { access_token, expires_in } = tokenData;
              localStorage.setItem('id_token', access_token);
              localStorage.setItem(
                'id_token_expires_at',
                // eslint-disable-next-line camelcase
                Date.now() + expires_in * 1000
              );
              setToken(access_token);
              await isAuthenticatedRefetch();
              history.push('/users/dashboard');
            } else {
              // Step 4 if any errors occur in trying to set the token, loginWithRedirect
              authState.loginWithRedirect({
                authorizationParams: {
                  // eslint-disable-next-line camelcase
                  login_hint: email,
                  // eslint-disable-next-line camelcase
                  screen_hint: 'login',
                },
                appState: { returnTo: '/users/dashboard' }, // Specify where to redirect after login
              });
            }
          },
          (res) => {
            const errors = res.graphQLErrors.map((error) => error.message); // array of all errors in response object
            this.setState({ errors, loading: false });
          }
        );
      });
  }

  createInternationalHubSpotContact() {
    const { createInternationalHubSpotContact, snackbar } = this.props;
    const { contactFirstName, contactLastName, contactCountry, contactEmail } =
      this.state;

    createInternationalHubSpotContact({
      variables: {
        input: {
          email: contactEmail,
          firstName: contactFirstName,
          lastName: contactLastName,
          country: contactCountry,
        },
      },
    })
      .catch((res) => {
        const errors = res.graphQLErrors.map((error) => error.message); // array of all errors in response object
        this.setState({ loading: false });
        snackbar.setState({
          snackbarMessage: `Error subscribing '${contactEmail}' due to invalid email address.`,
          snackbarOpen: true,
          snackbarVariant: 'error',
        });
        throw new Error('Error creating international contact.', errors);
      })
      .then(() => {
        this.setState({
          contactFormOpen: false,
          internationalContactCreatedDialogOpen: true,
          contactFormSubmitted: true,
        });
      });
  }

  render() {
    const { classes, theme, fullScreen, isAuthenticated } = this.props;
    const {
      email,
      errors,
      firstName,
      hidePassword,
      intendedFundingSrcType,
      is18Yrs,
      isUSCitizen,
      isUSResident,
      lastName,
      loading,
      password,
      primaryPhone,
      readTerms,
      referrerId,
      step,
      type,
      videoDialogOpen,
    } = this.state;
    if (isAuthenticated) {
      safelySetSessionStorageItem('newUser', true);
      return <Redirect to={{ pathname: '/users/dashboard' }} />;
    }

    const showMoneyShow = step === 0 && this.showMoneyShow;
    const showGenericPromo = step === 0 && this.showGenericPromo;
    const showACEPromo = step === 0 && this.showACEPromo;
    const showWealthFactoryPromo = step === 0 && this.showWealthFactoryPromo;
    const showWelcomePromo = step === 0 && this.showWelcomePromo;
    const showFoundersCardPromo = step === 0 && this.foundersCardPromoCodeId;

    const namePageIncomplete =
      !firstName ||
      firstName === '' ||
      !lastName ||
      lastName === '' ||
      !validator.isMobilePhone(primaryPhone, 'en-US');
    const reviewPageIncomplete = !validator.isEmail(email);
    const isContinueDisabled = () => {
      if (loading) {
        return true;
      }
      if (step === 0) {
        return !type;
      }
      if (step === 1) {
        return !isUSCitizen || !isUSResident || !is18Yrs;
      }
      if (process.env.MILLENNIUM_FEATURE_ON === 'true' && type === 'personal') {
        if (step === 2) {
          return !intendedFundingSrcType;
        }
        if (step === 3) {
          return namePageIncomplete;
        }
        if (step === 4) {
          return reviewPageIncomplete;
        }
      }
      if (step === 2) {
        return namePageIncomplete;
      }
      if (step === 3) {
        return reviewPageIncomplete;
      }

      return true;
    };
    const renderMoneyShow = () => (
      <Grid
        container
        direction="column"
        style={{
          marginTop: fullScreen ? 0 : '1rem',
          marginBottom: fullScreen ? 0 : '1rem',
        }}
      >
        <Grid item>
          <Icon
            style={{
              fontSize: '2.5rem',
              lineHeight: '2.5rem',
              marginBottom: '.25rem',
              color: theme.palette.secondary.main,
            }}
            className="fas fa-award"
          />
        </Grid>
        <Grid item>
          <Typography style={{ marginBottom: '.25rem' }} variant="h5">
            MoneyShow Promotion
          </Typography>
        </Grid>
        <Grid item>
          <Typography
            variant="h3"
            style={{ marginBottom: '.25rem' }}
            color="secondary"
          >
            {numeral(constants.referralProgramReward.investmentAmount).format(
              '$0,0.00'
            )}
          </Typography>
        </Grid>
      </Grid>
    );

    const renderGenericPromo = () => (
      <Grid
        container
        direction="column"
        style={{
          marginTop: fullScreen ? 0 : '1rem',
          marginBottom: fullScreen ? 0 : '1rem',
        }}
      >
        <Grid item>
          <Icon
            style={{
              fontSize: '2.5rem',
              lineHeight: '2.5rem',
              marginBottom: '.25rem',
              color: theme.palette.secondary.main,
            }}
            className="fas fa-award"
          />
        </Grid>
        <Grid item>
          <Typography style={{ marginBottom: '.25rem' }} variant="h5">
            Promotion
          </Typography>
        </Grid>
        <Grid item>
          <Typography
            variant="h3"
            style={{ marginBottom: '.25rem' }}
            color="secondary"
          >
            {numeral(constants.referralProgramReward.investmentAmount).format(
              '$0,0.00'
            )}
          </Typography>
        </Grid>
      </Grid>
    );

    const renderWelcomePromo = () => (
      <Grid
        container
        direction="column"
        style={{
          marginTop: fullScreen ? '1rem' : '2rem',
          marginBottom: fullScreen ? '1rem' : '2rem',
        }}
      >
        <Grid item>
          <Chip
            color="secondary"
            style={{
              // zIndex: 10000,
              backgroundColor: theme.palette.green.main,
            }}
            icon={
              <Icon
                style={{
                  padding: '2px',
                }}
                className="fas fa-award"
              />
            }
            label={
              <b>
                {numeral(constants.welcomePromo.value).format('$0,0.[00]')}{' '}
                welcome promo activated!
              </b>
            }
          />
        </Grid>
      </Grid>
    );

    const renderWealthFactoryPromo = () => (
      <Grid
        container
        direction="column"
        style={{
          marginTop: fullScreen ? 0 : '1rem',
          marginBottom: fullScreen ? 0 : '1rem',
        }}
      >
        <Grid item>
          <Icon
            style={{
              fontSize: '2rem',
              lineHeight: '2rem',
              marginBottom: '.25rem',
              color: theme.palette.secondary.main,
            }}
            className="fas fa-award"
          />
        </Grid>
        <Grid item>
          <Typography style={{ marginBottom: '.25rem' }} variant="h6">
            Wealth Factory Promotion
          </Typography>
        </Grid>
        <Grid item>
          <Typography
            variant="h3"
            style={{ marginBottom: '.25rem' }}
            color="secondary"
          >
            {numeral(constants.wealthFactorySignupPromo.value).format(
              '$0,0.00'
            )}
          </Typography>
        </Grid>
      </Grid>
    );

    const renderACEPromo = () => (
      <Grid
        container
        direction="column"
        style={{
          marginTop: fullScreen ? 0 : '1rem',
          marginBottom: fullScreen ? 0 : '1rem',
        }}
      >
        <Grid item>
          <Icon
            style={{
              fontSize: '2rem',
              lineHeight: '2rem',
              marginBottom: '.25rem',
              color: theme.palette.secondary.main,
            }}
            className="fas fa-award"
          />
        </Grid>
        <Grid item>
          <Typography style={{ marginBottom: '.25rem' }} variant="h6">
            ACE Promotion
          </Typography>
        </Grid>
        <Grid item>
          <Typography
            variant="h3"
            style={{ marginBottom: '.25rem' }}
            color="secondary"
          >
            {numeral(constants.aceSignupPromo.value).format('$0,0.00')}
          </Typography>
        </Grid>
      </Grid>
    );

    const renderFoundersCardPromo = () => (
      <Grid
        container
        justifyContent="center"
        style={{
          marginTop: fullScreen ? 0 : '2rem',
          marginBottom: fullScreen ? 0 : '1rem',
        }}
      >
        {this.foundersCardPromoCodeId === '0' ? (
          <>
            <Grid
              container
              alignItems="center"
              justifyContent="center"
              direction="row"
              spacing={2}
            >
              <Grid item>
                <Image
                  alt="Stairway Investors Logo"
                  style={{ width: '40px' }}
                  width={80}
                  crop="scale"
                  quality="auto"
                  cloud_name={constants.cloud_name}
                  publicId="energea/partner-logos/Screen_Shot_2023-11-02_at_3.37.54_PM" // eslint-disable-line camelcase
                  format="WebP"
                />
              </Grid>
              <Grid item>
                <Typography variant="h5" style={{ color: 'rgb(36,87,61)' }}>
                  <b>Welcome Stairway Investor!</b>
                </Typography>
              </Grid>
            </Grid>
            <Grid item xs={12} style={{ width: '100%' }}>
              <Divider
                style={{
                  marginTop: '2rem',
                  marginBottom: '1rem',
                  height: 0,
                  width: '100%',
                  borderTop: `thin solid ${theme.palette.divider}`,
                }}
              />
            </Grid>
          </>
        ) : (
          <Grid item style={{ marginBottom: '1rem' }}>
            <Alert
              style={{
                backgroundColor: '#2a2a2a',
                boxShadow: 'inset 0 0 20px #000',
                borderRadius: '50px',
                // backgroundImage:
                //   'radial-gradient(ellipse at center, #2a2a2a, #0d0d0d)',
                color: theme.palette.text.white,
              }}
              icon={false}
            >
              {/* https://res.cloudinary.com/energea/image/upload/v1698953930/energea/partner-logos/Screen_Shot_2023-11-02_at_3.37.54_PM.png */}
              <Grid container alignItems="center" spacing={1}>
                <Grid item>
                  <Icon
                    style={{
                      color: theme.palette.text.white,
                      fontSize: '1.2rem',
                    }}
                    className="fas fa-anchor"
                  />
                </Grid>
                <Grid item>
                  <Typography style={{ color: theme.palette.text.white }}>
                    <b>Welcome Founders Card Member!</b>
                  </Typography>
                </Grid>
              </Grid>
              {/* </AlertTitle> */}
            </Alert>
          </Grid>
        )}
      </Grid>
    );

    const renderDialog = () => {
      const {
        contactFormOpen,
        contactFormSubmitted,
        contactFirstName,
        contactLastName,
        contactCountry,
        contactEmail,
      } = this.state;
      const formComplete =
        contactFormOpen &&
        contactFirstName &&
        contactLastName &&
        contactCountry &&
        contactEmail;
      return (
        <Dialog
          open={!!contactFormOpen && !contactFormSubmitted}
          fullScreen={fullScreen}
          onClose={() => this.setState({ contactFormOpen: false })}
        >
          <DialogTitle id="form-dialog-title" style={{ textAlign: 'right' }}>
            <Grid container alignItems="center" justifyContent="space-between">
              <Grid item>Contact Form</Grid>
              <Grid item>
                <IconButton
                  onClick={() => this.setState({ contactFormOpen: false })}
                  style={{ marginRight: '-.5rem' }}
                  size="large"
                >
                  <Close />
                </IconButton>
              </Grid>
            </Grid>
          </DialogTitle>
          <DialogContent style={{ marginTop: '.5rem' }}>
            <Grid container justifyContent="center">
              <Grid item xs={12}>
                <Typography
                  style={{ marginBottom: '1.5rem' }}
                  variant="body1"
                  gutterBottom
                  paragraph
                >
                  Unfortunately, at this point in time, we cannot accept
                  investments from people who are not US residents or don&apos;t
                  have a valid US Social Security Number.
                  <br />
                  <br />
                  <b>
                    If you would like to be notified when we are able to accept
                    investments from your country, complete the form below.
                  </b>
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <form className={classes.container} autoComplete="on">
                  <FormControl margin="normal" required fullWidth>
                    <TextField
                      variant="outlined"
                      required
                      label="First Name"
                      autoComplete="given-name"
                      fullWidth
                      className={classes.textField}
                      value={contactFirstName}
                      onChange={(event) =>
                        this.setState({ contactFirstName: event.target.value })
                      }
                    />
                  </FormControl>
                  <FormControl margin="normal" required fullWidth>
                    <TextField
                      variant="outlined"
                      required
                      autoComplete="family-name"
                      label="Last Name"
                      fullWidth
                      className={classes.textField}
                      value={contactLastName}
                      onChange={(event) =>
                        this.setState({ contactLastName: event.target.value })
                      }
                    />
                  </FormControl>
                  <FormControl margin="normal" required fullWidth>
                    <InputLabel id="country-select-label">Country</InputLabel>
                    <Select
                      fullWidth
                      variant="outlined"
                      id="country"
                      name="country"
                      required
                      label="Country"
                      // autoComplete="address-level1"
                      value={contactCountry || ''}
                      onChange={(event) =>
                        this.setState({ contactCountry: event.target.value })
                      }
                      labelId="country-label"
                    >
                      {countries.map((countryOption) => (
                        <MenuItem
                          key={`get-started-country-selector-${countryOption.code}`}
                          name="country"
                          value={countryOption.code}
                        >
                          {countryOption.name} ({countryOption.code})
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                  <FormControl margin="normal" required fullWidth>
                    <TextField
                      variant="outlined"
                      required
                      autoComplete="email"
                      type="email"
                      label="Email"
                      fullWidth
                      className={classes.textField}
                      value={contactEmail}
                      onChange={(event) =>
                        this.setState({ contactEmail: event.target.value })
                      }
                    />
                  </FormControl>
                </form>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Grid
              container
              style={{ width: '100%', padding: '0 1rem 1rem' }}
              justifyContent="center"
            >
              <Grid item>
                <CustomButton
                  onClick={() => this.createInternationalHubSpotContact()}
                  disabled={!formComplete || !validator.isEmail(contactEmail)}
                  loading={loading}
                  color="green"
                  size="large"
                  pill
                  variant="contained"
                  style={{
                    borderRadius: '50px',
                  }}
                  endIcon={<ArrowForward />}
                >
                  Submit
                </CustomButton>
              </Grid>
            </Grid>
          </DialogActions>
        </Dialog>
      );
    };

    const renderInternationalContactCreatedDialog = () => {
      const { internationalContactCreatedDialogOpen } = this.state;
      return (
        <Dialog
          open={!!internationalContactCreatedDialogOpen}
          fullScreen={fullScreen}
          onClose={() =>
            this.setState({ internationalContactCreatedDialogOpen: false })
          }
        >
          <DialogTitle id="form-dialog-title" style={{ textAlign: 'right' }}>
            <IconButton
              onClick={() =>
                this.setState({
                  internationalContactCreatedDialogOpen: false,
                })
              }
              style={{ marginRight: '-.5rem' }}
              size="large"
            >
              <Close />
            </IconButton>
          </DialogTitle>
          <DialogContent>
            <Grid container justifyContent="center">
              <Grid item>
                <Typography
                  style={{ fontWeight: 'bold' }}
                  variant="h6"
                  paragraph
                >
                  Success!
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography
                  style={{ marginBottom: '1.5rem' }}
                  variant="body1"
                  gutterBottom
                  paragraph
                >
                  You&apos;re contact information has been saved and we will
                  notify you when we are able to accept investments from your
                  country.
                </Typography>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Grid
              container
              style={{ width: '100%', padding: '0 1rem 1rem' }}
              justifyContent="center"
            >
              <Grid item>
                <CustomButton
                  component={Link}
                  to="/investments"
                  color="secondary"
                  size="large"
                  variant="contained"
                  pill
                  endIcon={<ArrowForward />}
                >
                  Go To Marketplace
                </CustomButton>
              </Grid>
            </Grid>
          </DialogActions>
        </Dialog>
      );
    };

    const getCustomerType = () => (
      <Grid
        container
        style={{
          paddingTop:
            fullScreen &&
            (showMoneyShow || showGenericPromo || showFoundersCardPromo)
              ? '2rem'
              : '0',
          height:
            fullScreen &&
            (showMoneyShow || showGenericPromo || showFoundersCardPromo)
              ? '8rem'
              : '80%',
        }}
        justifyContent="center"
      >
        <Grid item style={{ maxWidth: maxFormWidth }}>
          <Hidden smDown>
            <Typography
              style={{ textAlign: 'left' }}
              component="h1"
              variant="h6"
            >
              Let&apos;s Get Started
            </Typography>
          </Hidden>
          <Typography
            component="h2"
            style={{
              textAlign: fullScreen ? 'center' : 'left',
              width: '100%',
              minHeight: fullScreen ? '1.5em' : '2.5em',
            }}
            color="primary"
            variant={fullScreen ? 'h5' : 'h4'}
          >
            <b>Are you an individual or a business?</b>
          </Typography>
        </Grid>
        <Grid item className={classes.form}>
          <Grid container justifyContent="center">
            <Grid item>
              <CardSelector
                primaryText="Individual"
                onClick={() => this.setState({ type: 'personal' })}
                selected={type === 'personal'}
                icon={<UserIcon />}
              />
            </Grid>
            <Grid item>
              <CardSelector
                primaryText="Business"
                primaryTextLine2="or Trust"
                onClick={() => this.setState({ type: 'business' })}
                selected={type === 'business'}
                icon={<BusinessIcon />}
              />
            </Grid>
          </Grid>
          {referrerId ? (
            <Grid container justifyContent="center">
              <Grid item style={{ paddingTop: '.5rem' }}>
                <ReferralProgramAlert
                  referrerId={referrerId}
                  fullScreen={fullScreen}
                  parent={this}
                />
              </Grid>
            </Grid>
          ) : null}
        </Grid>
      </Grid>
    );
    const getCitizenStatus = () => {
      const { contactFormSubmitted } = this.state;
      return (
        <>
          <Grid container justifyContent="center">
            <Grid item style={{ maxWidth: maxFormWidth }}>
              <Typography
                style={{
                  visibility: 'hidden',
                  textAlign: fullScreen ? 'center' : 'left',
                }}
                // color="primary"
                variant="h6"
              >
                {type && `${type[0].toUpperCase()}${type.substring(1)}`}
              </Typography>
              <Typography
                style={{
                  textAlign: fullScreen ? 'center' : 'left',
                  width: '100%',
                  minHeight: '2.5em',
                }}
                color="primary"
                variant="h4"
              >
                <b>First, please confirm the following.</b>
              </Typography>
              <Grid className={classes.form}>
                <div style={{ marginLeft: fullScreen ? 0 : '.6rem' }}>
                  <CustomCheckbox
                    required
                    fullWidth
                    checked={isUSResident}
                    name="isUSResident"
                    onChange={(e) => {
                      this.setState({
                        isUSResident: e.target.checked,
                        contactFormOpen:
                          !e.target.checked && !contactFormSubmitted,
                        internationalContactCreatedDialogOpen:
                          !e.target.checked && contactFormSubmitted,
                      });
                    }}
                    label="I am currently a US resident"
                    style={{ width: '100%' }}
                  />
                  {isUSResident ? null : (
                    <FormHelperText
                      style={{
                        textAlign: 'left',
                        marginLeft: 0,
                        width: '100%',
                        color: theme.palette.error.main,
                      }}
                    >
                      We are currently only accepting investments from US
                      residents.
                    </FormHelperText>
                  )}
                </div>
                <div style={{ marginLeft: fullScreen ? 0 : '.6rem' }}>
                  <CustomCheckbox
                    required
                    checked={isUSCitizen}
                    name="isUSCitizen"
                    onChange={(e) => {
                      this.setState({
                        isUSCitizen: e.target.checked,
                        contactFormOpen:
                          !e.target.checked && !contactFormSubmitted,
                        internationalContactCreatedDialogOpen:
                          !e.target.checked && contactFormSubmitted,
                      });
                    }}
                    label="I have a valid US Social Security Number"
                  />
                  {isUSCitizen ? null : (
                    <FormHelperText
                      style={{
                        textAlign: 'left',
                        marginLeft: 0,
                        width: '100%',
                        color: theme.palette.error.main,
                      }}
                    >
                      We are currently only accepting investments from US
                      citizens or people with valid US Social Security Numbers.
                    </FormHelperText>
                  )}
                </div>
                <div style={{ marginLeft: fullScreen ? 0 : '.6rem' }}>
                  <CustomCheckbox
                    required
                    checked={is18Yrs}
                    name="is18Yrs"
                    onChange={(e) =>
                      this.setState({ is18Yrs: e.target.checked })
                    }
                    label="I am at least 18 years old"
                  />
                  {is18Yrs ? null : (
                    <FormHelperText
                      style={{
                        textAlign: 'left',
                        marginLeft: 0,
                        width: '100%',
                        color: theme.palette.error.main,
                      }}
                    >
                      You must be at least 18 years old to invest at this time.
                    </FormHelperText>
                  )}
                </div>
              </Grid>
            </Grid>
          </Grid>
          {renderDialog()}
          {renderInternationalContactCreatedDialog()}
        </>
      );
    };

    const getFundingSrc = () => (
      <Grid
        container
        style={{
          paddingTop:
            fullScreen &&
            (showMoneyShow || showGenericPromo || showFoundersCardPromo)
              ? '2rem'
              : '0',
          height:
            fullScreen &&
            (showMoneyShow || showGenericPromo || showFoundersCardPromo)
              ? '8rem'
              : '80%',
        }}
        justifyContent="center"
      >
        <Grid item style={{ maxWidth: maxFormWidth }}>
          <Grid item>
            <Hidden smDown>
              <Typography
                style={{ textAlign: 'left', visibility: 'hidden' }}
                // color="primary"
                variant="h6"
              >
                Funding
              </Typography>
            </Hidden>
            <Typography
              style={{
                textAlign: fullScreen ? 'center' : 'left',
                width: '100%',
                minHeight: fullScreen ? '1.5em' : '2.5em',
              }}
              color="primary"
              variant={fullScreen ? 'h5' : 'h4'}
            >
              <b>How do you plan to fund your account?</b>
            </Typography>
          </Grid>
          <Grid item className={classes.form}>
            <Grid container justifyContent="center">
              <Grid item>
                <CardSelector
                  primaryText="Bank Account"
                  secondaryText="Checking"
                  secondaryTextSmall
                  onClick={() =>
                    this.setState({ intendedFundingSrcType: 'bank' })
                  }
                  selected={intendedFundingSrcType === 'bank'}
                  icon={<BankIcon />}
                />
              </Grid>
              <Grid item>
                <CardSelector
                  primaryText="IRA"
                  secondaryText="Tax-Deferred"
                  secondaryTextSmall
                  onClick={() =>
                    this.setState({ intendedFundingSrcType: 'IRA' })
                  }
                  selected={intendedFundingSrcType === 'IRA'}
                  icon={
                    <i className={classNames('fa-solid', 'fa-piggy-bank')} />
                  }
                />
              </Grid>
            </Grid>
            {referrerId ? (
              <Grid item style={{ paddingTop: '.5rem' }}>
                <ReferralProgramAlert
                  referrerId={referrerId}
                  fullScreen={fullScreen}
                  parent={this}
                />
              </Grid>
            ) : null}
          </Grid>
        </Grid>
      </Grid>
    );
    const getNamePage = () => (
      <Grid container justifyContent="center">
        <Grid item style={{ maxWidth: maxFormWidth }}>
          <Typography
            style={{ visibility: 'hidden', center: 'left' }}
            // color="primary"
            variant="h6"
          >
            {type && `${type[0].toUpperCase()}${type.substring(1)}`}
          </Typography>
          <Typography
            style={{
              textAlign: fullScreen ? 'center' : 'left',
              width: '100%',
              minHeight: '2.5em',
            }}
            color="primary"
            variant="h4"
          >
            <b>Great! Let&apos;s get started.</b>
          </Typography>
          <Grid className={classes.form}>
            <FormControl margin="dense" required fullWidth>
              <TextField
                variant="outlined"
                label={
                  type === 'business'
                    ? 'Your First Name (not your business name)'
                    : 'Your First Name'
                }
                autoComplete="fName"
                required
                disabled={!queryStringBiPass()}
                id="first-name-text-field"
                value={firstName}
                onChange={(e) => this.setState({ firstName: e.target.value })}
                slotProps={{
                  input: {
                    endAdornment: (
                      <InputAdornment position="end">
                        <UserIcon color="primary" />
                      </InputAdornment>
                    ),
                  },
                  inputLabel: {
                    shrink: Boolean(firstName), // Force label to shrink if there's a value
                  },
                }}
              />
            </FormControl>
            <FormControl margin="dense" required fullWidth>
              <TextField
                disabled={!queryStringBiPass()}
                variant="outlined"
                label={
                  type === 'business'
                    ? 'Your Last Name (not your business name)'
                    : 'Your Last Name'
                }
                value={lastName}
                required
                autoComplete="lName"
                onChange={(e) => this.setState({ lastName: e.target.value })}
                slotProps={{
                  input: {
                    endAdornment: (
                      <InputAdornment position="end">
                        <UserIcon color="primary" />
                      </InputAdornment>
                    ),
                  },
                  inputLabel: {
                    shrink: Boolean(lastName), // Force label to shrink if there's a value
                  },
                }}
              />
            </FormControl>
            <FormControl margin="dense" required fullWidth>
              <TextField
                required
                className={
                  fullScreen ? classes.formControlMobile : classes.formControl
                }
                name="primaryPhone"
                label="Phone Number"
                type="tel"
                autoComplete="tel"
                error={
                  String(primaryPhone).length === 10 &&
                  !validator.isMobilePhone(primaryPhone, 'en-US')
                }
                variant="outlined"
                onChange={(e) =>
                  this.setState({ primaryPhone: e.target.value })
                }
                value={primaryPhone || ''}
                fullWidth
                slotProps={{
                  input: {
                    endAdornment: (
                      <InputAdornment position="end">
                        <PhoneIcon color="primary" />
                      </InputAdornment>
                    ),
                    inputComponent: PhoneFormat,
                  },
                  inputLabel: {
                    shrink: Boolean(primaryPhone), // Force label to shrink if there's a value
                  },
                }}
                InputLabelProps={{
                  shrink: Boolean(primaryPhone),
                }}
              />
            </FormControl>
          </Grid>
        </Grid>
      </Grid>
    );
    const getReviewPage = () => (
      <Grid container justifyContent="center">
        <Grid item style={{ maxWidth: maxFormWidth }}>
          <Typography
            style={{ visibility: 'hidden', center: 'left' }}
            // color="primary"
            variant="h6"
          >
            {type && `${type[0].toUpperCase()}${type.substring(1)}`}
          </Typography>
          <Typography
            style={{
              textAlign: fullScreen ? 'center' : 'left',
              width: '100%',
              minHeight: '2.5em',
            }}
            color="primary"
            variant="h4"
          >
            <b>Let&apos;s create your account!</b>
          </Typography>
          <Grid className={classes.form}>
            <FormControl margin="dense" required fullWidth>
              <TextField
                id="email-text-field"
                variant="outlined"
                label="Email"
                required
                error={email.length > 4 && !validator.isEmail(email)}
                disabled={!queryStringBiPass()}
                type="email"
                value={email}
                autoComplete="email"
                onChange={(e) => this.setState({ email: e.target.value })}
                slotProps={{
                  input: {
                    endAdornment: (
                      <InputAdornment position="end">
                        <EmailIcon color="primary" />
                      </InputAdornment>
                    ),
                  },
                  inputLabel: {
                    shrink: Boolean(email), // Force label to shrink if there's a value
                  },
                }}
              />
            </FormControl>
            <FormControl margin="dense" required fullWidth>
              <TextField
                variant="outlined"
                label="Create a Password"
                required
                id="password-text-field"
                disabled={!queryStringBiPass()}
                type={hidePassword ? 'password' : 'text'}
                value={password}
                onChange={(e) => {
                  const passResults = owasp.test(e.target.value);
                  this.setState({
                    errors: passResults.errors,
                    password: e.target.value,
                  });
                }}
                slotProps={{
                  input: {
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          tabIndex={-1}
                          color="primary"
                          style={{ marginRight: '-8px' }}
                          aria-label="toggle password visibility"
                          onClick={() => {
                            this.setState({
                              hidePassword: !hidePassword,
                            });
                          }}
                          onMouseDown={(event) => event.preventDefault()}
                          size="medium"
                        >
                          {hidePassword ? <EyeSlashIcon /> : <EyeIcon />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  },
                  inputLabel: {
                    shrink: Boolean(password), // Force label to shrink if there's a value
                  },
                }}
              />
            </FormControl>
            <Collapse in={!!(password && password.length > 5)}>
              {errors.slice(0, 2).map((error) => (
                <Typography
                  variant="body2"
                  gutterBottom
                  color="error"
                  key={error}
                  style={{ fontSize: fullScreen ? '.7rem' : null }}
                >
                  {error}
                </Typography>
              ))}
            </Collapse>
            <Collapse
              in={
                !password ||
                password.length <= 5 ||
                !errors ||
                errors.length === 0
              }
            >
              <CustomCheckbox
                disabled={!queryStringBiPass()}
                iconLeft
                noOutline
                style={{
                  // marginLeft: fullScreen ? 0 : '.6rem',
                  // marginRight: 0,
                  textAlign: 'left',
                  width: '100%',
                }}
                checked={readTerms}
                onChange={() => {
                  this.setState({ readTerms: !readTerms });
                }}
                label={
                  <div style={{ lineHeight: '1rem' }}>
                    <Typography
                      variant="caption"
                      style={{
                        width: '100%',
                        lineHeight: '1rem',
                        textAlign: 'left',
                        color: `inherit`,
                      }}
                    >
                      I agree to your
                      <a
                        target="_blank"
                        rel="noopener noreferrer"
                        href={constants.termsOfServiceUrl}
                      >
                        {' '}
                        Terms of Service
                      </a>{' '}
                      and{' '}
                      <a
                        target="_blank"
                        rel="noopener noreferrer"
                        href={constants.privacyPolicyUrl}
                      >
                        Privacy Policy
                      </a>{' '}
                      as well as your partner{' '}
                      <a
                        target="_blank"
                        rel="noopener noreferrer"
                        href={constants.dwollaTermsOfServiceUrl}
                      >
                        Dwolla&apos;s Terms of Service
                      </a>{' '}
                      and{' '}
                      <a
                        target="_blank"
                        rel="noopener noreferrer"
                        href={constants.dwollaPrivacyPolicyUrl}
                      >
                        Privacy Policy
                      </a>
                      .
                    </Typography>
                  </div>
                }
              />
            </Collapse>
          </Grid>
        </Grid>
      </Grid>
    );
    return (
      <>
        <FadeInWrapper>
          <Grid
            container
            justifyContent="center"
            alignContent="center"
            alignItems="center"
            style={{
              minHeight: fullScreen ? `${window.innerHeight}px` : '100vh',
              maxWidth: '100vw',
            }}
          >
            <Grid
              style={{ height: '100%' }}
              item
              container
              justifyContent="center"
              alignItems="center"
              xl={6}
              lg={8}
              md={10}
              xs={12}
            >
              <Grid item md={8} xs={12} style={{ maxWidth: '3200px' }}>
                <Grid
                  component={fullScreen ? Grid : Paper}
                  className={classes.paper}
                  style={{
                    padding: fullScreen ? theme.spacing(2) : null,
                  }}
                  variant="outlined"
                >
                  <Grid
                    item
                    style={{
                      textAlign: 'center',
                      maxWidth: '100%',
                      height: '100%',
                      marginTop:
                        fullScreen &&
                        (showMoneyShow ||
                          showGenericPromo ||
                          showFoundersCardPromo ||
                          showACEPromo ||
                          showWealthFactoryPromo ||
                          showWelcomePromo)
                          ? '72px'
                          : 0,
                    }}
                    container
                  >
                    <Grid
                      container
                      style={{ height: '100%' }}
                      direction="column"
                      justifyContent={fullScreen ? 'space-between' : null}
                    >
                      <Grid item xs={12} style={{ maxWidth: '100%' }}>
                        <Hidden mdDown>
                          <Collapse
                            in={
                              !(
                                showMoneyShow ||
                                showGenericPromo ||
                                showFoundersCardPromo ||
                                showACEPromo ||
                                showWealthFactoryPromo ||
                                showWelcomePromo
                              )
                            }
                          >
                            <FaviconLogo
                              style={{
                                width: '56px',
                                height: 'auto',
                                margin: '3em 0 1em',
                              }}
                            />
                          </Collapse>
                        </Hidden>
                        <Collapse in={showMoneyShow}>
                          {renderMoneyShow()}
                        </Collapse>
                        <Collapse in={showGenericPromo}>
                          {renderGenericPromo()}
                        </Collapse>
                        <Collapse in={showFoundersCardPromo}>
                          {renderFoundersCardPromo()}
                        </Collapse>
                        <Collapse in={showACEPromo}>
                          {renderACEPromo()}
                        </Collapse>
                        <Collapse in={showWealthFactoryPromo}>
                          {renderWealthFactoryPromo()}
                        </Collapse>
                        <Collapse in={showWelcomePromo}>
                          {renderWelcomePromo()}
                        </Collapse>

                        <SwipeableViews
                          slideStyle={{
                            height: fullScreen ? '24rem' : '25rem',
                            // padding: '0 2px',
                          }}
                          disabled
                          index={step}
                        >
                          {getCustomerType()}
                          {getCitizenStatus()}
                          {process.env.MILLENNIUM_FEATURE_ON === 'true' &&
                          type === 'personal'
                            ? getFundingSrc()
                            : []}
                          {getNamePage()}
                          {getReviewPage()}
                        </SwipeableViews>
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        style={{
                          background: theme.palette.white.main,
                          marginTop: fullScreen ? 0 : '1rem',
                          marginBottom: fullScreen ? '0' : '0',
                        }}
                        className={fullScreen ? classes.fixedFooter : null}
                      >
                        <Grid
                          container
                          justifyContent="center"
                          style={{ marginTop: '1rem' }}
                        >
                          {step !== 0 ? (
                            <CustomButton
                              size="large"
                              pill
                              disabled={step === 0}
                              onClick={() => {
                                this.setState({ step: step - 1 });
                              }}
                            >
                              Back
                            </CustomButton>
                          ) : null}
                          {step !==
                          (process.env.MILLENNIUM_FEATURE_ON === 'true' &&
                          type === 'personal'
                            ? 4
                            : 3) ? (
                            <CustomButton
                              disabled={isContinueDisabled()}
                              variant="contained"
                              color="green"
                              size="large"
                              pill
                              onClick={() => {
                                if (step === 1) {
                                  // TODO: add hubSpot contact here
                                }
                                if (step === 2) {
                                  document
                                    .getElementById('first-name-text-field')
                                    .focus();
                                }
                                if (step === 3) {
                                  document
                                    .getElementById('email-text-field')
                                    .focus();
                                }
                                this.setState({ step: step + 1 });
                              }}
                              endIcon={<ArrowForward />}
                            >
                              Continue
                            </CustomButton>
                          ) : (
                            <CustomButton
                              disabled={
                                !firstName ||
                                !lastName ||
                                !validator.isEmail(email) ||
                                !password ||
                                owasp.test(password).errors.length > 0 ||
                                !readTerms
                              }
                              pill
                              loading={!!loading}
                              onClick={this.onSubmit}
                              className="tmt-get-started-create-account-btn"
                              variant="contained"
                              color="green"
                              size="large"
                              endIcon={<ArrowForward />}
                            >
                              Create Account
                            </CustomButton>
                          )}
                        </Grid>
                        <Grid container justifyContent="center">
                          <Grid item xs={12}>
                            <Divider
                              style={{
                                marginTop: '1rem',
                                marginBottom: '1rem',
                              }}
                            />
                          </Grid>
                          <Grid item xs={12}>
                            <Grid>
                              <Typography
                                component="div"
                                variant={fullScreen ? 'body2' : 'body1'}
                                style={{
                                  marginTop: fullScreen ? null : '.5rem',
                                  marginBottom: fullScreen ? '1rem' : '0',
                                }}
                              >
                                <Grid
                                  container
                                  alignItems="center"
                                  justifyContent="center"
                                >
                                  <Grid item>Already have an account?</Grid>{' '}
                                  <Auth0LoginButton />
                                </Grid>
                              </Typography>
                            </Grid>
                          </Grid>
                        </Grid>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
              <Hidden lgDown>
                <Grid style={{ padding: '1.5rem' }} item md={4} xs={12}>
                  <Typography
                    variant="body1"
                    color="primary"
                    style={{ fontWeight: 'bold' }}
                  >
                    See How It Works
                  </Typography>
                  <Grid
                    style={{
                      borderRadius: theme.shape.borderRadius,
                      // border: 'navy solid 1px',
                      // TODO: raise shadow
                      overflow: 'hidden',
                      padding: 0,
                      minHeight: '120px',
                      margin: '.5rem 0',
                    }}
                    item
                    onClick={() => {
                      trackBehavioralEvent({
                        GADataLayer: {
                          event: 'openHowItWorksVideo-getStartedPage',
                        },
                        hubSpotDataLayer: {
                          name: 'pe20071979_how_it_works_video',
                          properties: {
                            // give each property a value so we know what they mean
                          },
                        },
                      });
                      this.setState({ videoDialogOpen: true });
                    }}
                    component={Button}
                  >
                    <div
                      style={{
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                      }}
                    >
                      <PlayVideoIcon
                        style={{
                          color: theme.palette.text.white,
                        }}
                        sx={{ width: '4rem', height: '4rem' }}
                      />
                    </div>
                    <Image
                      alt="See How It Works Video"
                      style={{
                        objectFit: 'cover',
                        width: '100%',
                        // maxHeight: '285px'
                      }}
                      quality="auto"
                      title="See How It Works Video"
                      width="500"
                      crop="scale"
                      cloud_name={constants.cloud_name}
                      publicId="/energea/signup_form" // eslint-disable-line camelcase
                      format="WebP"
                    />
                  </Grid>
                  <Typography variant="body2">
                    We connect investors to premium renewable energy projects in
                    order to accelerate renewable energy development worldwide.
                  </Typography>
                </Grid>
              </Hidden>
            </Grid>
          </Grid>
        </FadeInWrapper>
        <Dialog
          BackdropProps={{ classes: { root: classes.backdropRoot } }}
          onClose={() => this.setState({ videoDialogOpen: false })}
          maxWidth="lg"
          fullScreen={fullScreen}
          open={videoDialogOpen}
          keepMounted
        >
          <DialogContent style={{ padding: 0, background: '#000' }}>
            <IconButton
              style={{ position: 'absolute', right: 0, zIndex: 1400 }}
              onClick={() => this.setState({ videoDialogOpen: false })}
              size="large"
            >
              <Close
                style={{
                  color: theme.palette.text.white,
                  width: '2rem',
                  height: '2rem',
                  filter: 'drop-shadow( 0 0 2px rgba(0,0,0,0.6))',
                }}
              />
            </IconButton>
            <Grid container style={{ height: '100%' }} alignItems="center">
              <Grid item xs={12}>
                <Video
                  style={{ width: '100%' }}
                  cloudName={constants.cloud_name}
                  publicId={`energea/global-videos/${constants.howItWorksVideoPublicId}`}
                  poster={{
                    publicId: `energea/global-videos/${constants.howItWorksVideoPublicId}`,
                    // eslint-disable-next-line camelcase
                    transformation: { start_offset: 0 },
                    format: 'WebP',
                    quality: 'auto',
                  }}
                  autoPlay={videoDialogOpen}
                  muted={false}
                  sourceTypes={['mp4']}
                  crop="scale"
                  width={
                    fullScreen
                      ? constants.mobileVideoWidth
                      : constants.desktopVideoWidth
                  }
                  controls
                  // autoPlayMode="always"
                  quality="auto"
                />
              </Grid>
            </Grid>
          </DialogContent>
        </Dialog>
      </>
    );
  }
}

GetStarted.propTypes = {
  classes: PropTypes.object.isRequired,
};

export default withRouter(
  withSnackbar(
    withAuth(
      withIpQualityScript(
        graphql(logToSlackMutation, {
          name: 'logToSlack',
        })(
          graphql(claimPromoCodeMutation, {
            name: 'claimPromoCode',
          })(
            graphql(getAuth0TokensMutation, {
              name: 'getAuth0Tokens',
            })(
              graphql(signupMutation)(
                graphql(createInternationalHubSpotContactMutation, {
                  name: 'createInternationalHubSpotContact',
                })(
                  withStyles(styles, { withTheme: true })(
                    withFullScreen(GetStarted)
                  )
                )
              )
            )
          )
        )
      )
    )
  )
);
